exec ${PAGER:-/usr/bin/less} "$0" || exit 1
Executing tests from //backend/test/api_integration/collection/fintech/payment/old_invoice/bulk_payment:bulk_payment_test
-----------------------------------------------------------------------------
Use env: testing
2025/05/22 07:08:09 Datadog Tracer v1.69.1 INFO: DATADOG TRACER CONFIGURATION {"date":"2025-05-22T07:08:09Z","os_name":"darwin","os_version":"14.7.4","version":"v1.69.1","lang":"Go","lang_version":"go1.23.6 X:nocoverageredesign","env":"","service":"bulk_payment","agent_url":"http://localhost:8126/v0.4/traces","agent_error":"","debug":false,"analytics_enabled":false,"sample_rate":"NaN","sample_rate_limit":"disabled","trace_sampling_rules":null,"span_sampling_rules":null,"sampling_rules_error":"","service_mappings":null,"tags":{"runtime-id":"58a48646-f189-491d-8421-77e4f1d495b4"},"runtime_metrics_enabled":true,"health_metrics_enabled":true,"profiler_code_hotspots_enabled":true,"profiler_endpoints_enabled":true,"dd_version":"","architecture":"arm64","global_service":"bulk_payment","lambda_mode":"true","appsec":false,"agent_features":{"DropP0s":false,"Stats":false,"StatsdPort":0},"integrations":{"AWS SDK":{"instrumented":false,"available":false,"available_version":""},"AWS SDK v2":{"instrumented":false,"available":false,"available_version":""},"Bun":{"instrumented":false,"available":false,"available_version":""},"BuntDB":{"instrumented":false,"available":false,"available_version":""},"Cassandra":{"instrumented":false,"available":false,"available_version":""},"Consul":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v3":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v5":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v6":{"instrumented":false,"available":false,"available_version":""},"FastHTTP":{"instrumented":false,"available":false,"available_version":""},"Fiber":{"instrumented":false,"available":false,"available_version":""},"Gin":{"instrumented":false,"available":false,"available_version":""},"Goji":{"instrumented":false,"available":false,"available_version":""},"Google API":{"instrumented":false,"available":false,"available_version":""},"Gorilla Mux":{"instrumented":false,"available":false,"available_version":""},"Gorm":{"instrumented":false,"available":false,"available_version":""},"Gorm (gopkg)":{"instrumented":false,"available":false,"available_version":""},"Gorm v1":{"instrumented":false,"available":false,"available_version":""},"GraphQL":{"instrumented":false,"available":false,"available_version":""},"HTTP":{"instrumented":true,"available":false,"available_version":""},"HTTP Router":{"instrumented":false,"available":false,"available_version":""},"HTTP Treemux":{"instrumented":false,"available":false,"available_version":""},"IBM sarama":{"instrumented":true,"available":false,"available_version":""},"Kafka (confluent)":{"instrumented":false,"available":false,"available_version":""},"Kafka (confluent) v2":{"instrumented":false,"available":false,"available_version":""},"Kafka v0":{"instrumented":false,"available":false,"available_version":""},"Kubernetes":{"instrumented":false,"available":false,"available_version":""},"LevelDB":{"instrumented":false,"available":false,"available_version":""},"Logrus":{"instrumented":false,"available":false,"available_version":""},"Memcache":{"instrumented":false,"available":false,"available_version":""},"MongoDB":{"instrumented":false,"available":false,"available_version":""},"MongoDB (mgo)":{"instrumented":false,"available":false,"available_version":""},"Negroni":{"instrumented":false,"available":false,"available_version":""},"Pub/Sub":{"instrumented":false,"available":false,"available_version":""},"Redigo":{"instrumented":false,"available":false,"available_version":""},"Redigo (dep)":{"instrumented":false,"available":false,"available_version":""},"Redis":{"instrumented":false,"available":false,"available_version":""},"Redis v7":{"instrumented":false,"available":false,"available_version":""},"Redis v8":{"instrumented":false,"available":false,"available_version":""},"Redis v9":{"instrumented":false,"available":false,"available_version":""},"SQL":{"instrumented":false,"available":false,"available_version":""},"SQLx":{"instrumented":false,"available":false,"available_version":""},"Shopify sarama":{"instrumented":false,"available":false,"available_version":""},"Twirp":{"instrumented":false,"available":false,"available_version":""},"Vault":{"instrumented":false,"available":false,"available_version":""},"chi":{"instrumented":false,"available":false,"available_version":""},"chi v5":{"instrumented":false,"available":false,"available_version":""},"echo":{"instrumented":false,"available":false,"available_version":""},"echo v4":{"instrumented":false,"available":false,"available_version":""},"gRPC":{"instrumented":true,"available":false,"available_version":""},"go-pg v10":{"instrumented":false,"available":false,"available_version":""},"go-restful":{"instrumented":false,"available":false,"available_version":""},"go-restful v3":{"instrumented":false,"available":false,"available_version":""},"gqlgen":{"instrumented":false,"available":false,"available_version":""},"log/slog":{"instrumented":false,"available":false,"available_version":""},"miekg/dns":{"instrumented":false,"available":false,"available_version":""}},"partial_flush_enabled":false,"partial_flush_min_spans":1000,"orchestrion":{"enabled":false},"feature_flags":[],"propagation_style_inject":"datadog,tracecontext","propagation_style_extract":"datadog,tracecontext"}
Use env: testing
2025/05/22 07:08:09 Datadog Tracer v1.69.1 INFO: DATADOG TRACER CONFIGURATION {"date":"2025-05-22T07:08:09Z","os_name":"darwin","os_version":"14.7.4","version":"v1.69.1","lang":"Go","lang_version":"go1.23.6 X:nocoverageredesign","env":"","service":"bulk_payment","agent_url":"http://localhost:8126/v0.4/traces","agent_error":"","debug":false,"analytics_enabled":false,"sample_rate":"NaN","sample_rate_limit":"disabled","trace_sampling_rules":null,"span_sampling_rules":null,"sampling_rules_error":"","service_mappings":null,"tags":{"runtime-id":"b3e57c23-484a-40f8-833f-406afbac82c2"},"runtime_metrics_enabled":true,"health_metrics_enabled":true,"profiler_code_hotspots_enabled":true,"profiler_endpoints_enabled":true,"dd_version":"","architecture":"arm64","global_service":"bulk_payment","lambda_mode":"true","appsec":false,"agent_features":{"DropP0s":false,"Stats":false,"StatsdPort":0},"integrations":{"AWS SDK":{"instrumented":false,"available":false,"available_version":""},"AWS SDK v2":{"instrumented":false,"available":false,"available_version":""},"Bun":{"instrumented":false,"available":false,"available_version":""},"BuntDB":{"instrumented":false,"available":false,"available_version":""},"Cassandra":{"instrumented":false,"available":false,"available_version":""},"Consul":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v3":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v5":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v6":{"instrumented":false,"available":false,"available_version":""},"FastHTTP":{"instrumented":false,"available":false,"available_version":""},"Fiber":{"instrumented":false,"available":false,"available_version":""},"Gin":{"instrumented":false,"available":false,"available_version":""},"Goji":{"instrumented":false,"available":false,"available_version":""},"Google API":{"instrumented":false,"available":false,"available_version":""},"Gorilla Mux":{"instrumented":false,"available":false,"available_version":""},"Gorm":{"instrumented":false,"available":false,"available_version":""},"Gorm (gopkg)":{"instrumented":false,"available":false,"available_version":""},"Gorm v1":{"instrumented":false,"available":false,"available_version":""},"GraphQL":{"instrumented":false,"available":false,"available_version":""},"HTTP":{"instrumented":true,"available":false,"available_version":""},"HTTP Router":{"instrumented":false,"available":false,"available_version":""},"HTTP Treemux":{"instrumented":false,"available":false,"available_version":""},"IBM sarama":{"instrumented":true,"available":false,"available_version":""},"Kafka (confluent)":{"instrumented":false,"available":false,"available_version":""},"Kafka (confluent) v2":{"instrumented":false,"available":false,"available_version":""},"Kafka v0":{"instrumented":false,"available":false,"available_version":""},"Kubernetes":{"instrumented":false,"available":false,"available_version":""},"LevelDB":{"instrumented":false,"available":false,"available_version":""},"Logrus":{"instrumented":false,"available":false,"available_version":""},"Memcache":{"instrumented":false,"available":false,"available_version":""},"MongoDB":{"instrumented":false,"available":false,"available_version":""},"MongoDB (mgo)":{"instrumented":false,"available":false,"available_version":""},"Negroni":{"instrumented":false,"available":false,"available_version":""},"Pub/Sub":{"instrumented":false,"available":false,"available_version":""},"Redigo":{"instrumented":false,"available":false,"available_version":""},"Redigo (dep)":{"instrumented":false,"available":false,"available_version":""},"Redis":{"instrumented":false,"available":false,"available_version":""},"Redis v7":{"instrumented":false,"available":false,"available_version":""},"Redis v8":{"instrumented":false,"available":false,"available_version":""},"Redis v9":{"instrumented":false,"available":false,"available_version":""},"SQL":{"instrumented":false,"available":false,"available_version":""},"SQLx":{"instrumented":false,"available":false,"available_version":""},"Shopify sarama":{"instrumented":false,"available":false,"available_version":""},"Twirp":{"instrumented":false,"available":false,"available_version":""},"Vault":{"instrumented":false,"available":false,"available_version":""},"chi":{"instrumented":false,"available":false,"available_version":""},"chi v5":{"instrumented":false,"available":false,"available_version":""},"echo":{"instrumented":false,"available":false,"available_version":""},"echo v4":{"instrumented":false,"available":false,"available_version":""},"gRPC":{"instrumented":true,"available":false,"available_version":""},"go-pg v10":{"instrumented":false,"available":false,"available_version":""},"go-restful":{"instrumented":false,"available":false,"available_version":""},"go-restful v3":{"instrumented":false,"available":false,"available_version":""},"gqlgen":{"instrumented":false,"available":false,"available_version":""},"log/slog":{"instrumented":false,"available":false,"available_version":""},"miekg/dns":{"instrumented":false,"available":false,"available_version":""}},"partial_flush_enabled":false,"partial_flush_min_spans":1000,"orchestrion":{"enabled":false},"feature_flags":[],"propagation_style_inject":"datadog,tracecontext","propagation_style_extract":"datadog,tracecontext"}
=== RUN   TestBulkPaymentSuite
{"level":"info","ts":"2025-05-22T07:08:09.485Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:10.554Z","caller":"api/response.go:49","msg":"reqeust id: c2c08c37-1664-962c-ad94-4f3213c7a93e, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:11.552Z","caller":"api/response.go:49","msg":"reqeust id: 39d521c2-f617-931f-8a75-d8f9e1accbb3, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:12.361Z","caller":"api/response.go:49","msg":"reqeust id: 6ea0b5d1-3d57-92cc-99c7-29dc0e1096a3, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:12.732Z","caller":"api/response.go:49","msg":"reqeust id: a8a2ed37-ff6e-906c-80cd-60364eb49cd6, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListBoradingMultiUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:13.021Z","caller":"api/response.go:49","msg":"reqeust id: 5dfad968-52a9-9a25-8fcc-41bb592ebe95, POST https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:13.518Z","caller":"api/response.go:49","msg":"reqeust id: 87d18984-3b7a-91d7-92c9-c18692885e3f, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:13.518Z","caller":"bulk_payment/bulk_payment_test.go:403","msg":"-------- appt id: ********","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:14.020Z","caller":"api/response.go:49","msg":"reqeust id: 52ebfbdf-59d7-98b8-ac55-909aa82d6b09, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:14.020Z","caller":"bulk_payment/bulk_payment_test.go:403","msg":"-------- appt id: 21062392","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:14.368Z","caller":"api/response.go:49","msg":"reqeust id: 7bd73318-c325-9544-8aec-46ae276ea395, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:14.716Z","caller":"api/response.go:49","msg":"reqeust id: 8a3d05a8-f96b-9cc0-8a96-6bdb303c4538, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListBoradingSingleUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:15.005Z","caller":"api/response.go:49","msg":"reqeust id: 7cccd7b8-f3cb-99d8-bf8a-0cdce80bcbeb, POST https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:15.532Z","caller":"api/response.go:49","msg":"reqeust id: 00290078-a9f4-94e5-a99c-d80549dd028e, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:15.532Z","caller":"bulk_payment/bulk_payment_test.go:349","msg":"-------- appt id: 21062393","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:15.891Z","caller":"api/response.go:49","msg":"reqeust id: 1ab6d3aa-ee43-95d3-aaf8-1e9465c9c505, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListDaycareMultiUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:16.186Z","caller":"api/response.go:49","msg":"reqeust id: f2812561-e9ba-9072-9dee-29c9cd8e5a6e, POST https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:16.686Z","caller":"api/response.go:49","msg":"reqeust id: 1ba1a901-8c70-95d2-bf53-9891c3607381, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:16.686Z","caller":"bulk_payment/bulk_payment_test.go:294","msg":"-------- appt id: 21062394","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:17.169Z","caller":"api/response.go:49","msg":"reqeust id: fa8e3bab-425a-9517-8af8-61888c61f128, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:17.169Z","caller":"bulk_payment/bulk_payment_test.go:294","msg":"-------- appt id: 21062395","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:17.512Z","caller":"api/response.go:49","msg":"reqeust id: bf232519-25f6-9bdc-b022-4f0be51fcd57, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:17.853Z","caller":"api/response.go:49","msg":"reqeust id: adcda093-9b9d-98da-b186-604a61e9a2c0, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListDaycareSingleUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:18.211Z","caller":"api/response.go:49","msg":"reqeust id: 800a65ab-882a-93a0-bda9-5a1ecf62d5cd, POST https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:18.702Z","caller":"api/response.go:49","msg":"reqeust id: 43b9ab5b-7a3c-9eb8-9feb-1cd7eddb5514, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:18.702Z","caller":"bulk_payment/bulk_payment_test.go:240","msg":"-------- appt id: 21062396","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:19.050Z","caller":"api/response.go:49","msg":"reqeust id: 638a68a1-891d-94aa-b306-e42fe8b58e1f, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListEvaluationMultiUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:19.521Z","caller":"api/response.go:49","msg":"reqeust id: cd17fa93-b180-907c-8149-79a8507e1273, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:19.522Z","caller":"bulk_payment/bulk_payment_test.go:503","msg":"-------- appt id: 21062397","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:19.993Z","caller":"api/response.go:49","msg":"reqeust id: 4ed53aa6-353d-9bb5-bbfc-ce309848bc8d, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:19.993Z","caller":"bulk_payment/bulk_payment_test.go:503","msg":"-------- appt id: 21062398","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:20.335Z","caller":"api/response.go:49","msg":"reqeust id: 83f1f0a3-81ec-98d4-a35e-1b606f3f4790, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:20.687Z","caller":"api/response.go:49","msg":"reqeust id: 8a89f773-390e-93fb-9f00-e53af555d278, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListEvaluationSingleUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:21.167Z","caller":"api/response.go:49","msg":"reqeust id: ad523b62-70f0-9c3d-85ab-b45e95c33d4b, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:21.167Z","caller":"bulk_payment/bulk_payment_test.go:453","msg":"-------- appt id: 21062399","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:21.516Z","caller":"api/response.go:49","msg":"reqeust id: 89f4613d-de24-9934-9d27-1ef0e9c9b13c, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListGroomingMultiUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:21.799Z","caller":"api/response.go:49","msg":"reqeust id: 7b21bf88-319d-9424-bb7d-958bf3a467eb, POST https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:22.293Z","caller":"api/response.go:49","msg":"reqeust id: a8fb8caf-5ead-9d12-a115-8e0670c90565, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:22.293Z","caller":"bulk_payment/bulk_payment_test.go:188","msg":"-------- index: 0, appt id: 21062400","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:22.757Z","caller":"api/response.go:49","msg":"reqeust id: 1064428c-bb7a-95d1-9767-60d500695eb8, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:22.757Z","caller":"bulk_payment/bulk_payment_test.go:188","msg":"-------- index: 1, appt id: 21062401","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:23.106Z","caller":"api/response.go:49","msg":"reqeust id: 7a0b52fe-37d6-9704-b5cb-26ed3daab155, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:23.450Z","caller":"api/response.go:49","msg":"reqeust id: e0b56036-61ff-9669-9889-fad67f2341cb, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListGroomingSingleUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:23.734Z","caller":"api/response.go:49","msg":"reqeust id: b3b2e77c-e818-98dd-9c4c-759c44ac32b8, POST https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:24.224Z","caller":"api/response.go:49","msg":"reqeust id: cb541332-0bde-9409-8816-e33749b8b15f, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:24.224Z","caller":"bulk_payment/bulk_payment_test.go:133","msg":"-------- appt id: 21062402","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:24.574Z","caller":"api/response.go:49","msg":"reqeust id: fdd777d1-794b-94c2-82b9-89aea65bbed9, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
=== RUN   TestBulkPaymentSuite/TestListNotUnpaidOrder
{"level":"info","ts":"2025-05-22T07:08:24.918Z","caller":"api/response.go:49","msg":"reqeust id: 2493acaf-6635-9f38-9121-37341241905f, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:25.286Z","caller":"api/response.go:49","msg":"reqeust id: 313b148e-cecb-9f31-adb6-aeb267491da4, DELETE https://go.t2.moego.dev/api/customer","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:25.563Z","caller":"api/response.go:49","msg":"reqeust id: e952abea-76eb-9643-9f36-2b24b157705c, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":7201431051372682556}
{"level":"info","ts":"2025-05-22T07:08:25.563Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":7201431051372682556}
--- PASS: TestBulkPaymentSuite (16.08s)
    --- PASS: TestBulkPaymentSuite/TestListBoradingMultiUnpaidOrder (1.98s)
    --- PASS: TestBulkPaymentSuite/TestListBoradingSingleUnpaidOrder (1.17s)
    --- PASS: TestBulkPaymentSuite/TestListDaycareMultiUnpaidOrder (1.96s)
    --- PASS: TestBulkPaymentSuite/TestListDaycareSingleUnpaidOrder (1.20s)
    --- PASS: TestBulkPaymentSuite/TestListEvaluationMultiUnpaidOrder (1.64s)
    --- PASS: TestBulkPaymentSuite/TestListEvaluationSingleUnpaidOrder (0.83s)
    --- PASS: TestBulkPaymentSuite/TestListGroomingMultiUnpaidOrder (1.93s)
    --- PASS: TestBulkPaymentSuite/TestListGroomingSingleUnpaidOrder (1.12s)
    --- PASS: TestBulkPaymentSuite/TestListNotUnpaidOrder (0.34s)
PASS
