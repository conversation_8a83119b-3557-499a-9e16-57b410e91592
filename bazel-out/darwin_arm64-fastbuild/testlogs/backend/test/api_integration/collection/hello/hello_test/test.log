exec ${PAGER:-/usr/bin/less} "$0" || exit 1
Executing tests from //backend/test/api_integration/collection/hello:hello_test
-----------------------------------------------------------------------------
Use env: testing
2025/05/22 07:08:55 Datadog Tracer v1.69.1 INFO: DATADOG TRACER CONFIGURATION {"date":"2025-05-22T07:08:55Z","os_name":"darwin","os_version":"14.7.4","version":"v1.69.1","lang":"Go","lang_version":"go1.23.6 X:nocoverageredesign","env":"","service":"hello","agent_url":"http://localhost:8126/v0.4/traces","agent_error":"","debug":false,"analytics_enabled":false,"sample_rate":"NaN","sample_rate_limit":"disabled","trace_sampling_rules":null,"span_sampling_rules":null,"sampling_rules_error":"","service_mappings":null,"tags":{"runtime-id":"466b3298-1083-4d14-809e-71f1a8afaac2"},"runtime_metrics_enabled":true,"health_metrics_enabled":true,"profiler_code_hotspots_enabled":true,"profiler_endpoints_enabled":true,"dd_version":"","architecture":"arm64","global_service":"hello","lambda_mode":"true","appsec":false,"agent_features":{"DropP0s":false,"Stats":false,"StatsdPort":0},"integrations":{"AWS SDK":{"instrumented":false,"available":false,"available_version":""},"AWS SDK v2":{"instrumented":false,"available":false,"available_version":""},"Bun":{"instrumented":false,"available":false,"available_version":""},"BuntDB":{"instrumented":false,"available":false,"available_version":""},"Cassandra":{"instrumented":false,"available":false,"available_version":""},"Consul":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v3":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v5":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v6":{"instrumented":false,"available":false,"available_version":""},"FastHTTP":{"instrumented":false,"available":false,"available_version":""},"Fiber":{"instrumented":false,"available":false,"available_version":""},"Gin":{"instrumented":false,"available":false,"available_version":""},"Goji":{"instrumented":false,"available":false,"available_version":""},"Google API":{"instrumented":false,"available":false,"available_version":""},"Gorilla Mux":{"instrumented":false,"available":false,"available_version":""},"Gorm":{"instrumented":false,"available":false,"available_version":""},"Gorm (gopkg)":{"instrumented":false,"available":false,"available_version":""},"Gorm v1":{"instrumented":false,"available":false,"available_version":""},"GraphQL":{"instrumented":false,"available":false,"available_version":""},"HTTP":{"instrumented":true,"available":false,"available_version":""},"HTTP Router":{"instrumented":false,"available":false,"available_version":""},"HTTP Treemux":{"instrumented":false,"available":false,"available_version":""},"IBM sarama":{"instrumented":true,"available":false,"available_version":""},"Kafka (confluent)":{"instrumented":false,"available":false,"available_version":""},"Kafka (confluent) v2":{"instrumented":false,"available":false,"available_version":""},"Kafka v0":{"instrumented":false,"available":false,"available_version":""},"Kubernetes":{"instrumented":false,"available":false,"available_version":""},"LevelDB":{"instrumented":false,"available":false,"available_version":""},"Logrus":{"instrumented":false,"available":false,"available_version":""},"Memcache":{"instrumented":false,"available":false,"available_version":""},"MongoDB":{"instrumented":false,"available":false,"available_version":""},"MongoDB (mgo)":{"instrumented":false,"available":false,"available_version":""},"Negroni":{"instrumented":false,"available":false,"available_version":""},"Pub/Sub":{"instrumented":false,"available":false,"available_version":""},"Redigo":{"instrumented":false,"available":false,"available_version":""},"Redigo (dep)":{"instrumented":false,"available":false,"available_version":""},"Redis":{"instrumented":false,"available":false,"available_version":""},"Redis v7":{"instrumented":false,"available":false,"available_version":""},"Redis v8":{"instrumented":false,"available":false,"available_version":""},"Redis v9":{"instrumented":false,"available":false,"available_version":""},"SQL":{"instrumented":false,"available":false,"available_version":""},"SQLx":{"instrumented":false,"available":false,"available_version":""},"Shopify sarama":{"instrumented":false,"available":false,"available_version":""},"Twirp":{"instrumented":false,"available":false,"available_version":""},"Vault":{"instrumented":false,"available":false,"available_version":""},"chi":{"instrumented":false,"available":false,"available_version":""},"chi v5":{"instrumented":false,"available":false,"available_version":""},"echo":{"instrumented":false,"available":false,"available_version":""},"echo v4":{"instrumented":false,"available":false,"available_version":""},"gRPC":{"instrumented":true,"available":false,"available_version":""},"go-pg v10":{"instrumented":false,"available":false,"available_version":""},"go-restful":{"instrumented":false,"available":false,"available_version":""},"go-restful v3":{"instrumented":false,"available":false,"available_version":""},"gqlgen":{"instrumented":false,"available":false,"available_version":""},"log/slog":{"instrumented":false,"available":false,"available_version":""},"miekg/dns":{"instrumented":false,"available":false,"available_version":""}},"partial_flush_enabled":false,"partial_flush_min_spans":1000,"orchestrion":{"enabled":false},"feature_flags":[],"propagation_style_inject":"datadog,tracecontext","propagation_style_extract":"datadog,tracecontext"}
Use env: testing
2025/05/22 07:08:55 Datadog Tracer v1.69.1 INFO: DATADOG TRACER CONFIGURATION {"date":"2025-05-22T07:08:55Z","os_name":"darwin","os_version":"14.7.4","version":"v1.69.1","lang":"Go","lang_version":"go1.23.6 X:nocoverageredesign","env":"","service":"hello","agent_url":"http://localhost:8126/v0.4/traces","agent_error":"","debug":false,"analytics_enabled":false,"sample_rate":"NaN","sample_rate_limit":"disabled","trace_sampling_rules":null,"span_sampling_rules":null,"sampling_rules_error":"","service_mappings":null,"tags":{"runtime-id":"a3d97556-88ec-4b1d-a2ed-5712f64ec1a9"},"runtime_metrics_enabled":true,"health_metrics_enabled":true,"profiler_code_hotspots_enabled":true,"profiler_endpoints_enabled":true,"dd_version":"","architecture":"arm64","global_service":"hello","lambda_mode":"true","appsec":false,"agent_features":{"DropP0s":false,"Stats":false,"StatsdPort":0},"integrations":{"AWS SDK":{"instrumented":false,"available":false,"available_version":""},"AWS SDK v2":{"instrumented":false,"available":false,"available_version":""},"Bun":{"instrumented":false,"available":false,"available_version":""},"BuntDB":{"instrumented":false,"available":false,"available_version":""},"Cassandra":{"instrumented":false,"available":false,"available_version":""},"Consul":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v3":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v5":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v6":{"instrumented":false,"available":false,"available_version":""},"FastHTTP":{"instrumented":false,"available":false,"available_version":""},"Fiber":{"instrumented":false,"available":false,"available_version":""},"Gin":{"instrumented":false,"available":false,"available_version":""},"Goji":{"instrumented":false,"available":false,"available_version":""},"Google API":{"instrumented":false,"available":false,"available_version":""},"Gorilla Mux":{"instrumented":false,"available":false,"available_version":""},"Gorm":{"instrumented":false,"available":false,"available_version":""},"Gorm (gopkg)":{"instrumented":false,"available":false,"available_version":""},"Gorm v1":{"instrumented":false,"available":false,"available_version":""},"GraphQL":{"instrumented":false,"available":false,"available_version":""},"HTTP":{"instrumented":true,"available":false,"available_version":""},"HTTP Router":{"instrumented":false,"available":false,"available_version":""},"HTTP Treemux":{"instrumented":false,"available":false,"available_version":""},"IBM sarama":{"instrumented":true,"available":false,"available_version":""},"Kafka (confluent)":{"instrumented":false,"available":false,"available_version":""},"Kafka (confluent) v2":{"instrumented":false,"available":false,"available_version":""},"Kafka v0":{"instrumented":false,"available":false,"available_version":""},"Kubernetes":{"instrumented":false,"available":false,"available_version":""},"LevelDB":{"instrumented":false,"available":false,"available_version":""},"Logrus":{"instrumented":false,"available":false,"available_version":""},"Memcache":{"instrumented":false,"available":false,"available_version":""},"MongoDB":{"instrumented":false,"available":false,"available_version":""},"MongoDB (mgo)":{"instrumented":false,"available":false,"available_version":""},"Negroni":{"instrumented":false,"available":false,"available_version":""},"Pub/Sub":{"instrumented":false,"available":false,"available_version":""},"Redigo":{"instrumented":false,"available":false,"available_version":""},"Redigo (dep)":{"instrumented":false,"available":false,"available_version":""},"Redis":{"instrumented":false,"available":false,"available_version":""},"Redis v7":{"instrumented":false,"available":false,"available_version":""},"Redis v8":{"instrumented":false,"available":false,"available_version":""},"Redis v9":{"instrumented":false,"available":false,"available_version":""},"SQL":{"instrumented":false,"available":false,"available_version":""},"SQLx":{"instrumented":false,"available":false,"available_version":""},"Shopify sarama":{"instrumented":false,"available":false,"available_version":""},"Twirp":{"instrumented":false,"available":false,"available_version":""},"Vault":{"instrumented":false,"available":false,"available_version":""},"chi":{"instrumented":false,"available":false,"available_version":""},"chi v5":{"instrumented":false,"available":false,"available_version":""},"echo":{"instrumented":false,"available":false,"available_version":""},"echo v4":{"instrumented":false,"available":false,"available_version":""},"gRPC":{"instrumented":true,"available":false,"available_version":""},"go-pg v10":{"instrumented":false,"available":false,"available_version":""},"go-restful":{"instrumented":false,"available":false,"available_version":""},"go-restful v3":{"instrumented":false,"available":false,"available_version":""},"gqlgen":{"instrumented":false,"available":false,"available_version":""},"log/slog":{"instrumented":false,"available":false,"available_version":""},"miekg/dns":{"instrumented":false,"available":false,"available_version":""}},"partial_flush_enabled":false,"partial_flush_min_spans":1000,"orchestrion":{"enabled":false},"feature_flags":[],"propagation_style_inject":"datadog,tracecontext","propagation_style_extract":"datadog,tracecontext"}
=== RUN   TestBffDemoTestSuite
{"level":"info","ts":"2025-05-22T07:08:55.684Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":489030315478846497}
{"level":"info","ts":"2025-05-22T07:08:56.676Z","caller":"api/response.go:49","msg":"reqeust id: 300b406e-7d06-9f76-a323-b9500e95cdf0, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":489030315478846497}
{"level":"info","ts":"2025-05-22T07:08:57.743Z","caller":"api/response.go:49","msg":"reqeust id: 404215d9-4ff5-9b99-b787-3f0beaebcce4, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":489030315478846497}
{"level":"info","ts":"2025-05-22T07:08:58.570Z","caller":"api/response.go:49","msg":"reqeust id: 644b2118-a74d-9888-9cbd-64dae7467524, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":489030315478846497}
=== RUN   TestBffDemoTestSuite/TestCreateBook
{"level":"info","ts":"2025-05-22T07:08:58.829Z","caller":"api/response.go:49","msg":"reqeust id: 913b2826-3be7-9acd-a82d-06bffda8c438, POST https://go.t2.moego.dev/moego.bff/book/createBook","trace_id":489030315478846497}
=== RUN   TestBffDemoTestSuite/TestOrder
{"level":"info","ts":"2025-05-22T07:08:59.338Z","caller":"api/response.go:49","msg":"reqeust id: dd94494c-0e33-9a16-91f7-841f0528edb4, GET https://go.t2.moego.dev/moego.bff/test-order/getTestOrder?id=*********&businessId=108108","trace_id":489030315478846497}
{"level":"info","ts":"2025-05-22T07:08:59.588Z","caller":"api/response.go:49","msg":"reqeust id: 45f6f0c2-9ef4-9961-807a-ed72853a81be, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":489030315478846497}
{"level":"info","ts":"2025-05-22T07:08:59.588Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":489030315478846497}
--- PASS: TestBffDemoTestSuite (3.90s)
    --- PASS: TestBffDemoTestSuite/TestCreateBook (0.26s)
    --- PASS: TestBffDemoTestSuite/TestOrder (0.51s)
=== RUN   TestHelloNewbiesTestSuite
{"level":"info","ts":"2025-05-22T07:08:59.589Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":1060100768464162361}
=== RUN   TestHelloNewbiesTestSuite/TestHelloArk
{"level":"info","ts":"2025-05-22T07:08:59.589Z","caller":"hello/hello_newbies_test.go:51","msg":"Start to run test HelloNewbiesSuite.TestHelloArk","trace_id":1060100768464162361}
{"traces": [[{"trace_id":"6c9627b1e838c21","span_id":"6c9627b1e838c21","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"language":"go","runtime-id":"a3d97556-88ec-4b1d-a2ed-5712f64ec1a9","_dd.p.dm":"-1","_dd.p.tid":"682ecd8700000000"},"metrics":{"_dd.agent_psr":1,"process_id":15462,"_dd.profiling.enabled":0,"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1},"start":1747897735684788000,"duration":**********,"service":"hello"},{"trace_id":"6c9627b1e838c21","span_id":"e0d9dbc8332c1ac","parent_id":"6c9627b1e838c21","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"a3d97556-88ec-4b1d-a2ed-5712f64ec1a9","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount"},"metrics":{"_sampling_priority_v1":1,"process_id":15462},"start":1747897735685570000,"duration":*********,"service":"hello"},{"trace_id":"6c9627b1e838c21","span_id":"267c4b0aa7ec7f47","parent_id":"6c9627b1e838c21","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"a3d97556-88ec-4b1d-a2ed-5712f64ec1a9","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15462},"start":1747897736676765000,"duration":**********,"service":"hello"},{"trace_id":"6c9627b1e838c21","span_id":"1f480e6ccb032d8c","parent_id":"6c9627b1e838c21","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","runtime-id":"a3d97556-88ec-4b1d-a2ed-5712f64ec1a9","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/business/account/v2/info"},"metrics":{"_sampling_priority_v1":1,"process_id":15462},"start":1747897738244604000,"duration":*********,"service":"hello"},{"trace_id":"6c9627b1e838c21","span_id":"3168f74107268199","parent_id":"6c9627b1e838c21","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/moego.bff/book/createBook","runtime-id":"a3d97556-88ec-4b1d-a2ed-5712f64ec1a9","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15462},"start":1747897738572337000,"duration":*********,"service":"hello"},{"trace_id":"6c9627b1e838c21","span_id":"3cfddd072c3a67c2","parent_id":"6c9627b1e838c21","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"GET","runtime-id":"a3d97556-88ec-4b1d-a2ed-5712f64ec1a9","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/moego.bff/test-order/getTestOrder?id=*********\u0026businessId=108108","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15462},"start":1747897738830215000,"duration":*********,"service":"hello"},{"trace_id":"6c9627b1e838c21","span_id":"47d312e37c1c0e06","parent_id":"6c9627b1e838c21","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"a3d97556-88ec-4b1d-a2ed-5712f64ec1a9","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","component":"net/http"},"metrics":{"process_id":15462,"_sampling_priority_v1":1},"start":1747897739341424000,"duration":*********,"service":"hello"}]]}
{"level":"info","ts":"2025-05-22T07:08:59.836Z","caller":"api/response.go:49","msg":"reqeust id: aa43405a-dc6b-9a8a-a65e-851415745ad4, POST https://go.t2.moego.dev/moego.api.todo.v1.TodoService/HelloArk","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:08:59.837Z","caller":"hello/hello_newbies_test.go:57","msg":"Finish running test HelloNewbiesSuite.TestHelloArk","trace_id":1060100768464162361}
=== RUN   TestHelloNewbiesTestSuite/TestHelloJett
{"level":"info","ts":"2025-05-22T07:08:59.837Z","caller":"hello/hello_newbies_test.go:51","msg":"Start to run test HelloNewbiesSuite.TestHelloJett","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:09:00.084Z","caller":"api/response.go:49","msg":"reqeust id: 9f6cb260-f8fc-98d8-84df-2614682f9ce4, POST https://go.t2.moego.dev/moego.api.todo.v1.TodoService/HelloJett","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:09:00.084Z","caller":"hello/hello_newbies_test.go:57","msg":"Finish running test HelloNewbiesSuite.TestHelloJett","trace_id":1060100768464162361}
=== RUN   TestHelloNewbiesTestSuite/TestHelloWell
{"level":"info","ts":"2025-05-22T07:09:00.084Z","caller":"hello/hello_newbies_test.go:51","msg":"Start to run test HelloNewbiesSuite.TestHelloWell","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:09:00.751Z","caller":"api/response.go:49","msg":"reqeust id: b63a3add-4b36-97b7-b4a8-bb4ad716a912, GET https://go.t2.moego.dev/api/business/debugging/well","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:09:00.751Z","caller":"hello/hello_newbies_test.go:57","msg":"Finish running test HelloNewbiesSuite.TestHelloWell","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:09:00.751Z","caller":"hello/hello_newbies_test.go:42","msg":"Hello ark true","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:09:00.751Z","caller":"hello/hello_newbies_test.go:42","msg":"Hello jett true","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:09:00.751Z","caller":"hello/hello_newbies_test.go:42","msg":"Hello well true","trace_id":1060100768464162361}
{"level":"info","ts":"2025-05-22T07:09:00.751Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":1060100768464162361}
--- PASS: TestHelloNewbiesTestSuite (1.16s)
    --- PASS: TestHelloNewbiesTestSuite/TestHelloArk (0.25s)
    --- PASS: TestHelloNewbiesTestSuite/TestHelloJett (0.25s)
    --- PASS: TestHelloNewbiesTestSuite/TestHelloWell (0.67s)
PASS
