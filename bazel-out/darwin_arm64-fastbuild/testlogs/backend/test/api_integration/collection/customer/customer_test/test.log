exec ${PAGER:-/usr/bin/less} "$0" || exit 1
Executing tests from //backend/test/api_integration/collection/customer:customer_test
-----------------------------------------------------------------------------
Use env: testing
2025/05/22 07:06:35 Datadog Tracer v1.69.1 INFO: DATADOG TRACER CONFIGURATION {"date":"2025-05-22T07:06:35Z","os_name":"darwin","os_version":"14.7.4","version":"v1.69.1","lang":"Go","lang_version":"go1.23.6 X:nocoverageredesign","env":"","service":"customer","agent_url":"http://localhost:8126/v0.4/traces","agent_error":"","debug":false,"analytics_enabled":false,"sample_rate":"NaN","sample_rate_limit":"disabled","trace_sampling_rules":null,"span_sampling_rules":null,"sampling_rules_error":"","service_mappings":null,"tags":{"runtime-id":"9ed753ae-43d7-40a7-83c4-fbbf29c7db49"},"runtime_metrics_enabled":true,"health_metrics_enabled":true,"profiler_code_hotspots_enabled":true,"profiler_endpoints_enabled":true,"dd_version":"","architecture":"arm64","global_service":"customer","lambda_mode":"true","appsec":false,"agent_features":{"DropP0s":false,"Stats":false,"StatsdPort":0},"integrations":{"AWS SDK":{"instrumented":false,"available":false,"available_version":""},"AWS SDK v2":{"instrumented":false,"available":false,"available_version":""},"Bun":{"instrumented":false,"available":false,"available_version":""},"BuntDB":{"instrumented":false,"available":false,"available_version":""},"Cassandra":{"instrumented":false,"available":false,"available_version":""},"Consul":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v3":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v5":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v6":{"instrumented":false,"available":false,"available_version":""},"FastHTTP":{"instrumented":false,"available":false,"available_version":""},"Fiber":{"instrumented":false,"available":false,"available_version":""},"Gin":{"instrumented":false,"available":false,"available_version":""},"Goji":{"instrumented":false,"available":false,"available_version":""},"Google API":{"instrumented":false,"available":false,"available_version":""},"Gorilla Mux":{"instrumented":false,"available":false,"available_version":""},"Gorm":{"instrumented":false,"available":false,"available_version":""},"Gorm (gopkg)":{"instrumented":false,"available":false,"available_version":""},"Gorm v1":{"instrumented":false,"available":false,"available_version":""},"GraphQL":{"instrumented":false,"available":false,"available_version":""},"HTTP":{"instrumented":true,"available":false,"available_version":""},"HTTP Router":{"instrumented":false,"available":false,"available_version":""},"HTTP Treemux":{"instrumented":false,"available":false,"available_version":""},"IBM sarama":{"instrumented":true,"available":false,"available_version":""},"Kafka (confluent)":{"instrumented":false,"available":false,"available_version":""},"Kafka (confluent) v2":{"instrumented":false,"available":false,"available_version":""},"Kafka v0":{"instrumented":false,"available":false,"available_version":""},"Kubernetes":{"instrumented":false,"available":false,"available_version":""},"LevelDB":{"instrumented":false,"available":false,"available_version":""},"Logrus":{"instrumented":false,"available":false,"available_version":""},"Memcache":{"instrumented":false,"available":false,"available_version":""},"MongoDB":{"instrumented":false,"available":false,"available_version":""},"MongoDB (mgo)":{"instrumented":false,"available":false,"available_version":""},"Negroni":{"instrumented":false,"available":false,"available_version":""},"Pub/Sub":{"instrumented":false,"available":false,"available_version":""},"Redigo":{"instrumented":false,"available":false,"available_version":""},"Redigo (dep)":{"instrumented":false,"available":false,"available_version":""},"Redis":{"instrumented":false,"available":false,"available_version":""},"Redis v7":{"instrumented":false,"available":false,"available_version":""},"Redis v8":{"instrumented":false,"available":false,"available_version":""},"Redis v9":{"instrumented":false,"available":false,"available_version":""},"SQL":{"instrumented":false,"available":false,"available_version":""},"SQLx":{"instrumented":false,"available":false,"available_version":""},"Shopify sarama":{"instrumented":false,"available":false,"available_version":""},"Twirp":{"instrumented":false,"available":false,"available_version":""},"Vault":{"instrumented":false,"available":false,"available_version":""},"chi":{"instrumented":false,"available":false,"available_version":""},"chi v5":{"instrumented":false,"available":false,"available_version":""},"echo":{"instrumented":false,"available":false,"available_version":""},"echo v4":{"instrumented":false,"available":false,"available_version":""},"gRPC":{"instrumented":true,"available":false,"available_version":""},"go-pg v10":{"instrumented":false,"available":false,"available_version":""},"go-restful":{"instrumented":false,"available":false,"available_version":""},"go-restful v3":{"instrumented":false,"available":false,"available_version":""},"gqlgen":{"instrumented":false,"available":false,"available_version":""},"log/slog":{"instrumented":false,"available":false,"available_version":""},"miekg/dns":{"instrumented":false,"available":false,"available_version":""}},"partial_flush_enabled":false,"partial_flush_min_spans":1000,"orchestrion":{"enabled":false},"feature_flags":[],"propagation_style_inject":"datadog,tracecontext","propagation_style_extract":"datadog,tracecontext"}
Use env: testing
2025/05/22 07:06:35 Datadog Tracer v1.69.1 INFO: DATADOG TRACER CONFIGURATION {"date":"2025-05-22T07:06:35Z","os_name":"darwin","os_version":"14.7.4","version":"v1.69.1","lang":"Go","lang_version":"go1.23.6 X:nocoverageredesign","env":"","service":"customer","agent_url":"http://localhost:8126/v0.4/traces","agent_error":"","debug":false,"analytics_enabled":false,"sample_rate":"NaN","sample_rate_limit":"disabled","trace_sampling_rules":null,"span_sampling_rules":null,"sampling_rules_error":"","service_mappings":null,"tags":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"runtime_metrics_enabled":true,"health_metrics_enabled":true,"profiler_code_hotspots_enabled":true,"profiler_endpoints_enabled":true,"dd_version":"","architecture":"arm64","global_service":"customer","lambda_mode":"true","appsec":false,"agent_features":{"DropP0s":false,"Stats":false,"StatsdPort":0},"integrations":{"AWS SDK":{"instrumented":false,"available":false,"available_version":""},"AWS SDK v2":{"instrumented":false,"available":false,"available_version":""},"Bun":{"instrumented":false,"available":false,"available_version":""},"BuntDB":{"instrumented":false,"available":false,"available_version":""},"Cassandra":{"instrumented":false,"available":false,"available_version":""},"Consul":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v3":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v5":{"instrumented":false,"available":false,"available_version":""},"Elasticsearch v6":{"instrumented":false,"available":false,"available_version":""},"FastHTTP":{"instrumented":false,"available":false,"available_version":""},"Fiber":{"instrumented":false,"available":false,"available_version":""},"Gin":{"instrumented":false,"available":false,"available_version":""},"Goji":{"instrumented":false,"available":false,"available_version":""},"Google API":{"instrumented":false,"available":false,"available_version":""},"Gorilla Mux":{"instrumented":false,"available":false,"available_version":""},"Gorm":{"instrumented":false,"available":false,"available_version":""},"Gorm (gopkg)":{"instrumented":false,"available":false,"available_version":""},"Gorm v1":{"instrumented":false,"available":false,"available_version":""},"GraphQL":{"instrumented":false,"available":false,"available_version":""},"HTTP":{"instrumented":true,"available":false,"available_version":""},"HTTP Router":{"instrumented":false,"available":false,"available_version":""},"HTTP Treemux":{"instrumented":false,"available":false,"available_version":""},"IBM sarama":{"instrumented":true,"available":false,"available_version":""},"Kafka (confluent)":{"instrumented":false,"available":false,"available_version":""},"Kafka (confluent) v2":{"instrumented":false,"available":false,"available_version":""},"Kafka v0":{"instrumented":false,"available":false,"available_version":""},"Kubernetes":{"instrumented":false,"available":false,"available_version":""},"LevelDB":{"instrumented":false,"available":false,"available_version":""},"Logrus":{"instrumented":false,"available":false,"available_version":""},"Memcache":{"instrumented":false,"available":false,"available_version":""},"MongoDB":{"instrumented":false,"available":false,"available_version":""},"MongoDB (mgo)":{"instrumented":false,"available":false,"available_version":""},"Negroni":{"instrumented":false,"available":false,"available_version":""},"Pub/Sub":{"instrumented":false,"available":false,"available_version":""},"Redigo":{"instrumented":false,"available":false,"available_version":""},"Redigo (dep)":{"instrumented":false,"available":false,"available_version":""},"Redis":{"instrumented":false,"available":false,"available_version":""},"Redis v7":{"instrumented":false,"available":false,"available_version":""},"Redis v8":{"instrumented":false,"available":false,"available_version":""},"Redis v9":{"instrumented":false,"available":false,"available_version":""},"SQL":{"instrumented":false,"available":false,"available_version":""},"SQLx":{"instrumented":false,"available":false,"available_version":""},"Shopify sarama":{"instrumented":false,"available":false,"available_version":""},"Twirp":{"instrumented":false,"available":false,"available_version":""},"Vault":{"instrumented":false,"available":false,"available_version":""},"chi":{"instrumented":false,"available":false,"available_version":""},"chi v5":{"instrumented":false,"available":false,"available_version":""},"echo":{"instrumented":false,"available":false,"available_version":""},"echo v4":{"instrumented":false,"available":false,"available_version":""},"gRPC":{"instrumented":true,"available":false,"available_version":""},"go-pg v10":{"instrumented":false,"available":false,"available_version":""},"go-restful":{"instrumented":false,"available":false,"available_version":""},"go-restful v3":{"instrumented":false,"available":false,"available_version":""},"gqlgen":{"instrumented":false,"available":false,"available_version":""},"log/slog":{"instrumented":false,"available":false,"available_version":""},"miekg/dns":{"instrumented":false,"available":false,"available_version":""}},"partial_flush_enabled":false,"partial_flush_min_spans":1000,"orchestrion":{"enabled":false},"feature_flags":[],"propagation_style_inject":"datadog,tracecontext","propagation_style_extract":"datadog,tracecontext"}
=== RUN   TestBookNewTestSuite
{"level":"info","ts":"2025-05-22T07:06:35.680Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":1009163935432193861}
{"level":"info","ts":"2025-05-22T07:06:37.235Z","caller":"api/response.go:49","msg":"reqeust id: 65ce9cb3-a08b-9ad4-95b6-6fa508ef185a, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":1009163935432193861}
{"level":"info","ts":"2025-05-22T07:06:38.964Z","caller":"api/response.go:49","msg":"reqeust id: bdd70553-4e67-959c-8521-dcb1f8eadd93, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":1009163935432193861}
{"level":"info","ts":"2025-05-22T07:06:39.787Z","caller":"api/response.go:49","msg":"reqeust id: 7dd3c54a-e069-9dba-bd6a-8e5aa39c2ef6, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":1009163935432193861}
{"level":"info","ts":"2025-05-22T07:06:40.218Z","caller":"api/response.go:49","msg":"reqeust id: f0e1b114-bfc8-9115-abbd-5182968049c8, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":1009163935432193861}
=== RUN   TestBookNewTestSuite/TestBookNewDetail
{"level":"info","ts":"2025-05-22T07:06:40.692Z","caller":"api/response.go:49","msg":"reqeust id: 547ae364-95db-95c2-adc2-60f4600755b1, GET https://go.t2.moego.dev/api/customer/booknew/detail?customerId=********","trace_id":1009163935432193861}
=== RUN   TestBookNewTestSuite/TestSearchWithEmail
{"level":"info","ts":"2025-05-22T07:06:41.139Z","caller":"api/response.go:49","msg":"reqeust id: 61a7fc0b-369d-909c-9bc7-6a9fdb43201a, GET https://go.t2.moego.dev/api/customer/booknew/search?keyword=tnv_5tgx","trace_id":1009163935432193861}
=== RUN   TestBookNewTestSuite/TestSearchWithName
{"level":"info","ts":"2025-05-22T07:06:41.585Z","caller":"api/response.go:49","msg":"reqeust id: 4d00d43c-c884-980c-82dd-f9603d29f897, GET https://go.t2.moego.dev/api/customer/booknew/search?keyword=Joshua","trace_id":1009163935432193861}
=== RUN   TestBookNewTestSuite/TestSearchWithPetBreed
{"level":"info","ts":"2025-05-22T07:06:42.018Z","caller":"api/response.go:49","msg":"reqeust id: a08663fc-791f-92c8-aeb0-dccd7fd81226, GET https://go.t2.moego.dev/api/customer/booknew/search?keyword=Affenpinscher","trace_id":1009163935432193861}
=== RUN   TestBookNewTestSuite/TestSearchWithPetName
{"level":"info","ts":"2025-05-22T07:06:42.454Z","caller":"api/response.go:49","msg":"reqeust id: 999a9f4b-4b08-90f8-97fc-edc8c4bf3db7, GET https://go.t2.moego.dev/api/customer/booknew/search?keyword=Max","trace_id":1009163935432193861}
=== RUN   TestBookNewTestSuite/TestSearchWithPhoneNumber
{"level":"info","ts":"2025-05-22T07:06:42.891Z","caller":"api/response.go:49","msg":"reqeust id: 79e963be-627d-9b3a-8292-c4db86e6f0a0, GET https://go.t2.moego.dev/api/customer/booknew/search?keyword=**********","trace_id":1009163935432193861}
{"level":"info","ts":"2025-05-22T07:06:43.272Z","caller":"api/response.go:49","msg":"reqeust id: 16cb268e-7aa2-98dd-a898-d4fb633f1e52, DELETE https://go.t2.moego.dev/api/customer","trace_id":1009163935432193861}
{"level":"info","ts":"2025-05-22T07:06:43.530Z","caller":"api/response.go:49","msg":"reqeust id: 9498095f-6976-9a84-95c2-dc4bdc8117c8, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":1009163935432193861}
{"level":"info","ts":"2025-05-22T07:06:43.530Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":1009163935432193861}
--- PASS: TestBookNewTestSuite (7.85s)
    --- PASS: TestBookNewTestSuite/TestBookNewDetail (0.47s)
    --- PASS: TestBookNewTestSuite/TestSearchWithEmail (0.45s)
    --- PASS: TestBookNewTestSuite/TestSearchWithName (0.45s)
    --- PASS: TestBookNewTestSuite/TestSearchWithPetBreed (0.43s)
    --- PASS: TestBookNewTestSuite/TestSearchWithPetName (0.44s)
    --- PASS: TestBookNewTestSuite/TestSearchWithPhoneNumber (0.44s)
=== RUN   TestCustomerBasicTestSuite
{"level":"info","ts":"2025-05-22T07:06:43.530Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":7928140418418486717}
{"traces": [[{"trace_id":"e014540a3923745","span_id":"e014540a3923745","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"language":"go","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.dm":"-1","_dd.p.tid":"682eccfb00000000"},"metrics":{"process_id":15164,"_dd.profiling.enabled":0,"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1,"_dd.agent_psr":1},"start":1747897595680072000,"duration":**********,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"5d631cb7a487d42b","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897595680536000,"duration":**********,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"4f73ee526af00549","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897597235567000,"duration":**********,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"2e906e151ddfaafd","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897599466674000,"duration":*********,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"70029139b1fbe30e","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/withPet","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897599789574000,"duration":*********,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"7d16794c620d5fc2","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/booknew/detail?customerId=********","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897600221105000,"duration":470710000,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"28224c557efcbfe1","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/booknew/search?keyword=tnv_5tgx","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897600693120000,"duration":446094000,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"44fb74e81442cad2","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/booknew/search?keyword=Joshua","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897601139497000,"duration":446024000,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"690a2fdceb714000","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/booknew/search?keyword=Affenpinscher","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897601585754000,"duration":432292000,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"1fe186eb865803f7","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/booknew/search?keyword=Max","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897602018428000,"duration":435895000,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"2a3a889fc53f06e9","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/booknew/search?keyword=**********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897602454787000,"duration":436248000,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"337313073430c01e","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897602891937000,"duration":*********,"service":"customer"},{"trace_id":"e014540a3923745","span_id":"20146b7f9778440f","parent_id":"e014540a3923745","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897603273251000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:06:43.775Z","caller":"api/response.go:49","msg":"reqeust id: 78b66264-ec61-9563-b42a-8f294b373e8f, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":7928140418418486717}
{"level":"info","ts":"2025-05-22T07:06:44.105Z","caller":"api/response.go:49","msg":"reqeust id: 7dfa5fdc-dde5-9ae2-a5e5-f722d1725cd3, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":7928140418418486717}
{"level":"info","ts":"2025-05-22T07:06:44.939Z","caller":"api/response.go:49","msg":"reqeust id: b45d85cb-30f1-9e14-895f-7d6bd9d99b21, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":7928140418418486717}
{"level":"info","ts":"2025-05-22T07:06:45.340Z","caller":"api/response.go:49","msg":"reqeust id: 79669efd-a4ab-9d89-8431-4ecbd6bea0d4, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":7928140418418486717}
=== RUN   TestCustomerBasicTestSuite/TestCustomerBasic
{"level":"info","ts":"2025-05-22T07:06:45.686Z","caller":"api/response.go:49","msg":"reqeust id: 675b6fef-e36e-9152-8023-79263f840f3f, GET https://go.t2.moego.dev/api/customer/basic?customerId=********","trace_id":7928140418418486717}
=== RUN   TestCustomerBasicTestSuite/TestUpdateCustomerBasic
{"level":"info","ts":"2025-05-22T07:06:45.977Z","caller":"api/response.go:49","msg":"reqeust id: fffcb308-e1b5-9723-9d5f-8c9e293af36c, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":7928140418418486717}
{"level":"info","ts":"2025-05-22T07:06:46.322Z","caller":"api/response.go:49","msg":"reqeust id: af315200-864c-9e80-849c-8ba6c523edc2, GET https://go.t2.moego.dev/api/customer/basic?customerId=********","trace_id":7928140418418486717}
{"level":"info","ts":"2025-05-22T07:06:46.701Z","caller":"api/response.go:49","msg":"reqeust id: ccabda84-8061-9738-b0a4-cda0885da5c1, DELETE https://go.t2.moego.dev/api/customer","trace_id":7928140418418486717}
{"level":"info","ts":"2025-05-22T07:06:46.952Z","caller":"api/response.go:49","msg":"reqeust id: 06e0cba0-25a3-9c57-9e43-2092f06a2db2, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":7928140418418486717}
{"level":"info","ts":"2025-05-22T07:06:46.953Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":7928140418418486717}
--- PASS: TestCustomerBasicTestSuite (3.42s)
    --- PASS: TestCustomerBasicTestSuite/TestCustomerBasic (0.35s)
    --- PASS: TestCustomerBasicTestSuite/TestUpdateCustomerBasic (0.64s)
=== RUN   TestClientDetailTestSuite
{"level":"info","ts":"2025-05-22T07:06:46.954Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:47.202Z","caller":"api/response.go:49","msg":"reqeust id: 5f279adb-97ff-9449-b03a-9a71648daf3f, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:47.525Z","caller":"api/response.go:49","msg":"reqeust id: dd8f91b0-a6aa-988b-9a5f-7af9cbad6046, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":1321469109653243178}
{"traces": [[{"trace_id":"6e0669b499402dbd","span_id":"6e0669b499402dbd","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"_dd.p.dm":"-1","_dd.p.tid":"682ecd0300000000","language":"go","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_dd.agent_psr":1,"process_id":15164,"_dd.profiling.enabled":0,"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1},"start":1747897603530613000,"duration":**********,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"74d1f2571245fba0","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897603530728000,"duration":*********,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"445d21529d8ec8ee","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897603776045000,"duration":*********,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"707cbb55240c3779","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897604606654000,"duration":*********,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"7f48438f5ff18626","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/customer/withPet","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897604940669000,"duration":*********,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"456f0bc7d5dd4f1a","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/basic?customerId=********"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897605340765000,"duration":345816000,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"843424114947bb6","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/detail","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"PUT"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897605687162000,"duration":289821000,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"1cc542d0aec33234","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/basic?customerId=********"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897605977617000,"duration":344664000,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"4f601ee8b6ca7f21","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897606322814000,"duration":*********,"service":"customer"},{"trace_id":"6e0669b499402dbd","span_id":"beb0e23d961171","parent_id":"6e0669b499402dbd","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897606701643000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:06:48.343Z","caller":"api/response.go:49","msg":"reqeust id: a29c3beb-99e2-9ed3-b3b9-698db8156f2d, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:48.758Z","caller":"api/response.go:49","msg":"reqeust id: ed987afb-dff5-9fe5-9bf1-6aa37a0ba140, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:49.005Z","caller":"api/response.go:49","msg":"reqeust id: d25fe748-4326-91dc-bc4a-8607249c90f8, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:49.282Z","caller":"api/response.go:49","msg":"reqeust id: bafad237-b0dd-944a-983b-b4b05af19aba, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestAddAddress
{"level":"info","ts":"2025-05-22T07:06:49.543Z","caller":"api/response.go:49","msg":"reqeust id: 3e8c694c-1629-9674-b25e-53a1cd060875, POST https://go.t2.moego.dev/api/customer/address","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:49.966Z","caller":"api/response.go:49","msg":"reqeust id: b32cf7bf-920a-9449-a998-4f05b8c56657, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:50.224Z","caller":"api/response.go:49","msg":"reqeust id: 7cdac290-eb8d-9ce1-8f69-e89ea89e5951, DELETE https://go.t2.moego.dev/api/customer/address","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:50.642Z","caller":"api/response.go:49","msg":"reqeust id: d661bf4d-d580-9824-bb8e-9b92097b7ade, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestAddClientNote
{"level":"info","ts":"2025-05-22T07:06:50.895Z","caller":"api/response.go:49","msg":"reqeust id: e420c7f7-76c9-931a-8f77-ee35b5ff2780, POST https://go.t2.moego.dev/api/customer/note","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:51.319Z","caller":"api/response.go:49","msg":"reqeust id: e3d9a876-1a05-945f-9d3b-512049479af0, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:51.574Z","caller":"api/response.go:49","msg":"reqeust id: 489c88de-8575-9798-978e-4fecc7a7a3ad, DELETE https://go.t2.moego.dev/api/customer/note","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:52.004Z","caller":"api/response.go:49","msg":"reqeust id: 72d128e1-db42-9ffc-a684-ee4f3d86ed68, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestAddContact
{"level":"info","ts":"2025-05-22T07:06:52.273Z","caller":"api/response.go:49","msg":"reqeust id: c6c22c57-d60e-991e-8b3e-846aacc40cea, POST https://go.t2.moego.dev/api/customer/contact","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:52.687Z","caller":"api/response.go:49","msg":"reqeust id: cdb3d53c-46fb-980b-85b7-9b9194a281e4, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:52.942Z","caller":"api/response.go:49","msg":"reqeust id: 550a5ec7-e5c7-9cca-8442-4e596ba0f48c, DELETE https://go.t2.moego.dev/api/customer/contact","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:53.360Z","caller":"api/response.go:49","msg":"reqeust id: ac16ada1-6d72-9c63-ad0d-44e18bede50e, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestBlockMessage
{"level":"info","ts":"2025-05-22T07:06:53.625Z","caller":"api/response.go:49","msg":"reqeust id: bd3afb8d-b3ce-9efc-9799-300f15d4d016, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:54.036Z","caller":"api/response.go:49","msg":"reqeust id: c785b184-80e5-9992-99a3-e89468eb4ca0, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:54.303Z","caller":"api/response.go:49","msg":"reqeust id: 271d9338-c4b2-9c16-a268-afe3789a5d0d, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:54.716Z","caller":"api/response.go:49","msg":"reqeust id: c883eda1-432d-9dc1-a54a-299def6f0bd7, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestCustomerDetail
{"level":"info","ts":"2025-05-22T07:06:55.123Z","caller":"api/response.go:49","msg":"reqeust id: f1cd8cf8-1a73-9d05-b56b-c791677d0df8, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestCustomerDetailWithOverview
{"level":"info","ts":"2025-05-22T07:06:55.608Z","caller":"api/response.go:49","msg":"reqeust id: 768338e0-b3ac-959f-ae29-0eceeecba0ac, GET https://go.t2.moego.dev/api/customer/detail/withOverview?customerId=********","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestMarkCustomerAsInactive
{"level":"info","ts":"2025-05-22T07:06:55.869Z","caller":"api/response.go:49","msg":"reqeust id: ea623093-f7be-9bbf-84a9-f5d64401a485, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:56.319Z","caller":"api/response.go:49","msg":"reqeust id: 859b8ad3-193d-994f-8107-43a0e12aef87, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:56.576Z","caller":"api/response.go:49","msg":"reqeust id: 35cae0c7-6405-941d-ae12-e4ea2279de2b, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:56.985Z","caller":"api/response.go:49","msg":"reqeust id: 63b3eab6-99fd-94cc-8a15-3f34f2490380, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestPreferredTip
{"level":"info","ts":"2025-05-22T07:06:57.290Z","caller":"api/response.go:49","msg":"reqeust id: 8e86e04d-745c-9ef6-bfbe-bedae513a196, PUT https://go.t2.moego.dev/api/customer/preferredTip","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:57.539Z","caller":"api/response.go:49","msg":"reqeust id: 09ffd35f-5285-91b1-9403-3686af0c1e11, GET https://go.t2.moego.dev/api/customer/preferredTip?customerId=********","trace_id":1321469109653243178}
=== RUN   TestClientDetailTestSuite/TestUpdateCustomerInfo
{"level":"info","ts":"2025-05-22T07:06:57.835Z","caller":"api/response.go:49","msg":"reqeust id: 5ead0db6-4d0b-9ee3-8167-b7de08bbe68b, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:58.266Z","caller":"api/response.go:49","msg":"reqeust id: aba41653-456e-962d-af5e-0be6738e55ed, GET https://go.t2.moego.dev/api/customer/page/detail?customerId=********","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:58.640Z","caller":"api/response.go:49","msg":"reqeust id: a8cd186d-803f-9fe6-af99-fbdf8048b1ed, DELETE https://go.t2.moego.dev/api/customer","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:58.896Z","caller":"api/response.go:49","msg":"reqeust id: 22d4ac3c-0356-919e-bb61-799ed6a76f76, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":1321469109653243178}
{"level":"info","ts":"2025-05-22T07:06:58.896Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":1321469109653243178}
--- PASS: TestClientDetailTestSuite (11.94s)
    --- PASS: TestClientDetailTestSuite/TestAddAddress (1.36s)
    --- PASS: TestClientDetailTestSuite/TestAddClientNote (1.36s)
    --- PASS: TestClientDetailTestSuite/TestAddContact (1.36s)
    --- PASS: TestClientDetailTestSuite/TestBlockMessage (1.36s)
    --- PASS: TestClientDetailTestSuite/TestCustomerDetail (0.41s)
    --- PASS: TestClientDetailTestSuite/TestCustomerDetailWithOverview (0.49s)
    --- PASS: TestClientDetailTestSuite/TestMarkCustomerAsInactive (1.38s)
    --- PASS: TestClientDetailTestSuite/TestPreferredTip (0.55s)
    --- PASS: TestClientDetailTestSuite/TestUpdateCustomerInfo (0.73s)
=== RUN   TestCustomerFilterViewTestSuite
{"level":"info","ts":"2025-05-22T07:06:58.897Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":2192587887728361290}
{"level":"info","ts":"2025-05-22T07:06:59.152Z","caller":"api/response.go:49","msg":"reqeust id: 4b6e0eb3-ec6b-96b2-b8d2-72e6fa83ec69, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":2192587887728361290}
{"level":"info","ts":"2025-05-22T07:06:59.660Z","caller":"api/response.go:49","msg":"reqeust id: c39994c7-04f5-909b-bcf6-b9460d433027, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":2192587887728361290}
{"traces": [[{"trace_id":"1256cd275e88852a","span_id":"1256cd275e88852a","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"language":"go","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.dm":"-1","_dd.p.tid":"682ecd0600000000"},"metrics":{"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1,"_dd.agent_psr":1,"process_id":15164,"_dd.profiling.enabled":0},"start":1747897606954192000,"duration":***********,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"11a591a86a0d2325","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897606954375000,"duration":*********,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"2f6bc1b4a933b10f","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897607202905000,"duration":*********,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"688e575349f78b6f","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897608026625000,"duration":*********,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"2aee4ab48f1412d8","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/withPet"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897608344107000,"duration":*********,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"61736b281f26eb95","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897608758871000,"duration":246896000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"1c8caa2f24f75776","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/customer/detail","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"PUT"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897609006397000,"duration":276386000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"1d84abac087320a3","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/address","component":"net/http"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897609283081000,"duration":260423000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"605f9aab49ae55fd","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897609543931000,"duration":422891000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"220640609e7ca03f","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/address","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"DELETE"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897609968270000,"duration":256261000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"759897c99417c84c","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897610224776000,"duration":417251000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"19a909ade4803fc1","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/note","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897610643457000,"duration":251850000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"e2f021d99d8d6fc","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897610895746000,"duration":423620000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"58a415a2ddd35523","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer/note","component":"net/http"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897611319890000,"duration":254831000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"7612e37148efe07","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897611574902000,"duration":429894000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"6c6ee41fc99f881","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/contact","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897612005555000,"duration":267809000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"68b64ca657f4c866","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897612273599000,"duration":413679000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"3d6d22a9a85c74ed","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer/contact","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897612687705000,"duration":254281000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"7873a687ab095e10","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897612942219000,"duration":418013000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"5e50c6e936b5cc09","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897613360572000,"duration":264788000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"4299ea3103407617","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897613625539000,"duration":410735000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"4c11141fc8c7f62c","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897614036596000,"duration":266443000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"43c117ed274127a2","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897614303316000,"duration":412682000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"7c6e40046260177b","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897614716645000,"duration":406529000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"7efa8dc235b3bfa0","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/detail/withOverview?customerId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897615124017000,"duration":483976000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"4313271f73b6d404","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897615609315000,"duration":260609000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"e5746d7d61116b5","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897615870075000,"duration":449609000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"413c04add03fcacb","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897616320424000,"duration":256467000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"132493ad461da6de","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897616577119000,"duration":408318000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"1ad26bd61cd716be","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/preferredTip","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897616985958000,"duration":304333000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"195876f111c5890e","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/preferredTip?customerId=********","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897617292204000,"duration":247160000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"154133347e5ae5b6","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897617539618000,"duration":295973000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"f69fd7358cd4492","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/page/detail?customerId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897617835903000,"duration":430545000,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"1016c05e8bb40a27","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897618266985000,"duration":*********,"service":"customer"},{"trace_id":"1256cd275e88852a","span_id":"7a6a336be013b876","parent_id":"1256cd275e88852a","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"platform-tools.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897618640313000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:07:00.485Z","caller":"api/response.go:49","msg":"reqeust id: 6c6636cc-0a63-91ef-93b5-858d6b76a57a, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":2192587887728361290}
{"level":"info","ts":"2025-05-22T07:07:00.754Z","caller":"api/response.go:49","msg":"reqeust id: 3ae61cb7-5320-98c8-802d-f224650e807c, POST https://go.t2.moego.dev/api/customer/filter/view/add","trace_id":2192587887728361290}
=== RUN   TestCustomerFilterViewTestSuite/TestCustomerFilterView
{"level":"info","ts":"2025-05-22T07:07:01.104Z","caller":"api/response.go:49","msg":"reqeust id: 04e8476b-012b-9b5b-a2e0-c9fdad7f99ec, GET https://go.t2.moego.dev/api/customer/filter/view/detail?id=9745","trace_id":2192587887728361290}
=== RUN   TestCustomerFilterViewTestSuite/TestUpdateCustomerViewWithFilter
{"level":"info","ts":"2025-05-22T07:07:01.368Z","caller":"api/response.go:49","msg":"reqeust id: 9b6a4ef7-7440-9b0d-aeb5-62c2b32046da, POST https://go.t2.moego.dev/api/customer/filter/view/update","trace_id":2192587887728361290}
=== RUN   TestCustomerFilterViewTestSuite/TestUpdateCustomerViewWithTitle
{"level":"info","ts":"2025-05-22T07:07:01.634Z","caller":"api/response.go:49","msg":"reqeust id: b2d63b78-d71b-907a-88f1-24dcf0271ef8, POST https://go.t2.moego.dev/api/customer/filter/view/update","trace_id":2192587887728361290}
=== RUN   TestCustomerFilterViewTestSuite/TestViewListContainsNewView
{"level":"info","ts":"2025-05-22T07:07:01.895Z","caller":"api/response.go:49","msg":"reqeust id: ee269ab7-1f52-9fa9-8a99-a46e48fa3d63, GET https://go.t2.moego.dev/api/customer/filter/view/list","trace_id":2192587887728361290}
{"level":"info","ts":"2025-05-22T07:07:02.154Z","caller":"api/response.go:49","msg":"reqeust id: e49e132e-b08c-94bc-b176-6100006677b8, DELETE https://go.t2.moego.dev/api/customer/filter/view/delete?id=9745","trace_id":2192587887728361290}
{"level":"info","ts":"2025-05-22T07:07:02.413Z","caller":"api/response.go:49","msg":"reqeust id: 03189b05-a818-9569-a381-06283538ab53, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":2192587887728361290}
{"level":"info","ts":"2025-05-22T07:07:02.413Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":2192587887728361290}
--- PASS: TestCustomerFilterViewTestSuite (3.52s)
    --- PASS: TestCustomerFilterViewTestSuite/TestCustomerFilterView (0.35s)
    --- PASS: TestCustomerFilterViewTestSuite/TestUpdateCustomerViewWithFilter (0.26s)
    --- PASS: TestCustomerFilterViewTestSuite/TestUpdateCustomerViewWithTitle (0.27s)
    --- PASS: TestCustomerFilterViewTestSuite/TestViewListContainsNewView (0.26s)
=== RUN   TestCustomerListTestSuite
{"level":"info","ts":"2025-05-22T07:07:02.414Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:02.666Z","caller":"api/response.go:49","msg":"reqeust id: 7dc9d815-9b2a-9177-b5c4-016659e211a6, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:02.993Z","caller":"api/response.go:49","msg":"reqeust id: 45c5e16b-69a2-9a8a-8cdb-3ef6575681fe, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":9100073262113278562}
{"traces": [[{"trace_id":"1e6da3112adf7f4a","span_id":"1e6da3112adf7f4a","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"language":"go","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.tid":"682ecd1200000000","_dd.p.dm":"-1"},"metrics":{"process_id":15164,"_dd.profiling.enabled":0,"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1,"_dd.agent_psr":1},"start":1747897618897891000,"duration":**********,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"3fc1fad6b039a119","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897618897980000,"duration":*********,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"21e96bbf1596c26d","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897619157360000,"duration":*********,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"2dea4d5497a0d77b","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897620161344000,"duration":*********,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"1e4ba74eeb4eab7c","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/filter/view/add"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897620486567000,"duration":*********,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"18d42da2fbaf2189","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/filter/view/detail?id=9745"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897620754756000,"duration":349614000,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"53b9982f155137a5","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/filter/view/update","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897621105936000,"duration":262386000,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"137e162e9476be91","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/filter/view/update","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897621368628000,"duration":266039000,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"10850fbf372c2568","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/filter/view/list"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897621634937000,"duration":260561000,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"3b333a33abbcc718","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer/filter/view/delete?id=9745","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897621895825000,"duration":*********,"service":"customer"},{"trace_id":"1e6da3112adf7f4a","span_id":"52df0485ac0b8e93","parent_id":"1e6da3112adf7f4a","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897622154573000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:07:03.812Z","caller":"api/response.go:49","msg":"reqeust id: 76ed785c-a985-99f3-b681-704f38374db0, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:04.226Z","caller":"api/response.go:49","msg":"reqeust id: 285c8263-c31c-9299-8ed8-197a55d902d5, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":9100073262113278562}
=== RUN   TestCustomerListTestSuite/TestCustomerList
{"level":"info","ts":"2025-05-22T07:07:04.718Z","caller":"api/response.go:49","msg":"reqeust id: 8fc9f522-a5cc-9c1d-8842-ccecfd6b7e51, POST https://go.t2.moego.dev/api/customer/list","trace_id":9100073262113278562}
=== RUN   TestCustomerListTestSuite/TestCustomerListTotal
{"level":"info","ts":"2025-05-22T07:07:05.069Z","caller":"api/response.go:49","msg":"reqeust id: 651f8676-43ac-90ed-88c8-b56d94a89514, POST https://go.t2.moego.dev/api/customer/list/total","trace_id":9100073262113278562}
=== RUN   TestCustomerListTestSuite/TestCustomerListTotalWithCategory
{"level":"info","ts":"2025-05-22T07:07:05.327Z","caller":"api/response.go:49","msg":"reqeust id: 902bba4e-d4c9-9d09-b1b7-709bb960a6cd, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:05.671Z","caller":"api/response.go:49","msg":"reqeust id: 639c06e4-1dc8-9f70-97e2-23d21470dfc2, POST https://go.t2.moego.dev/api/customer/list/total","trace_id":9100073262113278562}
=== RUN   TestCustomerListTestSuite/TestCustomerListTotalWithKeyword
{"level":"info","ts":"2025-05-22T07:07:06.045Z","caller":"api/response.go:49","msg":"reqeust id: ab37b4e4-336f-9a78-8cab-bbcbac816226, POST https://go.t2.moego.dev/api/customer/list/total","trace_id":9100073262113278562}
=== RUN   TestCustomerListTestSuite/TestCustomerListTotalWithTags
{"level":"info","ts":"2025-05-22T07:07:06.299Z","caller":"api/response.go:49","msg":"reqeust id: f14cbf61-4c7f-9c62-96e9-8e62f633124d, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:06.563Z","caller":"api/response.go:49","msg":"reqeust id: ec43a8f8-14d8-9ca4-9943-de803b7c310c, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:06.903Z","caller":"api/response.go:49","msg":"reqeust id: 1a5b7b61-8f8d-932f-a53c-25aec8091f61, POST https://go.t2.moego.dev/api/customer/list/total","trace_id":9100073262113278562}
=== RUN   TestCustomerListTestSuite/TestCustomerListWithCategory
{"level":"info","ts":"2025-05-22T07:07:07.159Z","caller":"api/response.go:49","msg":"reqeust id: a1486dc9-eda7-907f-ba33-c323ae9895c4, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:07.597Z","caller":"api/response.go:49","msg":"reqeust id: 72069b4a-8bce-9743-b895-50d3712c21a3, POST https://go.t2.moego.dev/api/customer/list","trace_id":9100073262113278562}
=== RUN   TestCustomerListTestSuite/TestCustomerListWithKeyword
{"level":"info","ts":"2025-05-22T07:07:08.051Z","caller":"api/response.go:49","msg":"reqeust id: 943f5967-5ad1-9894-9f98-3e6fbdda2984, POST https://go.t2.moego.dev/api/customer/list","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:08.509Z","caller":"api/response.go:49","msg":"reqeust id: f4a39775-f1e8-98a6-b9d8-2980ed57eb16, POST https://go.t2.moego.dev/api/customer/list","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:08.964Z","caller":"api/response.go:49","msg":"reqeust id: 05f8bfd0-88df-912e-af8e-b2bb798536a0, POST https://go.t2.moego.dev/api/customer/list","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:09.440Z","caller":"api/response.go:49","msg":"reqeust id: 974fbe71-9b77-9c35-a018-2a32a3239557, POST https://go.t2.moego.dev/api/customer/list","trace_id":9100073262113278562}
=== RUN   TestCustomerListTestSuite/TestCustomerListWithTags
{"level":"info","ts":"2025-05-22T07:07:09.698Z","caller":"api/response.go:49","msg":"reqeust id: c1551e50-bce5-9aa1-8b41-72802953e085, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:09.963Z","caller":"api/response.go:49","msg":"reqeust id: 59336e2e-5f00-9759-98aa-c74c9ed2df84, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:10.431Z","caller":"api/response.go:49","msg":"reqeust id: a947cbae-7ce1-9f66-bb1c-4b73da1f31e1, POST https://go.t2.moego.dev/api/customer/list","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:10.801Z","caller":"api/response.go:49","msg":"reqeust id: f0192cc3-9091-974c-98e7-8d7961b2799e, DELETE https://go.t2.moego.dev/api/customer","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:11.061Z","caller":"api/response.go:49","msg":"reqeust id: 60c7dd11-722d-9005-bba3-65f64dd7a0d4, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":9100073262113278562}
{"level":"info","ts":"2025-05-22T07:07:11.061Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":9100073262113278562}
--- PASS: TestCustomerListTestSuite (8.65s)
    --- PASS: TestCustomerListTestSuite/TestCustomerList (0.49s)
    --- PASS: TestCustomerListTestSuite/TestCustomerListTotal (0.35s)
    --- PASS: TestCustomerListTestSuite/TestCustomerListTotalWithCategory (0.60s)
    --- PASS: TestCustomerListTestSuite/TestCustomerListTotalWithKeyword (0.37s)
    --- PASS: TestCustomerListTestSuite/TestCustomerListTotalWithTags (0.86s)
    --- PASS: TestCustomerListTestSuite/TestCustomerListWithCategory (0.69s)
    --- PASS: TestCustomerListTestSuite/TestCustomerListWithKeyword (1.84s)
    --- PASS: TestCustomerListTestSuite/TestCustomerListWithTags (0.99s)
=== RUN   TestCustomerOverviewTestSuite
{"level":"info","ts":"2025-05-22T07:07:11.063Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:11.314Z","caller":"api/response.go:49","msg":"reqeust id: b26cd22a-d50b-98dd-93cc-a896fc0903fb, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:11.646Z","caller":"api/response.go:49","msg":"reqeust id: 37fb9451-f99d-98b3-a92c-40659fbd7a48, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":3881749612650027323}
{"traces": [[{"trace_id":"7e49f46aea4aca62","span_id":"7e49f46aea4aca62","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"language":"go","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.tid":"682ecd1600000000","_dd.p.dm":"-1"},"metrics":{"_dd.agent_psr":1,"process_id":15164,"_dd.profiling.enabled":0,"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1},"start":1747897622414587000,"duration":**********,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"3261b06556c33e03","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897622414796000,"duration":*********,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"31a19be9ae93fc3c","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897622666929000,"duration":*********,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"84540b58ee58bac","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897623495033000,"duration":*********,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"1313ecbef078d47c","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/withPet","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897623812865000,"duration":*********,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"329ff83d5d25f664","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/customer/list"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897624227380000,"duration":490986000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"6e5892d2f082d5de","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/list/total","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897624719008000,"duration":350054000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"43527cdbbeb80c8a","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897625069622000,"duration":258052000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"1b911bc7f13e7fc4","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/list/total","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897625327857000,"duration":343759000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"40ab0929dd3b563a","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/list/total","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897625672181000,"duration":372986000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"788202cf3e45a5cb","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","component":"net/http"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897626045442000,"duration":254384000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"402c5d0621ed069a","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"PUT","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/customer/detail"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897626300422000,"duration":262820000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"53c256bb81444523","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/list/total","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897626563544000,"duration":339874000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"6994f6133d3f9225","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897626903673000,"duration":255854000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"11b484e4e6c6dbe","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/list","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897627159761000,"duration":437566000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"5e4a854959e6ddd4","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/customer/list"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897627598119000,"duration":453052000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"28aa195494dcace2","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/list","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897628051456000,"duration":457889000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"49a62a13c81b30b3","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/list","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897628509894000,"duration":454900000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"55bbbb1ecbef020b","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/list","component":"net/http"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897628965126000,"duration":475119000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"48cac48e7960bee","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897629441173000,"duration":257448000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"7620d65946370601","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897629698879000,"duration":264758000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"7a86f8de9e09ff1b","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/list","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897629963888000,"duration":467041000,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"5812893cf1fa9a44","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897630431669000,"duration":*********,"service":"customer"},{"trace_id":"7e49f46aea4aca62","span_id":"17deb488c32ee571","parent_id":"7e49f46aea4aca62","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897630801608000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:07:12.468Z","caller":"api/response.go:49","msg":"reqeust id: ebe7dd70-4bd5-9c0c-9660-e31ea8453b82, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:12.900Z","caller":"api/response.go:49","msg":"reqeust id: 4c4e76e3-1276-98bf-ad26-ba3a9e8bcfea, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:13.157Z","caller":"api/response.go:49","msg":"reqeust id: 745d7584-0346-9689-943b-98489141e6e1, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:13.427Z","caller":"api/response.go:49","msg":"reqeust id: 42d3e745-0a0c-94f6-9895-1975c9bcce15, PUT https://go.t2.moego.dev/api/customer/detail","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:13.737Z","caller":"api/response.go:49","msg":"reqeust id: 6ab2ae34-2a9b-9c4a-9287-cb7f678c0c6b, POST https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","trace_id":3881749612650027323}
=== RUN   TestCustomerOverviewTestSuite/TestCustomerOverview
{"level":"info","ts":"2025-05-22T07:07:14.197Z","caller":"api/response.go:49","msg":"reqeust id: 453b31b2-5978-9c25-b63e-49649cce4645, GET https://go.t2.moego.dev/api/customer/overview?customerId=********","trace_id":3881749612650027323}
=== RUN   TestCustomerOverviewTestSuite/TestOverviewMetricWithCancelled
{"level":"info","ts":"2025-05-22T07:07:14.799Z","caller":"api/response.go:49","msg":"reqeust id: 83e57018-1e7a-9a4a-b824-88c90311bddf, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:15.311Z","caller":"api/response.go:49","msg":"reqeust id: b98ced2d-3f7c-9abf-b648-1a7ddec2cdb9, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:15.756Z","caller":"api/response.go:49","msg":"reqeust id: 85bde152-cb4d-9253-8321-7a1b21c13e6a, GET https://go.t2.moego.dev/api/customer/overview?customerId=********","trace_id":3881749612650027323}
=== RUN   TestCustomerOverviewTestSuite/TestOverviewMetricWithNoShow
{"level":"info","ts":"2025-05-22T07:07:16.272Z","caller":"api/response.go:49","msg":"reqeust id: 0e319d1c-aa3c-9d03-8368-98fabe1c5c28, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:16.627Z","caller":"api/response.go:49","msg":"reqeust id: c6ad8bb0-cd43-9626-b0de-1e40a537a41a, PUT https://go.t2.moego.dev/api/grooming/appointment/cancel","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:17.078Z","caller":"api/response.go:49","msg":"reqeust id: 7abef520-9675-91f3-9f1a-d55c14503fc4, GET https://go.t2.moego.dev/api/customer/overview?customerId=********","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:17.454Z","caller":"api/response.go:49","msg":"reqeust id: 45ce772a-c680-90dc-8651-fee873c15957, DELETE https://go.t2.moego.dev/api/customer","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:17.710Z","caller":"api/response.go:49","msg":"reqeust id: 9b384408-b76e-9303-805e-cca044588668, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":3881749612650027323}
{"level":"info","ts":"2025-05-22T07:07:17.710Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":3881749612650027323}
--- PASS: TestCustomerOverviewTestSuite (6.65s)
    --- PASS: TestCustomerOverviewTestSuite/TestCustomerOverview (0.46s)
    --- PASS: TestCustomerOverviewTestSuite/TestOverviewMetricWithCancelled (1.56s)
    --- PASS: TestCustomerOverviewTestSuite/TestOverviewMetricWithNoShow (1.32s)
=== RUN   TestClientPetTestSuite
{"level":"info","ts":"2025-05-22T07:07:17.712Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:17.959Z","caller":"api/response.go:49","msg":"reqeust id: 2b179f6b-0003-994c-851e-e8273f5ea699, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:18.298Z","caller":"api/response.go:49","msg":"reqeust id: 1b6cba27-c4c3-95f7-bf45-6308776071ac, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:19.114Z","caller":"api/response.go:49","msg":"reqeust id: 870d68f2-10b9-9770-aa3e-2176bce83cf5, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:19.497Z","caller":"api/response.go:49","msg":"reqeust id: 6b4f4bcb-5331-9805-a514-f59b82b64b1c, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestAddCustomerPet
{"traces": [[{"trace_id":"35debeb4d103d93b","span_id":"35debeb4d103d93b","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"language":"go","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.dm":"-1","_dd.p.tid":"682ecd1f00000000"},"metrics":{"_dd.agent_psr":1,"process_id":15164,"_dd.profiling.enabled":0,"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1},"start":1747897631062993000,"duration":**********,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"73c8167f36f36e2d","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897631063114000,"duration":*********,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"4ccca0f05d279c73","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897631314803000,"duration":*********,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"1693ad28e6f45ee8","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897632147511000,"duration":*********,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"484a75174a660518","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/withPet","component":"net/http","span.kind":"client"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897632469439000,"duration":*********,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"69984fd3cec07804","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897632900953000,"duration":256188000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"17e2a22cf9b92589","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/detail","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897633157452000,"duration":269497000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"420a8601eec54216","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","component":"net/http","span.kind":"client"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897633427495000,"duration":310045000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"67f9deaee0679597","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/overview?customerId=********","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897633738829000,"duration":458911000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"630aa919bb9ac575","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897634199800000,"duration":599863000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"38d74fb7b43a6969","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"PUT","http.url":"https://go.t2.moego.dev/api/grooming/appointment/cancel","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897634800911000,"duration":510287000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"5ec3ffd94e8814a0","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/overview?customerId=********","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897635311775000,"duration":444738000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"378e8d8bec8ebe1c","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897635758119000,"duration":514073000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"6f25c744b3f1b60e","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/grooming/appointment/cancel","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897636272442000,"duration":355260000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"2b3a21503a4f3479","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/overview?customerId=********","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897636627866000,"duration":450653000,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"4a831ab6b1e91df","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897637078916000,"duration":*********,"service":"customer"},{"trace_id":"35debeb4d103d93b","span_id":"16d326f34f295693","parent_id":"35debeb4d103d93b","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897637455009000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:07:19.752Z","caller":"api/response.go:49","msg":"reqeust id: dbcfe8de-580b-9105-b182-dc70ab22c20b, POST https://go.t2.moego.dev/api/customer/pet","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:20.003Z","caller":"api/response.go:49","msg":"reqeust id: 540ef94a-81f8-964a-9b1e-9ba4ed9f0556, GET https://go.t2.moego.dev/api/customer/pet/list?customerId=********","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:20.279Z","caller":"api/response.go:49","msg":"reqeust id: 01747f42-90f6-9411-956a-7a835ec0d62e, GET https://go.t2.moego.dev/api/customer/pet/detail?petId=********","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:20.640Z","caller":"api/response.go:49","msg":"reqeust id: aa1405c2-4a5b-9ca9-bc07-42468a1f7e0e, DELETE https://go.t2.moego.dev/api/customer/pet","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:20.893Z","caller":"api/response.go:49","msg":"reqeust id: 7eaeca86-7834-9b11-b999-ce167e17c331, GET https://go.t2.moego.dev/api/customer/pet/list?customerId=********","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestAddMedical
{"level":"info","ts":"2025-05-22T07:07:21.150Z","caller":"api/response.go:49","msg":"reqeust id: e6051697-5cd8-9cd7-9f21-2c222838b6f8, PUT https://go.t2.moego.dev/api/customer/pet","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:21.426Z","caller":"api/response.go:49","msg":"reqeust id: 86f56a93-5f4b-9167-8038-4feee6e132ae, GET https://go.t2.moego.dev/api/customer/pet/detail?petId=********","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestAddPetCode
{"level":"info","ts":"2025-05-22T07:07:21.674Z","caller":"api/response.go:49","msg":"reqeust id: dd3132d7-0fda-9612-a8d4-76f42a7350e2, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetCodeService/ListPetCode","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:21.935Z","caller":"api/response.go:49","msg":"reqeust id: 88993d2f-b79e-92e7-8d43-92bf3c8a06b0, PUT https://go.t2.moego.dev/api/customer/pet","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:22.207Z","caller":"api/response.go:49","msg":"reqeust id: 73a7bf04-9ff0-9df1-8e78-a0e636d63aa6, GET https://go.t2.moego.dev/api/customer/pet/detail?petId=********","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestAddPetNote
{"level":"info","ts":"2025-05-22T07:07:22.456Z","caller":"api/response.go:49","msg":"reqeust id: affdfbb7-a5ee-9680-b7a8-78fe0a82e437, POST https://go.t2.moego.dev/api/customer/pet/note","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:22.733Z","caller":"api/response.go:49","msg":"reqeust id: 0ee0749e-cf93-9044-a7a0-96cdf52fac1c, GET https://go.t2.moego.dev/api/customer/pet/detail?petId=********","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestAddPetVaccination
{"level":"info","ts":"2025-05-22T07:07:22.988Z","caller":"api/response.go:49","msg":"reqeust id: c9e4c872-116e-9d5a-8b8b-58d2bb140a09, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetVaccineService/ListPetVaccine","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:23.246Z","caller":"api/response.go:49","msg":"reqeust id: a5ad0fcd-f68a-9c60-9153-e326c633f91b, POST https://go.t2.moego.dev/api/customer/pet/vaccine/binding","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:23.528Z","caller":"api/response.go:49","msg":"reqeust id: 58756b3f-0e2d-9528-b61c-f7363a2e8791, GET https://go.t2.moego.dev/api/customer/pet/detail?petId=********","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestGetPetDetail
{"level":"info","ts":"2025-05-22T07:07:23.807Z","caller":"api/response.go:49","msg":"reqeust id: 84a8d4b4-528d-9ef1-86f3-febeec44bfd3, GET https://go.t2.moego.dev/api/customer/pet/detail?petId=********","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestGetPetDetailList
{"level":"info","ts":"2025-05-22T07:07:24.076Z","caller":"api/response.go:49","msg":"reqeust id: 14d2fb9c-223d-9a8c-aa6b-6ff0bbd9d923, GET https://go.t2.moego.dev/api/customer/pet/detail/list?petIdList=********","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestGetPetList
{"level":"info","ts":"2025-05-22T07:07:24.331Z","caller":"api/response.go:49","msg":"reqeust id: 460a79eb-1c35-9f61-93c3-778bf12bdf6d, GET https://go.t2.moego.dev/api/customer/pet/list?customerId=********","trace_id":*****************9}
=== RUN   TestClientPetTestSuite/TestMarkAsPassedAway
{"level":"info","ts":"2025-05-22T07:07:24.714Z","caller":"api/response.go:49","msg":"reqeust id: 640cf13e-0989-9c09-a5d6-4e8355b5c327, PUT https://go.t2.moego.dev/api/customer/pet/lifeStatus","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:25.002Z","caller":"api/response.go:49","msg":"reqeust id: 0e379b91-2c48-9e90-9287-2036010fbf26, GET https://go.t2.moego.dev/api/customer/pet/detail?petId=********","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:25.322Z","caller":"api/response.go:49","msg":"reqeust id: 384baf59-08b0-9eb5-9b55-aa07b977337f, PUT https://go.t2.moego.dev/api/customer/pet/lifeStatus","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:25.597Z","caller":"api/response.go:49","msg":"reqeust id: d4f5d556-6b43-9d6c-a447-b977f08ed764, GET https://go.t2.moego.dev/api/customer/pet/detail?petId=********","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:25.971Z","caller":"api/response.go:49","msg":"reqeust id: 4c4d12e1-ac43-9db6-bd4d-d34a34031cba, DELETE https://go.t2.moego.dev/api/customer","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:26.225Z","caller":"api/response.go:49","msg":"reqeust id: f1b79471-beb6-9c9a-9d4c-55a6c83a3124, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":*****************9}
{"level":"info","ts":"2025-05-22T07:07:26.225Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":*****************9}
--- PASS: TestClientPetTestSuite (8.51s)
    --- PASS: TestClientPetTestSuite/TestAddCustomerPet (1.40s)
    --- PASS: TestClientPetTestSuite/TestAddMedical (0.53s)
    --- PASS: TestClientPetTestSuite/TestAddPetCode (0.78s)
    --- PASS: TestClientPetTestSuite/TestAddPetNote (0.53s)
    --- PASS: TestClientPetTestSuite/TestAddPetVaccination (0.80s)
    --- PASS: TestClientPetTestSuite/TestGetPetDetail (0.28s)
    --- PASS: TestClientPetTestSuite/TestGetPetDetailList (0.27s)
    --- PASS: TestClientPetTestSuite/TestGetPetList (0.26s)
    --- PASS: TestClientPetTestSuite/TestMarkAsPassedAway (1.27s)
=== RUN   TestCustomerPetNoteTestSuite
{"level":"info","ts":"2025-05-22T07:07:26.228Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":2802678357021429598}
{"level":"info","ts":"2025-05-22T07:07:26.497Z","caller":"api/response.go:49","msg":"reqeust id: e6c71b11-e626-90ab-93ad-16c4385c2d43, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":2802678357021429598}
{"level":"info","ts":"2025-05-22T07:07:26.829Z","caller":"api/response.go:49","msg":"reqeust id: 2f57c479-23ed-99bb-bc26-9e1d63034322, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":2802678357021429598}
{"level":"info","ts":"2025-05-22T07:07:27.653Z","caller":"api/response.go:49","msg":"reqeust id: e6c7f61a-ee17-97e8-8368-061a72e700db, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":2802678357021429598}
{"traces": [[{"trace_id":"791e9f061aebe49","span_id":"791e9f061aebe49","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.dm":"-1","_dd.p.tid":"682ecd2500000000","language":"go"},"metrics":{"process_id":15164,"_dd.profiling.enabled":0,"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1,"_dd.agent_psr":1},"start":1747897637712024000,"duration":**********,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"19901c6e1ad64ed","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897637712270000,"duration":*********,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"7b1d6251630448d2","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897637960096000,"duration":*********,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"645438c206e7aec7","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897638799705000,"duration":*********,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"242180adefc1b1fa","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/withPet","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897639114623000,"duration":*********,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"145700bed884dcc2","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/pet","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897639498209000,"duration":253967000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"3c355b36fe804d77","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/list?customerId=********","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897639752403000,"duration":250726000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"3052ca7bce838b54","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/customer/pet/detail?petId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897640003513000,"duration":276209000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"44ba6877b77c047","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer/pet","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897640280597000,"duration":360236000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"755392573f20b368","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/list?customerId=********","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897640641108000,"duration":251998000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"6e408fb82ed83b3a","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/pet","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897640893714000,"duration":257101000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"14493995a013d5e9","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/pet/detail?petId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897641151058000,"duration":275507000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"7065abf37f29a67b","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetCodeService/ListPetCode","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897641427057000,"duration":247574000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"75fc77a541ffcfaa","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/pet","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897641675748000,"duration":259287000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"73e6460cd18ce941","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/detail?petId=********","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897641936715000,"duration":270821000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"5adaddc4977a9e3","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/pet/note","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897642207973000,"duration":248807000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"7aedd3b847ee79f4","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/detail?petId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897642456937000,"duration":276051000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"72ca24f326ee1256","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetVaccineService/ListPetVaccine","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897642733633000,"duration":254562000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"73bf08de1a38e2f2","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/pet/vaccine/binding","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897642989144000,"duration":257140000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"5361052610ec605d","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/detail?petId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897643246549000,"duration":281647000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"6279436b4ded368e","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/detail?petId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897643528957000,"duration":278378000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"32ecfcea08dc0c8c","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/detail/list?petIdList=********","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897643808084000,"duration":267831000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"57f01a4ab05698cb","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/list?customerId=********","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897644076300000,"duration":255470000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"3d3d7e14cb7e1f6b","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/pet/lifeStatus","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897644332315000,"duration":382520000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"24f057bf90973107","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/detail?petId=********","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897644715330000,"duration":287027000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"7115f2c1995f0017","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/pet/lifeStatus","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897645002815000,"duration":319242000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"16bda1cf84d646ef","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","network.destination.name":"go.t2.moego.dev","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/pet/detail?petId=********","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897645322318000,"duration":274978000,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"6b1a9ba2444449c8","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897645597829000,"duration":*********,"service":"customer"},{"trace_id":"791e9f061aebe49","span_id":"787245711a42102f","parent_id":"791e9f061aebe49","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897645971672000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:07:28.071Z","caller":"api/response.go:49","msg":"reqeust id: b34eae19-c2b5-9104-a05c-e168f27d9854, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":2802678357021429598}
=== RUN   TestCustomerPetNoteTestSuite/TestCustomerNotes
{"level":"info","ts":"2025-05-22T07:07:28.329Z","caller":"api/response.go:49","msg":"reqeust id: b4783105-a295-9c88-b417-388d577f182a, POST https://go.t2.moego.dev/api/customer/note","trace_id":2802678357021429598}
{"level":"info","ts":"2025-05-22T07:07:28.601Z","caller":"api/response.go:49","msg":"reqeust id: e90ad61a-6883-9110-9ebd-8106c12df2ea, POST https://go.t2.moego.dev/api/customer/note/withPet","trace_id":2802678357021429598}
=== RUN   TestCustomerPetNoteTestSuite/TestPetNotes
{"level":"info","ts":"2025-05-22T07:07:28.851Z","caller":"api/response.go:49","msg":"reqeust id: b60cc142-7214-9295-8f81-94f417e434e6, POST https://go.t2.moego.dev/api/customer/pet/note","trace_id":2802678357021429598}
{"level":"info","ts":"2025-05-22T07:07:29.138Z","caller":"api/response.go:49","msg":"reqeust id: 894ab2ff-a88f-9d7e-9fe2-1a8137ad1617, POST https://go.t2.moego.dev/api/customer/note/withPet","trace_id":2802678357021429598}
{"level":"info","ts":"2025-05-22T07:07:30.221Z","caller":"api/response.go:49","msg":"reqeust id: 3b55083f-9383-9784-a994-de671d3e6021, DELETE https://go.t2.moego.dev/api/customer","trace_id":2802678357021429598}
{"level":"info","ts":"2025-05-22T07:07:30.488Z","caller":"api/response.go:49","msg":"reqeust id: 7cd7c94b-c7bf-9011-80d2-58448fd178db, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":2802678357021429598}
{"level":"info","ts":"2025-05-22T07:07:30.488Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":2802678357021429598}
--- PASS: TestCustomerPetNoteTestSuite (4.26s)
    --- PASS: TestCustomerPetNoteTestSuite/TestCustomerNotes (0.53s)
    --- PASS: TestCustomerPetNoteTestSuite/TestPetNotes (0.54s)
=== RUN   TestDetailedForm
{"level":"info","ts":"2025-05-22T07:07:30.490Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:30.743Z","caller":"api/response.go:49","msg":"reqeust id: d9b9dc64-c354-9a4b-8f9c-3eccf8a039de, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:31.079Z","caller":"api/response.go:49","msg":"reqeust id: d1ca7749-c3da-93d1-ab60-fb24e95541e3, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":2177558592450227192}
{"traces": [[{"trace_id":"26e51d1dd1f00f5e","span_id":"26e51d1dd1f00f5e","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.dm":"-1","_dd.p.tid":"682ecd2e00000000","language":"go"},"metrics":{"process_id":15164,"_dd.profiling.enabled":0,"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1,"_dd.agent_psr":1},"start":1747897646228553000,"duration":**********,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"2de0aabc0efc9999","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897646229217000,"duration":*********,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"3ba4b199e1048a7a","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897646497875000,"duration":*********,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"7adbd87baef3b79f","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897647330752000,"duration":*********,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"677a5f66e6934778","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/withPet","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897647653485000,"duration":*********,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"1fb28c6a6af94bc8","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/note","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897648072511000,"duration":257016000,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"aca3650457ef7a7","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/note/withPet","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897648329861000,"duration":271474000,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"48d126f00c7754b9","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/pet/note","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897648602080000,"duration":248797000,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"10e45e8ae826efcc","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/note/withPet","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897648851330000,"duration":286697000,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"271df630dc57567f","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"DELETE","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897649138456000,"duration":**********,"service":"customer"},{"trace_id":"26e51d1dd1f00f5e","span_id":"103342c8ca79bc2a","parent_id":"26e51d1dd1f00f5e","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897650221991000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:07:31.906Z","caller":"api/response.go:49","msg":"reqeust id: a83069dc-962d-9f67-b4fa-3b536790ccee, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:32.349Z","caller":"api/response.go:49","msg":"reqeust id: 430b7fb6-f59b-93e7-8d8c-fed81e688763, POST https://go.t2.moego.dev/api/customer/form/add","trace_id":2177558592450227192}
=== RUN   TestDetailedForm/TestAddQuestion
{"level":"info","ts":"2025-05-22T07:07:32.600Z","caller":"api/response.go:49","msg":"reqeust id: 16809b0f-e85f-987d-86c3-b962999d6d1b, POST https://go.t2.moego.dev/api/customer/form/addQuestion","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:32.858Z","caller":"api/response.go:49","msg":"reqeust id: 76cb7d44-0190-91fd-9814-70f2ca12d4d3, GET https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:33.125Z","caller":"api/response.go:49","msg":"reqeust id: 98310fa3-ad6e-92cc-9784-634f7f2b69cb, DELETE https://go.t2.moego.dev/api/customer/form/deleteDetailedForm?formDetailId=563880","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:33.383Z","caller":"api/response.go:49","msg":"reqeust id: 5d9951c3-d979-97cd-a2d4-361b12cb08da, GET https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","trace_id":2177558592450227192}
=== RUN   TestDetailedForm/TestFormIDsContainsAddedForm
{"level":"info","ts":"2025-05-22T07:07:33.630Z","caller":"api/response.go:49","msg":"reqeust id: afdeb1db-d3c2-99e5-ba87-ecfdf073707b, GET https://go.t2.moego.dev/api/customer/form/getDetailedForm","trace_id":2177558592450227192}
=== RUN   TestDetailedForm/TestSortForm
{"level":"info","ts":"2025-05-22T07:07:33.890Z","caller":"api/response.go:49","msg":"reqeust id: 9d29e8fe-e7a8-954b-bfbb-8009fba2e386, GET https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:34.155Z","caller":"api/response.go:49","msg":"reqeust id: d7bc7299-e6b4-956c-b870-2d31df76fcb4, PUT https://go.t2.moego.dev/api/customer/form/sortDetailedForm","trace_id":2177558592450227192}
=== RUN   TestDetailedForm/TestUpdateDetailedForm
{"level":"info","ts":"2025-05-22T07:07:34.415Z","caller":"api/response.go:49","msg":"reqeust id: 43abd4ab-8fcb-9a8c-a731-28ec926013f4, GET https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:34.664Z","caller":"api/response.go:49","msg":"reqeust id: 819ecf4e-fff0-9ef3-8ced-82275dfd9555, PUT https://go.t2.moego.dev/api/customer/form/updateDetailedForm","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:34.923Z","caller":"api/response.go:49","msg":"reqeust id: 4b6d3668-ae0e-942b-876c-7e55a173b7f4, GET https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","trace_id":2177558592450227192}
=== RUN   TestDetailedForm/TestUpdateFormBasicInfo
{"level":"info","ts":"2025-05-22T07:07:35.174Z","caller":"api/response.go:49","msg":"reqeust id: 200929fc-947f-9959-a0e3-528bf157a899, POST https://go.t2.moego.dev/api/customer/form/update","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:35.433Z","caller":"api/response.go:49","msg":"reqeust id: 1473056f-66af-9878-855e-cbed9c021e14, GET https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:35.682Z","caller":"api/response.go:49","msg":"reqeust id: b84d68c0-f341-9361-8719-f89a890d3151, DELETE https://go.t2.moego.dev/api/customer/form/delete?formId=22756","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:35.938Z","caller":"api/response.go:49","msg":"reqeust id: 51315fae-46b0-9209-b0c9-b8fcbe05f596, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":2177558592450227192}
{"level":"info","ts":"2025-05-22T07:07:35.938Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":2177558592450227192}
--- PASS: TestDetailedForm (5.45s)
    --- PASS: TestDetailedForm/TestAddQuestion (1.03s)
    --- PASS: TestDetailedForm/TestFormIDsContainsAddedForm (0.25s)
    --- PASS: TestDetailedForm/TestSortForm (0.52s)
    --- PASS: TestDetailedForm/TestUpdateDetailedForm (0.77s)
    --- PASS: TestDetailedForm/TestUpdateFormBasicInfo (0.51s)
=== RUN   TestIncidentReportTestSuite
{"level":"info","ts":"2025-05-22T07:07:35.939Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:36.196Z","caller":"api/response.go:49","msg":"reqeust id: 129f60d6-8e57-950e-8e2c-50659f979d8e, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:36.521Z","caller":"api/response.go:49","msg":"reqeust id: 0ae67148-ddb2-91e6-afc4-e3292d89a115, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:37.341Z","caller":"api/response.go:49","msg":"reqeust id: 8e12d939-bc65-9e94-8967-8db46dbe053f, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":1763815226049404382}
{"traces": [[{"trace_id":"1e383e00ac986bf8","span_id":"1e383e00ac986bf8","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"language":"go","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.dm":"-1","_dd.p.tid":"682ecd3200000000"},"metrics":{"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1,"_dd.agent_psr":1,"process_id":15164,"_dd.profiling.enabled":0},"start":1747897650490506000,"duration":**********,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"509ab53e7bf2c44e","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897650490905000,"duration":*********,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"2da4b14b47ca880f","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897650744184000,"duration":*********,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"7136e4fb08e8ddde","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897651579870000,"duration":*********,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"6ea0f74fcd17d36b","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/form/add","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897651907352000,"duration":*********,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"8a9da408d0bd25d","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/api/customer/form/addQuestion","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897652350693000,"duration":249618000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"740c2ba1d807351b","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897652600624000,"duration":257654000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"a9924889a49c584","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"DELETE","http.url":"https://go.t2.moego.dev/api/customer/form/deleteDetailedForm?formDetailId=563880"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897652859573000,"duration":265618000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"3883b014fd504bda","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897653125391000,"duration":258092000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"2aa01a2ae4d55d9a","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/form/getDetailedForm","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897653384084000,"duration":246529000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"6e98948ae00d5862","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897653632097000,"duration":257776000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"55d5bfe4e7fcfc06","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"PUT","http.url":"https://go.t2.moego.dev/api/customer/form/sortDetailedForm","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897653891367000,"duration":263649000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"7226e23259f89c3e","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"GET","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897654155521000,"duration":260218000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"7469882f0a500cc5","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/form/updateDetailedForm","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"PUT","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897654416692000,"duration":247527000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"126d139eae0eef37","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897654664531000,"duration":258264000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"31dce1d8876193ab","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/form/update","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"POST"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897654923641000,"duration":250255000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"271d91568715b60a","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/customer/form/getDetailedFormById?formId=22756","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897655174229000,"duration":258601000,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"1a781a5669bc31ee","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/api/customer/form/delete?formId=22756","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"DELETE"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897655433758000,"duration":*********,"service":"customer"},{"trace_id":"1e383e00ac986bf8","span_id":"22c18252f9296fc6","parent_id":"1e383e00ac986bf8","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"platform-tools.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897655682878000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:07:37.856Z","caller":"api/response.go:49","msg":"reqeust id: 6fbaf569-1523-96b1-9786-98c9cf7899cb, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentTypeService/CreatePetIncidentType","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:38.203Z","caller":"api/response.go:49","msg":"reqeust id: 20d811b1-86e5-90b2-826f-ea5b2fcc2030, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/CreatePetIncidentReport","trace_id":1763815226049404382}
=== RUN   TestIncidentReportTestSuite/TestCreateIncidentReport_nullIncidentType
{"level":"info","ts":"2025-05-22T07:07:38.203Z","caller":"customer/incident_report_test.go:87","msg":"Start to run test IncidentReportTestSuite.TestCreateIncidentReport_nullIncidentType","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:38.505Z","caller":"api/response.go:49","msg":"reqeust id: ab7bbdf7-2da6-9a73-9b0e-54ec05c266bb, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/CreatePetIncidentReport","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:38.505Z","caller":"customer/incident_report_test.go:91","msg":"Finish running test IncidentReportTestSuite.TestCreateIncidentReport_nullIncidentType","trace_id":1763815226049404382}
=== RUN   TestIncidentReportTestSuite/TestListIncidentReport
{"level":"info","ts":"2025-05-22T07:07:38.505Z","caller":"customer/incident_report_test.go:87","msg":"Start to run test IncidentReportTestSuite.TestListIncidentReport","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:38.758Z","caller":"api/response.go:49","msg":"reqeust id: e602c4de-c3d0-9d8a-8318-766d8f90a1b6, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/ListPetIncidentReport","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:38.759Z","caller":"customer/incident_report_test.go:91","msg":"Finish running test IncidentReportTestSuite.TestListIncidentReport","trace_id":1763815226049404382}
=== RUN   TestIncidentReportTestSuite/TestUpdateIncidentReport
{"level":"info","ts":"2025-05-22T07:07:38.759Z","caller":"customer/incident_report_test.go:87","msg":"Start to run test IncidentReportTestSuite.TestUpdateIncidentReport","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:39.084Z","caller":"api/response.go:49","msg":"reqeust id: a97b2d7d-2981-9e02-aad4-8b90f96e1911, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/UpdatePetIncidentReport","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:39.342Z","caller":"api/response.go:49","msg":"reqeust id: bfd43aca-4e97-9d4f-812c-beafbb258a9f, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/ListPetIncidentReport","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:39.343Z","caller":"customer/incident_report_test.go:91","msg":"Finish running test IncidentReportTestSuite.TestUpdateIncidentReport","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:39.663Z","caller":"api/response.go:49","msg":"reqeust id: 97899a9f-675f-98e0-b878-cd89ba48e7f8, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/DeletePetIncidentReport","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:39.965Z","caller":"api/response.go:49","msg":"reqeust id: f379e96e-e5b3-987c-b04c-c6ea1b5ece2d, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentTypeService/DeletePetIncidentType","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:40.226Z","caller":"api/response.go:49","msg":"reqeust id: 5f071d6e-4aea-97c0-9009-277ef4ec2a0f, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":1763815226049404382}
{"level":"info","ts":"2025-05-22T07:07:40.226Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":1763815226049404382}
--- PASS: TestIncidentReportTestSuite (4.29s)
    --- PASS: TestIncidentReportTestSuite/TestCreateIncidentReport_nullIncidentType (0.30s)
    --- PASS: TestIncidentReportTestSuite/TestListIncidentReport (0.25s)
    --- PASS: TestIncidentReportTestSuite/TestUpdateIncidentReport (0.58s)
=== RUN   TestPetOptionsTestSuite
{"level":"info","ts":"2025-05-22T07:07:40.227Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":2813161996486845358}
{"level":"info","ts":"2025-05-22T07:07:40.486Z","caller":"api/response.go:49","msg":"reqeust id: 5da8dd35-9e65-9a82-97dc-eb1520a2d56f, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":2813161996486845358}
{"level":"info","ts":"2025-05-22T07:07:40.815Z","caller":"api/response.go:49","msg":"reqeust id: 0ea66f69-2bad-9541-87c6-d89841605dd3, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":2813161996486845358}
{"level":"info","ts":"2025-05-22T07:07:41.636Z","caller":"api/response.go:49","msg":"reqeust id: 2cf4b0b9-7870-9bdc-953e-c1803a432865, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":2813161996486845358}
=== RUN   TestPetOptionsTestSuite/TestDefaultPetOptions
{"traces": [[{"trace_id":"187a549a5e199dde","span_id":"187a549a5e199dde","parent_id":"0","name":"api-integration-testing","resource":"api-integration-testing","error":0,"meta":{"language":"go","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","_dd.p.dm":"-1","_dd.p.tid":"682ecd3700000000"},"metrics":{"_dd.trace_span_attribute_schema":0,"_dd.top_level":1,"_sampling_priority_v1":1,"_dd.agent_psr":1,"process_id":15164,"_dd.profiling.enabled":0},"start":1747897655939935000,"duration":**********,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"33e3c3f039323b23","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897655940151000,"duration":*********,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"6e726b3fbb2e7951","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.url":"https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897656196284000,"duration":*********,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"128df42d29264ee1","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"GET","http.url":"https://go.t2.moego.dev/api/business/account/v2/info","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897657023012000,"duration":*********,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"6464440418b1d8fd","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"http.status_code":"200","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentTypeService/CreatePetIncidentType","component":"net/http","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897657342545000,"duration":*********,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"447ae989f6acea09","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/CreatePetIncidentReport","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897657856862000,"duration":346229000,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"2fc27b83da3e9c9e","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":1,"meta":{"error.type":"*errors.errorString","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/CreatePetIncidentReport","component":"net/http","http.errors":"400 Bad Request","http.status_code":"400","error.message":"400: Bad Request","error.stack":"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer.(*span).setTagError\n\texternal/gazelle++go_deps+in_gopkg_datadog_dd_trace_go_v1/ddtrace/tracer/span.go:332\ngopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer.(*span).SetTag\n\texternal/gazelle++go_deps+in_gopkg_datadog_dd_trace_go_v1/ddtrace/tracer/span.go:127\ngopkg.in/DataDog/dd-trace-go.v1/contrib/net/http.(*roundTripper).RoundTrip\n\texternal/gazelle++go_deps+in_gopkg_datadog_dd_trace_go_v1/contrib/net/http/roundtripper.go:100\nnet/http.send\n\tGOROOT/src/net/http/client.go:259\nnet/http.(*Client).send\n\tGOROOT/src/net/http/client.go:180\nnet/http.(*Client).do\n\tGOROOT/src/net/http/client.go:731\nnet/http.(*Client).Do\n\tGOROOT/src/net/http/client.go:590\ngithub.com/MoeGolibrary/go-lib/http.(*client).Do.WrapHTTPClientDo.func1\n\texternal/gazelle++go_deps+com_github_moegolibrary_go_lib/observability/http.go:29\ngithub.com/MoeGolibrary/go-lib/http.(*client).Do\n\texternal/gazelle++go_deps+com_github_moegolibrary_go_lib/http/client.go:80\ngithub.com/MoeGolibrary/moego/backend/test/api_integration/utils/api.(*Request).Send\n\tbackend/test/api_integration/utils/api/request.go:131\nbackend/test/api_integration/collection/customer/customer_test.(*IncidentReportTestSuite).TestCreateIncidentReport_nullIncidentType\n\tbackend/test/api_integration/collection/customer/incident_report_test.go:177\nreflect.Value.call\n\tGOROOT/src/reflect/value.go:581\nreflect.Value.Call\n\tGOROOT/src/reflect/value.go:365\ngithub.com/stretchr/testify/suite.Run.func1\n\texternal/gazelle++go_deps+com_github_stretchr_testify/suite/suite.go:202\ntesting.tRunner\n\tGOROOT/src/testing/testing.go:1690\nruntime.goexit\n\tsrc/runtime/asm_arm64.s:1223","language":"go","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897658204105000,"duration":300920000,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"59a06a0be69cd405","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/ListPetIncidentReport","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897658505819000,"duration":252470000,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"64237153e840e650","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/UpdatePetIncidentReport","component":"net/http","span.kind":"client"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897658759574000,"duration":324821000,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"570a68113a89e3fc","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/ListPetIncidentReport","component":"net/http","span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897659084568000,"duration":258162000,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"6d62efc3a39dc2e6","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentReportService/DeletePetIncidentReport","component":"net/http","span.kind":"client","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","network.destination.name":"go.t2.moego.dev","http.method":"POST"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897659343315000,"duration":320376000,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"6e9f1e05ef39efbf","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"span.kind":"client","network.destination.name":"go.t2.moego.dev","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.method":"POST","http.url":"https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessPetIncidentTypeService/DeletePetIncidentType","component":"net/http"},"metrics":{"_sampling_priority_v1":1,"process_id":15164},"start":1747897659663903000,"duration":*********,"service":"customer"},{"trace_id":"187a549a5e199dde","span_id":"64506d30f155e5f9","parent_id":"187a549a5e199dde","name":"http.request","resource":"http.request","error":0,"meta":{"component":"net/http","span.kind":"client","network.destination.name":"platform-tools.t2.moego.dev","http.method":"POST","runtime-id":"53d61ffc-92ce-491f-98b3-2ac247041527","http.status_code":"200","language":"go","http.url":"https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount"},"metrics":{"process_id":15164,"_sampling_priority_v1":1},"start":1747897659965539000,"duration":*********,"service":"customer"}]]}
{"level":"info","ts":"2025-05-22T07:07:41.945Z","caller":"api/response.go:49","msg":"reqeust id: 8784aa4a-4b1a-97c7-8e0c-5b0bf0488c7e, GET https://go.t2.moego.dev/api/customer/pet/options","trace_id":2813161996486845358}
--- PASS: TestPetOptionsTestSuite (1.72s)
    --- PASS: TestPetOptionsTestSuite/TestDefaultPetOptions (0.31s)
=== RUN   TestSmartListTestSuite
{"level":"info","ts":"2025-05-22T07:07:41.947Z","caller":"godomain/context.go:168","msg":"Setup suite","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:42.197Z","caller":"api/response.go:49","msg":"reqeust id: eddb4c34-7876-9807-b8d7-da26f43a2e57, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/BorrowTestAccount","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:42.525Z","caller":"api/response.go:49","msg":"reqeust id: 2329830d-6086-9843-a671-ca69f4e07de9, POST https://go.t2.moego.dev/moego.api.account.v1.AccountAccessService/Login","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:43.342Z","caller":"api/response.go:49","msg":"reqeust id: 8b16bf1c-2b84-9db3-a197-f2f2ca837fe9, GET https://go.t2.moego.dev/api/business/account/v2/info","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:43.713Z","caller":"api/response.go:49","msg":"reqeust id: e2826833-b3ae-92e6-871c-e19958ffb304, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:44.021Z","caller":"api/response.go:49","msg":"reqeust id: b2be39fa-3b5f-9595-bfd9-983452241e53, POST https://go.t2.moego.dev/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:44.538Z","caller":"api/response.go:49","msg":"reqeust id: 0f9c41bb-3689-912e-af17-d5e9bf8fa7f3, POST https://go.t2.moego.dev/moego.api.appointment.v1.AppointmentService/CreateAppointment","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:44.790Z","caller":"api/response.go:49","msg":"reqeust id: b3b81b39-e0a9-921a-befd-da5de47bf017, POST https://go.t2.moego.dev/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestBatchAddTag
{"level":"info","ts":"2025-05-22T07:07:44.790Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestBatchAddTag","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:45.289Z","caller":"api/response.go:49","msg":"reqeust id: 128ccb86-6ff5-9b02-bf47-04963d052b7a, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:45.639Z","caller":"api/response.go:49","msg":"reqeust id: 7174e36d-8ad2-96fe-b140-fa7e74441cd8, PUT https://go.t2.moego.dev/api/customer/smart-list/tag","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:46.084Z","caller":"api/response.go:49","msg":"reqeust id: 1522959b-7b53-9625-b00f-fae80ba69ead, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:46.084Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestBatchAddTag","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestBatchDeleteCustomer
{"level":"info","ts":"2025-05-22T07:07:46.084Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestBatchDeleteCustomer","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:46.524Z","caller":"api/response.go:49","msg":"reqeust id: d6ca7c22-45b4-9d75-afc3-fc45c57b6a46, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:46.889Z","caller":"api/response.go:49","msg":"reqeust id: 3decaa05-8356-9985-9318-02aeb79d3ac0, POST https://go.t2.moego.dev/api/customer/withPet","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:47.241Z","caller":"api/response.go:49","msg":"reqeust id: 23205628-63f4-93d5-b95f-820910c0f4a1, DELETE https://go.t2.moego.dev/api/customer/smart-list/deletion","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:47.647Z","caller":"api/response.go:49","msg":"reqeust id: ce265c21-ba2f-9abb-9300-3ef1cb2ac416, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:47.647Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestBatchDeleteCustomer","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestSmartListNoFilter
{"level":"info","ts":"2025-05-22T07:07:47.647Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestSmartListNoFilter","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:48.472Z","caller":"api/response.go:49","msg":"reqeust id: 863f1452-695d-9ca0-b30b-bfc6fc2f0236, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:49.663Z","caller":"api/response.go:49","msg":"reqeust id: 51cedc3b-42ae-943a-b6df-9855c3903873, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:49.663Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestSmartListNoFilter","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestSmartListWithPetTypeFilter
{"level":"info","ts":"2025-05-22T07:07:49.663Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestSmartListWithPetTypeFilter","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:50.118Z","caller":"api/response.go:49","msg":"reqeust id: 0f006936-da14-91c6-8cf0-6dea1404229a, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:50.656Z","caller":"api/response.go:49","msg":"reqeust id: c7f0a4ce-b92b-9289-be90-7a3871fcb353, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:51.643Z","caller":"api/response.go:49","msg":"reqeust id: c341c329-4849-974b-87fb-b001654790a1, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:52.656Z","caller":"api/response.go:49","msg":"reqeust id: ecc0c1ac-1841-91bd-b50a-a2553275051a, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:53.655Z","caller":"api/response.go:49","msg":"reqeust id: b89e1425-c905-9f7c-831f-7f0fe2041843, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:53.655Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestSmartListWithPetTypeFilter","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestSmartListWithQueryEmail
{"level":"info","ts":"2025-05-22T07:07:53.655Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestSmartListWithQueryEmail","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:54.096Z","caller":"api/response.go:49","msg":"reqeust id: a13ceba0-9728-9d27-9ab9-b13b1d28c78d, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:54.672Z","caller":"api/response.go:49","msg":"reqeust id: 21d29ee1-2cda-920a-83f6-2a006b4c9fff, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:54.672Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestSmartListWithQueryEmail","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestSmartListWithQueryName
{"level":"info","ts":"2025-05-22T07:07:54.673Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestSmartListWithQueryName","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:55.113Z","caller":"api/response.go:49","msg":"reqeust id: 5a768f03-b67e-9df4-a6ae-1d1ebab7c545, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:55.669Z","caller":"api/response.go:49","msg":"reqeust id: 6b6cb299-628f-9908-931c-727e5067ccd7, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:55.669Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestSmartListWithQueryName","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestSmartListWithQueryPhone
{"level":"info","ts":"2025-05-22T07:07:55.669Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestSmartListWithQueryPhone","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:56.141Z","caller":"api/response.go:49","msg":"reqeust id: 631019cc-719b-9480-9f4e-0882342eed72, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:56.808Z","caller":"api/response.go:49","msg":"reqeust id: eb5315ce-18f6-94a5-8b85-d87f3695b758, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:56.809Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestSmartListWithQueryPhone","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestSmartListWithTotalAppointmentFilter
{"level":"info","ts":"2025-05-22T07:07:56.809Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestSmartListWithTotalAppointmentFilter","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:57.241Z","caller":"api/response.go:49","msg":"reqeust id: 56061d4b-f26f-9ea0-bf99-89db8588f62c, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:57.664Z","caller":"api/response.go:49","msg":"reqeust id: f5908171-8da9-9385-a0ee-0ea6d67ccf3b, POST https://go.t2.moego.dev/api/customer/smart-list","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:57.665Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestSmartListWithTotalAppointmentFilter","trace_id":9073556982860602826}
=== RUN   TestSmartListTestSuite/TestUpdateInactive
{"level":"info","ts":"2025-05-22T07:07:57.665Z","caller":"customer/smart_list_test.go:111","msg":"Start to run test SmartListTestSuite.TestUpdateInactive","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:58.169Z","caller":"api/response.go:49","msg":"reqeust id: e2f9a413-9d61-9acd-88b8-1803d693594c, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:58.518Z","caller":"api/response.go:49","msg":"reqeust id: a5b4422e-3ece-913c-a78e-382efce0b072, PUT https://go.t2.moego.dev/api/customer/smart-list/inactive","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:58.963Z","caller":"api/response.go:49","msg":"reqeust id: e80d73a8-1a4e-91b6-bef3-06ac15081926, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:59.311Z","caller":"api/response.go:49","msg":"reqeust id: e2d5ee1a-35bb-92f8-ac01-dfa64f4e1d68, PUT https://go.t2.moego.dev/api/customer/smart-list/inactive","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:59.896Z","caller":"api/response.go:49","msg":"reqeust id: 50eee1fc-6c16-9eb5-bdb4-585523e49380, GET https://go.t2.moego.dev/api/customer/overview?customerId=15804127","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:07:59.896Z","caller":"customer/smart_list_test.go:116","msg":"Finish running test SmartListTestSuite.TestUpdateInactive","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:08:00.452Z","caller":"api/response.go:49","msg":"reqeust id: 217e73ea-925e-96c5-a856-ac559c193eb7, DELETE https://go.t2.moego.dev/api/customer","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:08:00.713Z","caller":"api/response.go:49","msg":"reqeust id: d870ac62-53ca-92f8-8d80-5bbf346cc72a, POST https://platform-tools.t2.moego.dev/backend.proto.tools.v1.TestAccountService/ReturnTestAccount","trace_id":9073556982860602826}
{"level":"info","ts":"2025-05-22T07:08:00.713Z","caller":"godomain/context.go:192","msg":"Finish teardown suite","trace_id":9073556982860602826}
--- PASS: TestSmartListTestSuite (18.77s)
    --- PASS: TestSmartListTestSuite/TestBatchAddTag (1.29s)
    --- PASS: TestSmartListTestSuite/TestBatchDeleteCustomer (1.56s)
    --- PASS: TestSmartListTestSuite/TestSmartListNoFilter (2.02s)
    --- PASS: TestSmartListTestSuite/TestSmartListWithPetTypeFilter (3.99s)
    --- PASS: TestSmartListTestSuite/TestSmartListWithQueryEmail (1.02s)
    --- PASS: TestSmartListTestSuite/TestSmartListWithQueryName (1.00s)
    --- PASS: TestSmartListTestSuite/TestSmartListWithQueryPhone (1.14s)
    --- PASS: TestSmartListTestSuite/TestSmartListWithTotalAppointmentFilter (0.86s)
    --- PASS: TestSmartListTestSuite/TestUpdateInactive (2.23s)
PASS
