syntax = "proto3";

package moego.api.reporting.v2;

import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/dashboard_model.proto";
import "moego/models/reporting/v2/diagram_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2;reportingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.reporting.v2";

// DashboardService is the service for reporting dashboards
service DashboardService {
  // QueryDashboardPages returns meta data of dashboard
  rpc QueryDashboardPages(QueryDashboardPagesRequest) returns (QueryDashboardPagesResponse);
  // FetchDashboardData fetches dashboard diagram data
  rpc FetchDashboardData(FetchDashboardDataRequest) returns (FetchDashboardDataResponse);
}

// Describe pages of dashboard
message QueryDashboardPagesRequest {
  // The tabs of query dashboard page
  repeated moego.models.reporting.v2.DashboardPage.Tab tabs = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // 区分不同的 reporting 场景
  optional moego.models.reporting.v2.ReportingScene reporting_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Describe pages of dashboard
message QueryDashboardPagesResponse {
  // The list of dashboard pages
  repeated moego.models.reporting.v2.DashboardPage dashboard_pages = 1;
}

// Describe a request to fetch dashboard diagram data
message FetchDashboardDataRequest {
  // diagram ids
  repeated string diagram_ids = 1 [(validate.rules).repeated.items.string = {
    min_len: 1
    max_len: 100
  }];
  // The business id
  repeated uint64 business_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // current interval
  google.type.Interval current_period = 4 [(validate.rules).message = {required: true}];
  // previous interval
  optional google.type.Interval previous_period = 5;
  // group by field key
  repeated string group_by_field_keys = 6;
  // Filters
  repeated moego.models.reporting.v2.FilterRequest filters = 7;
}

// Describe a response to fetch dashboard diagram data
message FetchDashboardDataResponse {
  // The dashboard diagram data
  repeated moego.models.reporting.v2.DiagramData diagram_data = 1;
  // Report data last synced time
  google.protobuf.Timestamp last_synced_time = 2;
}
