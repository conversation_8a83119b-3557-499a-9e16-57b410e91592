// @since 2024-10-21
// <AUTHOR>

syntax = "proto3";

package moego.api.debugging.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/debugging/v1;debuggingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.debugging.v1";

// ListHeader params
message ListHeaderParams {}

// ListHeader result
message ListHeaderResult {
  // headers
  map<string, string> headers = 1;
}

// DebuggingService provides some API for debugging purpose.
service DebuggingService {
  // 这个接口只有一个作用，就是返回当前请求的所有 header，不做任何其他操作。
  //
  // 它有一些特殊的用途：
  //  - 用于测试测试网关是否正确转发了 header
  //  - 返回经过网关解析之后的 header，方便用于构造请求参数。一个使用场景是通过 curl 快速构造一个 gRPC 请求，用于 api 服务的调试，参考 https://github.com/MoeGolibrary/moego-http-to-rpc/blob/production/cmd/hr/main.go
  //
  // 这个接口只会在测试环境可用，生产环境不可用。
  rpc ListHeader(ListHeaderParams) returns (ListHeaderResult);
}
