// (-- api-linter: core::0131::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0132::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0132::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)

syntax = "proto3";

package moego.api.payment.v2;

import "google/type/interval.proto";
import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/payment_enums.proto";
import "moego/models/payment/v2/payment_models.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/payment/v2;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.payment.v2";

// Payment service.
service PaymentService {
  // 获取支付版本
  rpc GetPaymentVersion(GetPaymentVersionParams) returns (GetPaymentVersionResult);

  // GetPayData 获取支付数据，用于前端加载第三方支付组件
  rpc GetPayData(GetPayDataParams) returns (GetPayDataResult);
  // submit action detail
  rpc SubmitActionDetail(SubmitActionDetailParams) returns (SubmitActionDetailResult);
  // 查询payment
  rpc GetPayment(GetPaymentParams) returns (GetPaymentResult);
  // 获取支付列表
  rpc ListPayment(ListPaymentParams) returns (ListPaymentResult);

  // 添加绑定的支付方式
  rpc AddRecurringPaymentMethod(AddRecurringPaymentMethodParams) returns (AddRecurringPaymentMethodResult);
  // 删除绑定的支付方式
  rpc DeleteRecurringPaymentMethod(DeleteRecurringPaymentMethodParams) returns (DeleteRecurringPaymentMethodResult);
  // 将payment method设置为primary
  rpc SetRecurringPaymentMethodPrimary(SetRecurringPaymentMethodPrimaryParams) returns (SetRecurringPaymentMethodPrimaryResult);
  // 获取用户所有绑定的支付方式
  rpc ListRecurringPaymentMethods(ListRecurringPaymentMethodsParams) returns (ListRecurringPaymentMethodsResult);
}

// get payment version params
message GetPaymentVersionParams {}

// get payment version result
message GetPaymentVersionResult {
  // 支付版本
  models.payment.v2.PaymentVersion payment_version = 1;
  // 渠道
  models.payment.v2.ChannelType channel_type = 2;
}

// get pay data params
message GetPayDataParams {
  // 渠道，可不传，将由后端根据渠道路由自行决定；如果传了，优先级高于后端路由
  optional models.payment.v2.ChannelType channel_type = 1;
}

// get pay data result
message GetPayDataResult {
  // adyen data
  message AdyenData {
    // data
    string data = 1;
  }

  // 渠道
  models.payment.v2.ChannelType channel_type = 1;
  // 支付数据
  oneof data {
    // adyen data
    AdyenData adyen_data = 2;
  }
}

// submit action detail params
message SubmitActionDetailParams {
  // 渠道
  models.payment.v2.ChannelType channel_type = 1;
  // Action Result，前端从组件拿到的原始数据，
  // e.g. ayden 3ds2:
  // `{
  //   "details": {
  //     "threeDSResult": "eyJ0cmFuc1N0YXR1cyI6IlkifQ=="
  //   }
  // }`
  string raw_action_result = 2;
}

// submit action result
message SubmitActionDetailResult {
  // msg 可以展示给用户的信息
  optional string msg = 1;
  // channel response，渠道返回的原始数据，用于前端加载第三方支付组件,
  // e.g. adyen:
  // `{
  //   "resultCode": "Authorised",
  //   "pspReference": "V4HZ4RBFJGXXGN82"
  // }`
  string raw_channel_response = 2;
}

// get payment params
message GetPaymentParams {
  // 支付单据id
  int64 payment_id = 1;
}

// get payment result
message GetPaymentResult {
  // payment view
  models.payment.v2.PaymentView payment = 1;
}

// 查询payment列表请求参数
message ListPaymentParams {
  // 分页查询请求
  utils.v2.PaginationRequest pagination_request = 1;
  // filter
  message Filter {
    // customer id
    repeated int64 customer_ids = 1;
    // order id
    repeated int64 order_ids = 2;
    // order payment id
    repeated int64 order_payment_ids = 3;
    // payment id
    repeated int64 payment_ids = 4;
    // 查询时间范围
    optional google.type.Interval time_range = 5;
  }
  // filter
  Filter filter = 2;
}

// list payment result
message ListPaymentResult {
  // 支付列表
  repeated models.payment.v2.PaymentView payments = 1;
  // 分页
  utils.v2.PaginationResponse pagination_request = 2;
}

// add recurring payment method params
message AddRecurringPaymentMethodParams {
  // customer id
  optional int64 customer_id = 1;
  // customer code
  optional string encrypted_customer_id = 2;
  // channel type
  optional models.payment.v2.ChannelType channel_type = 3;
  // payment method type，目前只能是 Card
  models.payment.v2.PaymentMethod.MethodType payment_method_type = 4;
  // 要存储的支付方式
  models.payment.v2.PaymentMethod.Detail detail = 5;
  // 透传参数，一般是用户自定义的额外信息
  optional models.payment.v2.RecurringPaymentMethodModel.Extra extra = 6;
}

// add recurring payment method result
message AddRecurringPaymentMethodResult {
  // 已存储的 payment method，如果发生了 3ds 验证的话不会返回
  optional models.payment.v2.RecurringPaymentMethodView recurring_payment_method_view = 1;
  // 渠道返回的原始数据，用于 3ds 验证等
  string channel_response = 2;
}

// delete recurring payment method params
message DeleteRecurringPaymentMethodParams {
  // 存储的 payment method id
  int64 payment_method_id = 1;
}

// delete recurring payment method result
message DeleteRecurringPaymentMethodResult {}

// set recurring payment method primary params
message SetRecurringPaymentMethodPrimaryParams {
  // 存储的payment method id
  int64 payment_method_id = 1;
}

// set recurring payment method primary result
message SetRecurringPaymentMethodPrimaryResult {
  // 已存储的 payment method
  models.payment.v2.RecurringPaymentMethodView recurring_payment_method_view = 1;
}

// list recurring payment method params
message ListRecurringPaymentMethodsParams {
  // 用户id
  int64 customer_id = 1;
}

// list recurring payment method result
message ListRecurringPaymentMethodsResult {
  // 已存储的 payment method 列表
  repeated models.payment.v2.RecurringPaymentMethodView recurring_payment_method_views = 1;
}
