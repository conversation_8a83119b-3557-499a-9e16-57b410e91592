syntax = "proto3";

package moego.models.organization.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/organization/v1/location_models.proto";
import "moego/models/organization/v1/staff_enums.proto";
import "moego/utils/v1/time_of_day_interval.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// staff model
message StaffModel {
  // Status
  enum Status {
    // unspecified
    STAFF_STATUS_UNSPECIFIED = 0;
    // normal
    NORMAL = 1;
    // deleted
    DELETED = 2;
    // migrated
    MIGRATED = 3;
    // temporary
    TEMPORARY = 4;
    //
  }
  // staff id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // account id
  int64 account_id = 3;
  // avatar path
  string avatar_path = 4;
  // is deleted
  bool is_deleted = 5;
  // is available for book online
  bool is_book_online_available = 6;
  // is show on calendar
  bool is_show_on_calendar = 7;
  // first name
  string first_name = 8;
  // last name
  string last_name = 9;
  // company id
  int64 company_id = 10;
  // employee category
  models.organization.v1.StaffEmployeeCategory employee_category = 11;
  // enterprise id
  int64 enterprise_id = 12;
  // role id
  int64 role_id = 13;
  // hire date
  int64 hire_date = 14;
  // note
  string note = 15;
  // color code
  string color_code = 16;
  // last visit business id
  int64 last_visit_business_id = 17;
  // invite code
  string invite_code = 18;
  // access all working locations staffs
  bool access_all_working_locations_staffs = 19;
  // profile email
  string profile_email = 20;
  // sort
  int32 sort = 21;
  // access code
  string access_code = 22;
  // require access code
  bool require_access_code = 23;
  // status
  Status status = 24;
  // update time
  google.protobuf.Timestamp update_time = 25;
  // staff source
  models.organization.v1.StaffSource source = 26;
  // preserved num before 50 for future use
  // working locations
  repeated models.organization.v1.LocationBriefView working_location_list = 50;
  // phone number
  string phone_number = 51;
}

// staff model brief view
message StaffBriefView {
  // staff id
  int64 id = 1;
  // account id
  int64 account_id = 2;
  // company id
  int64 company_id = 3;
  // employee category
  models.organization.v1.StaffEmployeeCategory employee_category = 4;
  // enterprise id
  int64 enterprise_id = 5;
}

// view for staff list, with basic info for staff
message StaffBasicView {
  // staff id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // account id
  int64 account_id = 3;
  // avatar path
  string avatar_path = 4;
  // is deleted
  bool is_deleted = 5;
  // is available for book online
  bool is_book_online_available = 6;
  // is show on calendar
  bool is_show_on_calendar = 7;
  // first name
  string first_name = 8;
  // last name
  string last_name = 9;
  // company id
  int64 company_id = 10;
  // employee category
  models.organization.v1.StaffEmployeeCategory employee_category = 11;
  // enterprise id
  int64 enterprise_id = 12;
  // role id
  int64 role_id = 13;
  // hire date
  int64 hire_date = 14;
  // note
  string note = 15;
  // color code
  string color_code = 16;
  // last visit business id
  int64 last_visit_business_id = 17;
  // invite code
  string invite_code = 18;
  // access all working locations staffs
  bool access_all_working_locations_staffs = 19;
  // profile email
  string profile_email = 20;
  // sort
  int32 sort = 21;
  // access code
  string access_code = 22;
  // require access code
  bool require_access_code = 23;
  // phone number
  string phone_number = 24;
}

// staff login time model
message StaffLoginTimeModel {
  // login limit type
  StaffLoginLimitType login_limit_type = 1;
  // accurate to the minute now
  utils.v1.TimeOfDayInterval time_range = 2;
}
