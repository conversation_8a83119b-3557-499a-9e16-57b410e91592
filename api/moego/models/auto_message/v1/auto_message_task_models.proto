// @since 2024-06-05 11:24:51
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.auto_message.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/auto_message/v1/auto_message_task_enums.proto";
import "moego/models/message/v1/message_template_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.auto_message.v1";

// auto message task model
message AutoMessageTaskModel {
  // the unique id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // auto message config id
  int64 auto_msg_config_id = 4;
  // object id
  int64 object_id = 5;
  // object type
  AutoMessageTaskObjectType object_type = 6;
  // receiver id
  int64 receiver_id = 7;
  // receiver type
  AutoMessageTaskReceiverType receiver_type = 8;
  // send time
  google.protobuf.Timestamp send_time = 9;
  // status
  AutoMessageTaskStatus status = 10;
  // use case
  moego.models.message.v1.MessageTemplateUseCase use_case = 11;
  // version
  int32 version = 12;
}
