syntax = "proto3";

package moego.models.order.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// Refund Order 的退款模式.
enum RefundMode {
  // 未指定模式.
  REFUND_MODE_UNSPECIFIED = 0;
  // 基于 Order Item 进行退款.
  REFUND_MODE_BY_ITEM = 1;
  // 基于 Payment 进行退款.
  REFUND_MODE_BY_PAYMENT = 2;
}

// Refund Order 的状态.
enum RefundOrderStatus {
  // 未定义状态.
  REFUND_ORDER_STATUS_UNSPECIFIED = 0;
  // Refund Order 经创建.
  REFUND_ORDER_STATUS_CREATED = 100;
  // Refund Order 退款交易已经发起.
  REFUND_ORDER_STATUS_TRANSACTION_CREATED = 200;
  // 终态 - 已退款.
  REFUND_ORDER_STATUS_COMPLETED = 300;
}

// Refund Order Item 的退款模式.
enum RefundItemMode {
  // 未指定模式.
  REFUND_ITEM_MODE_UNSPECIFIED = 0;
  // 按照数量进行退款.
  REFUND_ITEM_MODE_BY_QUANTITY = 1;
  // 按照金额进行退款.
  REFUND_ITEM_MODE_BY_AMOUNT = 2;
}

// Refund Order Payment 的状态.
enum RefundOrderPaymentStatus {
  // 未指定状态.
  REFUND_ORDER_PAYMENT_STATUS_UNSPECIFIED = 0;
  // 已创建.
  REFUND_ORDER_PAYMENT_STATUS_CREATED = 100;
  // 退款交易已发起.
  REFUND_ORDER_PAYMENT_STATUS_TRANSACTION_CREATED = 200;
  // 终态 - 已退款.
  REFUND_ORDER_PAYMENT_STATUS_REFUNDED = 300;
  // 终态 - 退款失败.
  REFUND_ORDER_PAYMENT_STATUS_FAILED = 400;
  // 终态 - 退款取消.
  REFUND_ORDER_PAYMENT_STATUS_CANCELED = 500;
}
