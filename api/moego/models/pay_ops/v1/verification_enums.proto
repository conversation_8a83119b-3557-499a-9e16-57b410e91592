// @since 2-24-01-02
// <AUTHOR> <<EMAIL>>
syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

// stripe company struct
enum StripeCompanyStruct {
  // Represents an unspecified or unknown company structure.
  COMPANY_STRUCT_UNSPECIFIED = 0;

  // Represents a free zone establishment, a type of business setup in a free zone area.
  FREE_ZONE_ESTABLISHMENT = 1;

  // Represents a free zone limited liability company.
  FREE_ZONE_LLC = 2;

  // Represents a government instrumentality, a type of government agency.
  GOVERNMENT_INSTRUMENTALITY = 3;

  // Represents a governmental unit or entity.
  GOVERNMENTAL_UNIT = 4;

  // Represents a non-profit organization that is incorporated.
  INCORPORATED_NON_PROFIT = 5;

  // Represents a limited liability partnership, a partnership in which some or all partners have limited liabilities.
  LIMITED_LIABILITY_PARTNERSHIP = 6;

  // Represents a limited liability company.
  LLC = 7;

  // Represents a multi-member limited liability company.
  MULTI_MEMBER_LLC = 8;

  // Represents a privately held company.
  PRIVATE_COMPANY = 9;

  // Represents a private corporation, a corporation owned by private stakeholders.
  PRIVATE_CORPORATION = 10;

  // Represents a private partnership, a business owned by private individuals.
  PRIVATE_PARTNERSHIP = 11;

  // Represents a publicly held company.
  PUBLIC_COMPANY = 12;

  // Represents a public corporation, a corporation whose shares are publicly traded.
  PUBLIC_CORPORATION = 13;

  // Represents a public partnership, a partnership with public involvement.
  PUBLIC_PARTNERSHIP = 14;

  // Represents a single-member limited liability company.
  SINGLE_MEMBER_LLC = 15;

  // Represents a sole establishment, a business owned and operated by one person.
  SOLE_ESTABLISHMENT = 16;

  // Represents a sole proprietorship, a business owned and operated by a single person.
  SOLE_PROPRIETORSHIP = 17;

  // Represents a tax-exempt government instrumentality.
  TAX_EXEMPT_GOVERNMENT_INSTRUMENTALITY = 18;

  // Represents an unincorporated association, a group organized for a common purpose.
  UNINCORPORATED_ASSOCIATION = 19;

  // Represents an unincorporated non-profit organization.
  UNINCORPORATED_NON_PROFIT = 20;
}

// stripe business type
enum StripeBusinessType {
  // Unspecified
  BUSINESS_TYPE_UNSPECIFIED = 0;

  // Represents a company or corporate entity.
  COMPANY = 1;

  // Represents a government entity or public sector organization.
  GOVERNMENT_ENTITY = 2;

  // Represents an individual, as opposed to a company or organization.
  INDIVIDUAL = 3;

  // Represents a non-profit organization or a not-for-profit entity.
  NON_PROFIT = 4;
}
