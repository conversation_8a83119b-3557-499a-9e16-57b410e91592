syntax = "proto3";

package moego.service.online_booking.v1;

import "moego/models/online_booking/v1/grooming_auto_assign_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create grooming auto assign request
message CreateGroomingAutoAssignRequest {
  // The id of grooming auto assign record
  int64 booking_request_id = 2 [(validate.rules).int64.gt = 0];
  // The id of auto assign staff
  optional int64 staff_id = 3 [(validate.rules).int64.gt = 0];
  // The appointment time of auto assign, unit minute, 540 means 09:00
  optional int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Create grooming auto assign response
message CreateGroomingAutoAssignResponse {
  // The id grooming auto assign record
  int64 id = 1;
}

// Update grooming auto assign request
message UpdateGroomingAutoAssignRequest {
  // The id of grooming auto assign record
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // The id of auto assign staff
  optional int64 staff_id = 3 [(validate.rules).int64.gt = 0];
  // The appointment time of auto assign, unit minute, 540 means 09:00
  optional int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Update grooming auto assign response
message UpdateGroomingAutoAssignResponse {}

// Upsert grooming auto assign request
message UpsertGroomingAutoAssignRequest {
  // The id of booking request
  int64 booking_request_id = 2 [(validate.rules).int64.gt = 0];
  // auto assign staff id
  optional int64 staff_id = 3 [(validate.rules).int64.gt = 0];
  // auto assign appointment time, unit minute, 540 means 09:00
  optional int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Upsert grooming auto assign response
message UpsertGroomingAutoAssignResponse {
  // Grooming auto assign record
  models.online_booking.v1.GroomingAutoAssignModel auto_assign = 1;
}

// GroomingAutoAssign service
service GroomingAutoAssignService {
  // Create grooming auto assign record.
  rpc CreateGroomingAutoAssign(CreateGroomingAutoAssignRequest) returns (CreateGroomingAutoAssignResponse) {}
  // Update grooming auto assign record by id.
  rpc UpdateGroomingAutoAssign(UpdateGroomingAutoAssignRequest) returns (UpdateGroomingAutoAssignResponse) {}
  // Insert or update grooming auto assign record.
  rpc UpsertGroomingAutoAssign(UpsertGroomingAutoAssignRequest) returns (UpsertGroomingAutoAssignResponse) {}
}
