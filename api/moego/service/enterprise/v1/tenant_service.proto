syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/enterprise/v1/tenant_defs.proto";
import "moego/models/enterprise/v1/tenant_group_mapping_models.proto";
import "moego/models/enterprise/v1/tenant_group_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "moego/models/enterprise/v1/tenant_template_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// request to get enterprise
message GetTenantRequest {
  // tenant id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// response of get enterprise
message GetTenantResponse {
  // enterprise
  moego.models.enterprise.v1.TenantModel tenant = 1;
}

// request to create enterprise
message CreateTenantRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
  // related company id
  int64 related_company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // tenant
  models.enterprise.v1.CreateTenantDef tenant = 4;
  // status
  models.enterprise.v1.TenantModel.Status status = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// response of create enterprise
message CreateTenantResponse {
  // enterprise
  moego.models.enterprise.v1.TenantModel tenant = 1;
}

// request to update enterprise
message UpdateTenantRequest {
  // tenant id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // account id (change tenant binding account id)
  optional int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  optional int64 enterprise_id = 3 [(validate.rules).int64 = {gt: 0}];
  // tenant
  optional models.enterprise.v1.UpdateTenantDef tenant = 4;
  // status
  optional models.enterprise.v1.TenantModel.Status status = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// response of update enterprise
message UpdateTenantResponse {
  // enterprise
  moego.models.enterprise.v1.TenantModel tenant = 1;
}

// list tenant request
message ListTenantRequest {
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // enterprise id
  int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
  // order by
  optional moego.utils.v2.OrderBy order_by = 3;
  // filter
  message Filter {
    // group ids
    repeated int64 group_ids = 1;
    // statuses, default exclude deleted
    repeated models.enterprise.v1.TenantModel.Status statuses = 2;
    // keyword
    optional string keyword = 3;
    // ids
    repeated int64 ids = 4;
    // types
    repeated models.enterprise.v1.TenantModel.Type types = 5;
  }
  // filter
  optional Filter filter = 4;
}

// list tenant response
message ListTenantResponse {
  // tenant list
  repeated moego.models.enterprise.v1.TenantModel tenants = 1;
  // territory model
  repeated models.enterprise.v1.TenantTemplateModel tenant_templates = 2;
  // tenant group
  repeated moego.models.enterprise.v1.TenantGroupModel tenant_groups = 3;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 4;
}

// create tenant group params
message CreateTenantGroupRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // group name
  string group_name = 2 [(validate.rules).string = {max_len: 50}];
}

// create tenant group result
message CreateTenantGroupResponse {
  // tenant groups
  models.enterprise.v1.TenantGroupModel tenant_groups = 1;
}

// delete tenant group params
message DeleteTenantGroupRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // group id
  int64 group_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// delete tenant group result
message DeleteTenantGroupResponse {}

// delete tenant request
message DeleteTenantRequest {
  // tenant id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete tenant result
message DeleteTenantResponse {}

// list tenant group params
message ListTenantGroupRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter
  message Filter {
    // group ids
    repeated int64 group_ids = 1;
  }
  // filter
  Filter filter = 2;
}

// list tenant group result
message ListTenantGroupResponse {
  // tenant groups
  repeated models.enterprise.v1.TenantGroupModel tenant_groups = 1;
}

// modify tenant group mapping params
message ModifyTenantGroupMappingRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // tenant id
  int64 tenant_id = 2 [(validate.rules).int64 = {gt: 0}];
  // group ids
  repeated int64 group_ids = 3;
}

// modify tenant group mapping result
message ModifyTenantGroupMappingResponse {}

// get tenant group mapping params
message ListTenantGroupMappingRequest {
  // filter
  message Filter {
    // tenant id
    repeated int64 tenant_ids = 1;
    // group id
    repeated int64 group_ids = 2;
  }
  // filter
  Filter filter = 1;
}

// get tenant group mapping result
message ListTenantGroupMappingResponse {
  // group ids
  repeated models.enterprise.v1.TenantGroupMappingModel tenant_group_mapping = 1;
}

// batch create tenant group request
message BatchCreateTenantGroupRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // group names
  repeated string group_names = 2 [(validate.rules).repeated = {
    max_items: 500
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];
}

// batch create tenant group response
message BatchCreateTenantGroupResponse {
  // tenant groups
  repeated models.enterprise.v1.TenantGroupModel tenant_groups = 1;
}

// get tenant by company id
message GetTenantByCompanyIdRequest {
  // tenant id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// tenant service
service TenantService {
  // get tenant by id
  rpc GetTenant(GetTenantRequest) returns (GetTenantResponse) {}
  // create tenant
  rpc CreateTenant(CreateTenantRequest) returns (CreateTenantResponse) {}
  // update tenant
  rpc UpdateTenant(UpdateTenantRequest) returns (UpdateTenantResponse) {}
  // get tenant list
  rpc ListTenant(ListTenantRequest) returns (ListTenantResponse);
  // delete tenant
  rpc DeleteTenant(DeleteTenantRequest) returns (DeleteTenantResponse);
  // create tenant group
  rpc CreateTenantGroup(CreateTenantGroupRequest) returns (CreateTenantGroupResponse);
  // list tenant groups
  rpc ListTenantGroups(ListTenantGroupRequest) returns (ListTenantGroupResponse);
  // delete tenant group
  rpc DeleteTenantGroup(DeleteTenantGroupRequest) returns (DeleteTenantGroupResponse);
  // modify tenant group mapping
  rpc ModifyTenantGroupMapping(ModifyTenantGroupMappingRequest) returns (ModifyTenantGroupMappingResponse);
  // get tenant group mapping
  rpc ListTenantGroupMapping(ListTenantGroupMappingRequest) returns (ListTenantGroupMappingResponse);
  // batch create tenant group
  rpc BatchCreateTenantGroup(BatchCreateTenantGroupRequest) returns (BatchCreateTenantGroupResponse);
  // get tenant by company id
  rpc GetTenantByCompanyId(GetTenantByCompanyIdRequest) returns (GetTenantResponse);
}
