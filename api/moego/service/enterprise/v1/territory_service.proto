syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/enterprise/v1/territory_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// GetTerritoryRequest
message GetTerritoryRequest {
  // territory id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// GetTerritoryResponse
message GetTerritoryResponse {
  // territory
  models.enterprise.v1.TerritoryModel territory = 1;
}

// CreateTerritoryRequest
message CreateTerritoryRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];
  // zip code
  repeated string zip_codes = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];
  // territory type
  moego.models.enterprise.v1.TerritoryModel.Type territory_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// CreateTerritoryResponse
message CreateTerritoryResponse {
  // territory
  models.enterprise.v1.TerritoryModel territory = 1;
}

// UpdateTerritoryRequest
message UpdateTerritoryRequest {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // enterprise id
  optional int64 enterprise_id = 2 [(validate.rules).int64.gt = 0];
  // zip code
  repeated string zip_codes = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];
  // territory type
  optional moego.models.enterprise.v1.TerritoryModel.Type type = 4;
  // territory status
  optional moego.models.enterprise.v1.TerritoryModel.Status status = 5;
}

// UpdateTerritoryResponse
message UpdateTerritoryResponse {
  // territory
  models.enterprise.v1.TerritoryModel territory = 1;
}

// ListTerritoryRequest
message ListTerritoryRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];
  // filter
  message Filter {
    // territory ids
    repeated int64 territory_ids = 1;
    // type
    repeated moego.models.enterprise.v1.TerritoryModel.Type types = 2;
    // statuses
    repeated moego.models.enterprise.v1.TerritoryModel.Status statuses = 3;
  }
  // filter
  optional Filter filter = 2;
}

// ListTerritoryResponse
message ListTerritoryResponse {
  // territories
  repeated models.enterprise.v1.TerritoryModel territories = 1;
}

// delete territory params
message DeleteTerritoryRequest {
  // territory id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2 [(validate.rules).int64.gt = 0];
}

// delete territory result
message DeleteTerritoryResponse {}

// tenant service
service TerritoryService {
  // get territory by id
  rpc GetTerritory(GetTerritoryRequest) returns (GetTerritoryResponse) {}
  // create territory
  rpc CreateTerritory(CreateTerritoryRequest) returns (CreateTerritoryResponse) {}
  // update territory
  rpc UpdateTerritory(UpdateTerritoryRequest) returns (UpdateTerritoryResponse) {}
  // get tenant territory
  rpc ListTerritory(ListTerritoryRequest) returns (ListTerritoryResponse);
  // delete territory
  rpc DeleteTerritory(DeleteTerritoryRequest) returns (DeleteTerritoryResponse);
}
