package com.moego.server.message.dto;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.server.message.params.notification.NotificationParams;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationClientUpdatePetParams extends NotificationParams {
    private String title = "Pet info updated";
    private String body = "";
    private String type = NotificationEnum.TYPE_ACTIVITY_CLIENT_UPDATE_PET;
    private Boolean isNotifyBusinessOwner = true;
    private Boolean isAppointmentRelated = false;

    private Data webPushDto = Data.builder().build();

    @Builder(toBuilder = true)
    public record Data(
            Integer petId,
            String petName,
            Integer customerId,
            String customerFirstName,
            String customerLastName,
            Long updateTime) {}
}
