/*
 * @since 2023-07-06 16:49:13
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.message.vo;

import com.moego.common.utils.Pagination;
import jakarta.annotation.Nonnull;
import java.util.Set;
import lombok.Builder;

@Builder(toBuilder = true)
public record DescribeMassTextsVO(
        Set<Integer> ids,
        Set<Integer> businessIds,
        Set<Integer> staffIds,
        boolean includeDeleted,
        @Nonnull Pagination pagination) {}
