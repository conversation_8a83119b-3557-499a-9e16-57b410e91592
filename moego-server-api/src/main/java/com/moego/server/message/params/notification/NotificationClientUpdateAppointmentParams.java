package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * After the client updates the appointment, notify the business.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationClientUpdateAppointmentParams extends NotificationParams {
    private String title = "Appointment updated";
    private String body = "";
    private String type = NotificationEnum.TYPE_ACTIVITY_CLIENT_UPDATE_APPOINTMENT;
    private Boolean isNotifyBusinessOwner = true;
    private Boolean isAppointmentRelated = true;

    private Data webPushDto = Data.builder().build();

    @Builder(toBuilder = true)
    public record Data(
            Integer groomingId,
            String appointmentDate,
            Integer appointmentStartTime,
            Integer appointmentEndTime,
            Integer customerId,
            String customerFirstName,
            String customerLastName) {}
}
