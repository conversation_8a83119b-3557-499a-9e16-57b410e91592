package com.moego.server.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-10-08 03:13
 */
@Data
public class MoeBusinessReviewBoosterCountDTO {

    private Integer sendTotal;
    private Integer reviewTotal;
    private BigDecimal sumScore;
    private BigDecimal avgScore;

    @Schema(description = "SMS 来源的 review 数量")
    private Integer smsReviewCount;

    @Schema(description = "grooming report 来源的 review 数量")
    private Integer grReviewCount;

    @Schema(description = "pet parent portal 来源的 review 数量")
    private Integer pppReviewCount;
}
