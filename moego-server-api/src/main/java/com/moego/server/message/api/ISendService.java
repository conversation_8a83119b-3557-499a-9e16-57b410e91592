package com.moego.server.message.api;

import com.microtripit.mandrillapp.lutung.view.MandrillMessageStatus;
import com.moego.common.response.ResponseResult;
import com.moego.server.business.dto.TaskBusinessInfoDto;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.params.SendMsgParams;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2020-06-21 11:29
 */
public interface ISendService {
    /**
     * 账号发送邮件
     *
     * @param accountId
     * @param verifyCode
     * @return
     */
    @GetMapping("/service/message/send/account/email/verifyCode")
    ResponseResult<MandrillMessageStatus[]> sendAccountEmailVerifyCode(
            @RequestParam("accountId") Integer accountId, @RequestParam("verifyCode") String verifyCode);

    @Deprecated
    @PostMapping("/service/message/send/customer/account/email/verifyCode")
    Boolean sendClientAccountVerifyCode(
            @RequestParam("email") String email,
            @RequestParam("receivedName") String receivedName,
            @RequestParam("verifyCode") String verifyCode);

    /**
     * 发送邮件导出
     * 期望的参数
     * 1、businessId(获取business owner的email和businessName、businessId三个数据，放置在email正文内)
     * 2、接收方email(固定email)
     * 3、dataImportUrl()
     *
     * @param dataImportUrl
     * @return
     */
    @GetMapping("/service/message/send/account/email/dataImport")
    ResponseResult<MandrillMessageStatus[]> sendAccountEmailDataImport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("toEmail") String toEmail,
            @RequestParam("dataImportUrl") String dataImportUrl);

    @PostMapping("/service/message/send/reminder/notification")
    void reminderNotificationForBatchBusinessInfo(@RequestBody List<TaskBusinessInfoDto> request);

    @PostMapping("/service/message/send/business/daily/time")
    ResponseResult<String> sendBusinessDailyMailTime();

    /**
     * 发送消息并且保存消息
     *
     * @param tokenBusinessId
     * @param tokenStaffId
     * @param sendMessagesParams
     * @return
     */
    @PostMapping("/service/message/send/services/message/save")
    ResponseResult<String> sendMessageToCustomerAndSaveMessage(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam(value = "tokenStaffId", required = false) Integer tokenStaffId,
            @RequestBody SendMessagesParams sendMessagesParams);

    /**
     * 发送单条消息
     * 本接口提供发送一条消息最少的业务处理，包括扣费、保存记录、执行发送等动作。
     * 其他的消息处理结果请在参数中指定，例如发送者和接受者的信息，消息体是如何组装的，由调用者处理。
     * 即：本接口提供的是对已经组装好的消息的直接发送处理过程，当前仅作为内部接口使用。
     * @param sendMsgParams 已经组装好的消息参数结构：包括发送消息的所有必要参数，参见：SendMsgParams 结构定义。
     * @return 发送成功返回状态码：200，失败返回状态码和对应的错误信息。
     */
    @PostMapping("/service/message/send/directly/sendMessage")
    ResponseResult<Integer> sendMessage(@RequestBody SendMsgParams sendMsgParams);

    /**
     * 对新产生的 abandon 记录发送 notification。
     *
     * @return dummy value, always true
     */
    @PostMapping("/service/message/send/sendNotificationForNewAbandonedBookingsV2")
    boolean sendNotificationForNewAbandonedBookingsV2();

    /**
     * Send renew end date notification.
     *
     * @see <a href="https://moego.atlassian.net/jira/software/c/projects/ERP/issues/ERP-6284">ERP-6284</a>
     */
    @PostMapping("/service/message/send/renewEndDateNotification")
    Boolean sendRenewEndDateNotification();
}
