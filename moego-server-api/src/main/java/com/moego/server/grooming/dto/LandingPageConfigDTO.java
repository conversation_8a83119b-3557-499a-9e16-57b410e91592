package com.moego.server.grooming.dto;

import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class LandingPageConfigDTO {

    private Integer id;
    private Integer businessId;
    private String amenities;
    private String showcaseBeforeImage;
    private String showcaseAfterImage;
    private String themeColor;
    private String urlDomainName;
    private Boolean isPublished;
    private String gaMeasurementId;
    private String pageComponents;
    private Date createTime;
    private Date updateTime;
    private String aboutUs;
    private String welcomePageMessage;
}
