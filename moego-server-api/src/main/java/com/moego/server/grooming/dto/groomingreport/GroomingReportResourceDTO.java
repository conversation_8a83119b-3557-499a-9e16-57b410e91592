package com.moego.server.grooming.dto.groomingreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GroomingReportResourceDTO {

    @JsonIgnore
    private Integer reportId;

    private String themeCode;

    @Schema(description = "当前主题下的封面 url")
    private String coverUrl;
}
