package com.moego.server.grooming.api;

import com.moego.api.common.Range;
import com.moego.common.dto.AddressLatLng;
import com.moego.common.dto.PageDTO;
import com.moego.common.dto.clients.BusinessClientsDTO;
import com.moego.common.dto.clients.BusinessDateClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.response.ResponseResult;
import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.server.grooming.dto.AppointmentListDto;
import com.moego.server.grooming.dto.AppointmentReminderSendDTO;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.BusinessUpcomingDTO;
import com.moego.server.grooming.dto.CustomerAppointmentNumInfoDTO;
import com.moego.server.grooming.dto.CustomerAppointmentWithAmountList;
import com.moego.server.grooming.dto.CustomerApptCountDTO;
import com.moego.server.grooming.dto.CustomerApptDateDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerLastFinishedApptMapDto;
import com.moego.server.grooming.dto.CustomerLastServiceDTO;
import com.moego.server.grooming.dto.CustomerNextLastApptMapDto;
import com.moego.server.grooming.dto.CustomerRebookReminderDTO;
import com.moego.server.grooming.dto.CustomerUpComingAppointDTO;
import com.moego.server.grooming.dto.ExpiryCustomerCountListDto;
import com.moego.server.grooming.dto.GroomingBookingDTO;
import com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO;
import com.moego.server.grooming.dto.InitDataGroomingResultDto;
import com.moego.server.grooming.dto.MoeGroomingAppointmentDTO;
import com.moego.server.grooming.dto.PetLastServiceDTO;
import com.moego.server.grooming.dto.waitlist.WaitlistAvailableDTO;
import com.moego.server.grooming.params.AdminQueryAppointmentParams;
import com.moego.server.grooming.params.AppointReminderParams;
import com.moego.server.grooming.params.AppointmentReminderSendParams;
import com.moego.server.grooming.params.ByCustomerIdsParam;
import com.moego.server.grooming.params.CancelParams;
import com.moego.server.grooming.params.ClientCustomerUpcomingParams;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.ConfirmParams;
import com.moego.server.grooming.params.CustomerDeleteParams;
import com.moego.server.grooming.params.DateRangeParams;
import com.moego.server.grooming.params.GetInvoiceIdsAppointmentDateBetweenParam;
import com.moego.server.grooming.params.GroomingInitDataParam;
import com.moego.server.grooming.params.QueryCustomerApptNumPara;
import com.moego.server.grooming.params.QueryReminderRebookParams;
import com.moego.server.grooming.params.QuerySpecificSupportReminderRebookParams;
import com.moego.server.grooming.params.QueryUpcomingApptParams;
import com.moego.server.grooming.params.RepeatReminderParams;
import com.moego.server.grooming.params.status.StatusUpdateParams;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Builder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGroomingAppointmentService {

    /**
     * List grooming appointment by ids.
     *
     * @param ids appointment ids
     * @return appointment list
     */
    @PostMapping("/service/grooming/appointments/list")
    List<AppointmentDTO> list(@RequestBody Collection<Integer> ids);

    /**
     * 用于商户创建，初始化customer模块数据
     *
     * @param initDataParam
     * @return
     */
    @PostMapping("/service/grooming/initData")
    InitDataGroomingResultDto groomingInitData(@RequestBody GroomingInitDataParam initDataParam);

    /**
     * 初始化datarule
     *
     * @param businessId
     */
    @PostMapping("/service/grooming/initDataRule")
    void initDataRule(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/grooming/queryNeedSendCalendarReminderAppt")
    List<GroomingBookingDTO> queryNeedSendCalendarReminderAppt(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("staffId") Integer staffId,
            @RequestParam("beforeMins") Integer beforeMins);

    @GetMapping("/service/grooming/query/withDetail")
    AppointmentReminderSendDTO queryAppointmentWithDetail(
            @RequestParam("businessId") Integer businessId, @RequestParam("groomingId") Integer groomingId);

    @PostMapping("/service/grooming/expiry/list")
    ExpiryCustomerCountListDto queryExpiryCustomerList(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("upcomingNum") Integer upcomingNum,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize,
            @RequestBody List<Integer> dismissIds);

    @PostMapping("/service/grooming/expiry/list/count")
    Integer queryExpiryCustomerListCount(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("upcomingNum") Integer upcomingNum,
            @RequestBody List<Integer> dismissIds);

    @PostMapping("/service/grooming/upcoming")
    List<Integer> queryBusinessUpcomingApptIdList(@RequestBody QueryUpcomingApptParams params);

    @GetMapping("/service/grooming/customer/upcoming")
    List<Integer> queryCustomerIdUpcomingApptIdList(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * TODO(account structure): company 迁移后，查 company 维度的数据
     *
     * Count upcoming appointments by customer ids.
     *
     * @param param {@link ByCustomerIdsParam}
     * @return key: customerId, value: count
     */
    @PostMapping("/service/grooming/upcoming-appointments/count")
    Map<Integer, Integer> countUpcomingApptByCustomerIds(@RequestBody ByCustomerIdsParam param);

    /**
     * DONE: account structure 迁移后，查 company 维度的数据
     * @param businessId
     * @param customerIds
     * @return
     */
    @PostMapping("/service/grooming/appointment/customer/all/last")
    Map<Integer, CustomerGroomingAppointmentDTO> getAllCustomerLastAppointment(
            @RequestParam("businessId") Integer businessId, @RequestBody CommonIdsParams customerIds);

    @PostMapping("/service/grooming/query/staff/range")
    Set<Integer> queryStaffIdListByDateRange(
            @RequestParam("businessId") Integer businessId, @RequestBody DateRangeParams dateRangeParams);

    /**
     * DONE(account structure): company 迁移后，查 company 维度的数据
     */
    @PostMapping("/service/grooming/customer/last/finished/appt")
    CustomerLastFinishedApptMapDto getCustomerLastFinishedAppointmentByIds(
            @RequestParam("businessId") Integer businessId, @RequestBody CommonIdsParams customerIds);

    /**
     * TODO(account structure): company 迁移后，查 company 维度的数据
     */
    @PostMapping("/service/grooming/customer/last/uncanceled/appt/interval")
    Integer getCustomerLastAppointmentInterval(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * TODO(account structure): company 迁移后，查 company 维度的数据
     *
     * Get appointment interval by customer ids.
     *
     * @param param {@link QueryCustomerApptNumPara}
     * @return key: customerId, value: count
     */
    @PostMapping("/service/grooming/customer/appointment-interval")
    Map<Integer, Integer> getApptIntervalByCustomerIds(@RequestBody ByCustomerIdsParam param);

    @PostMapping("/service/grooming/customer/address/convert")
    AddressLatLng convert(@RequestParam("fullAddress") String fullAddress);

    /**
     * 查询 reminder appointment
     */
    @PostMapping("/service/grooming/appointment/reminder")
    PageDTO<MoeGroomingAppointmentDTO> queryAppointmentReminder(
            @RequestBody AppointReminderParams appointReminderParams);

    @PostMapping("/service/grooming/appointment/reminder/will")
    List<AppointmentReminderSendDTO> queryAppointmentReminderWill(
            @RequestBody AppointmentReminderSendParams appointmentReminderSendParams);

    /**
     * 获取未来需要提醒的全部预约，并且返回预约的详细信息
     *
     * @param appointmentReminderSendParams
     * @return
     */
    @PostMapping("/service/grooming/appointment/reminder/will/withDetail")
    List<AppointmentReminderSendDTO> queryAppointmentReminderWithDetailWill(
            @RequestBody AppointmentReminderSendParams appointmentReminderSendParams);

    /**
     * 查询customer 上一个和下一个预约
     *
     * TODO(account structure): company 迁移后，查 company 维度的 last and next appt
     *
     * @param customerId
     * @return
     * @see #getCustomerLastAndNextAppointmentV2(Integer, Integer, Integer, boolean)
     * @deprecated by Freeman since 2025/4/11, use {@link #getCustomerLastAndNextAppointmentV2} instead.
     */
    @Deprecated
    @PostMapping("/service/grooming/appointment/customer/last")
    Map<String, CustomerGroomingAppointmentDTO> getCustomerLastAndNextAppointment(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam(value = "ignoreGroomingId", required = false) Integer ignoreGroomingId);

    /**
     * 查询customer 上一个和下一个预约。
     *
     * <p> 在不同场景可能需要过滤/包含 booking request。
     *
     * @param businessId businessId
     * @param customerId customerId
     * @param ignoreGroomingId ignoreGroomingId, nullable
     * @param excludeBookingRequest whether to exclude booking request, default false
     * @return <"last", lastAppt>, <"next", nextAppt>
     */
    @PostMapping("/service/grooming/appointment/customer/getCustomerLastAndNextAppointmentV2")
    Map<String, CustomerGroomingAppointmentDTO> getCustomerLastAndNextAppointmentV2(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam(value = "ignoreGroomingId", required = false) @Nullable Integer ignoreGroomingId,
            @RequestParam("excludeBookingRequest") boolean excludeBookingRequest);

    /**
     * 查询customerList下一个预约
     *
     * @param customerIds
     * @return
     */
    @PostMapping("/service/grooming/appointment/customer/next")
    Map<Integer, CustomerGroomingAppointmentDTO> getCustomerNextAppointment(
            @RequestParam("businessId") Integer businessId, @RequestBody CommonIdsParams customerIds);

    /**
     * DONE(account structure): company 迁移后，查 company 维度的数据
     *
     * 查询customerList下一个预约
     *
     * @param businessId
     * @param customerIds
     * @return
     */
    @PostMapping("/service/grooming/appointment/customer/list/next/last")
    CustomerNextLastApptMapDto getCustomerNextLastAppointmentByIds(
            @RequestParam("businessId") Integer businessId, @RequestBody CommonIdsParams customerIds);

    /**
     * 查询customer 预约统计
     *
     * DONE(account structure): company 迁移后，查询 company 维度的数据
     *
     * @param customerIds
     * @return
     */
    @PostMapping("/service/grooming/appointment/customer/num")
    List<CustomerAppointmentNumInfoDTO> getCustomerAppointmentNum(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> customerIds);

    /**
     * 仅 AS 迁移后可调用。以 customer 为纬度统计各类 appointment 数量
     */
    @PostMapping("/service/grooming/appointment/listCustomerAppointmentNum")
    List<CustomerAppointmentNumInfoDTO> listCustomerAppointmentNum(
            @RequestParam("companyId") Long companyId, @RequestBody List<Integer> customerIds);

    /**
     * 查询customer 预约统计
     *
     * DONE(account structure): company 迁移后，查询 company 维度的数据
     *
     * @param apptNumPara
     * @return
     */
    @PostMapping("/service/grooming/appointment/customer/group")
    Map<Integer, List<CustomerAppointmentNumInfoDTO>> getCustomerAppointmentNumGroupByCustomerId(
            @RequestBody QueryCustomerApptNumPara apptNumPara);

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     *
     * Filter client by appt count condition
     * If there has customer id set, filter in customer id set, otherwise filter in business
     *
     * @param clientsFilterDTO filter params
     * @return filtered customer id set
     */
    @PostMapping("/service/grooming/count/filter")
    Set<Integer> listCustomerIdByCountFilter(@RequestBody ClientsFilterDTO clientsFilterDTO);

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     *
     * Filter client by appt date condition
     * If there has customer id set, filter in customer id set, otherwise filter in business
     *
     * @param clientsFilterDTO filter params
     * @return filtered customer id set
     */
    @PostMapping("/service/grooming/date/filter")
    Set<Integer> listCustomerIdByDateFilter(@RequestBody ClientsFilterDTO clientsFilterDTO);

    /**
     *
     * Filter client by appt groomer condition
     * If there has customer id set, filter in customer id set, otherwise filter in business
     *
     * @param clientsFilterDTO filter params
     * @return filtered customer id set
     */
    @PostMapping("/service/grooming/groomer/filter")
    Set<Integer> listCustomerIdByGroomerFilter(@RequestBody ClientsFilterDTO clientsFilterDTO);

    /**
     * TODO(account structure): 迁移后，统计 customer 在 company 维度的 appt count，不需要根据 preferredBusinessIds 进行过滤
     *
     * List customer appt count, contains total appt count, finished appt count
     *
     * @param businessDateClientsDTO business id and customer id set and date info
     * @return customer appt count info
     */
    @PostMapping("/service/grooming/count")
    Map<Integer, CustomerApptCountDTO> listCustomerApptCount(
            @RequestBody BusinessDateClientsDTO businessDateClientsDTO);

    /**
     * TODO(account structure): 迁移后，统计 customer 在 company 维度的 appt date，不需要根据 preferredBusinessIds 进行过滤
     *
     * List customer appt date, contains last appt date and next appt date
     *
     * @param businessDateClientsDTO business id and customer id set and date info
     * @return customer appt date info
     */
    @PostMapping("/service/grooming/date")
    Map<Integer, CustomerApptDateDTO> listCustomerApptDate(@RequestBody BusinessDateClientsDTO businessDateClientsDTO);

    /**
     * DONE(account structure): 迁移后删除 company 维度的数据
     *
     * 提供给customer模块，当customer删除的时候，操作对应的grooming appointment
     *
     * @param deleteParams
     * @return
     */
    @PutMapping("/service/grooming/customer/remove")
    Boolean customerDelete(@RequestBody CustomerDeleteParams deleteParams);

    /**
     * TODO(account structure): 兼容 company 维度删除
     *
     * Batch delete grooming appointment by customer id
     *
     * @param staffId            staff id
     * @param businessClientsDTO business id and customer id set
     * @return result
     */
    @PutMapping("/service/grooming/customer/batchRemove")
    Boolean batchDeleteByCustomerId(
            @RequestParam("staffId") Integer staffId, @RequestBody BusinessClientsDTO businessClientsDTO);

    /**
     * 查询在waiting列表的customer
     *
     * @param customerIds
     * @return
     */
    @PostMapping("/service/grooming/waiting/customer/num")
    List<Integer> queryCustomerIdsInWaiting(@RequestBody List<Integer> customerIds);

    /**
     * 查询商户大于当前日期的预约的客户ID【去重】
     *
     * @param businessId 商户ID
     * @return 商户大于当前日期的预约的客户ID【去重】
     */
    @GetMapping("/service/grooming/appointment/customer/future")
    List<Integer> queryFutureAppointmentCustomerIdList(@RequestParam("tokenBusinessId") Integer businessId);

    /**
     * 查询 repeat appointment
     */
    @PostMapping("/service/grooming/repeat/reminder")
    PageDTO<MoeGroomingAppointmentDTO> queryRepeatAppointmentReminder(
            @RequestBody RepeatReminderParams repeatReminderParams);

    @PostMapping("/service/grooming/appointment/business/upcoming")
    ResponseResult<BusinessUpcomingDTO> queryBusinessUpComingAppoint(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam(value = "date", required = false) String date);

    @PostMapping("/service/grooming/appointment/customer/upcoming/UnEncode")
    ResponseResult<List<CustomerUpComingAppointDTO>> queryCustomerUpComingAppointUnEncode(
            @RequestParam("customerId") Integer customerId,
            @RequestParam("businessId") Integer businessId,
            @RequestParam("appointmentDate") String appointmentDate,
            @RequestParam("minutes") Integer minutes);

    @GetMapping("/service/grooming/appointment/customer/upcoming/share")
    List<CustomerUpComingAppointDTO> queryCustomerUpComingAppointForClientShare(
            @RequestParam("customerId") Integer customerId);

    @PostMapping("/service/grooming/customer/within7days")
    List<Integer> selectCustomerIdWithIn7DaysAppointment(
            @RequestParam("tokenBusinessId") Integer businessId, @RequestParam("tokenStaffId") Integer tokenStaffId);

    /**
     * 获取 staff 在从 beforeDay 天之前，到 afterDay 天之后的范围内有 appointment 的 customerID 列表.
     *
     * @param businessId business ID.
     * @param tokenStaffId staff ID.
     * @param beforeDay: 可选参数，非负整数，表示今天之前的天数，不包括今天.
     * @param afterDay: 可选参数，非负整数，表示今天之后的天数，不包括今天.
     * @return customerId list.
     */
    @PostMapping("/service/grooming/customer/withinCustomDays")
    List<Integer> selectCustomerIdWithInCustomDaysAppointment(
            @RequestParam("tokenBusinessId") Integer businessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestParam(name = "beforeDay", required = false) Integer beforeDay,
            @RequestParam(name = "afterDay", required = false) Integer afterDay);

    /**
     * 获取 staff 在从 beforeDay 天之前，到 afterDay 天之后的范围内与 customer 有关的 appointment 的数量.
     *
     * @param businessId business ID.
     * @param tokenStaffId staff ID.
     * @param customerId customer ID.
     * @param beforeDay: 可选参数，非负整数，表示今天之前的天数，不包括今天.
     * @param afterDay: 可选参数，非负整数，表示今天之后的天数，不包括今天.
     * @return customerId.
     */
    @PostMapping("/service/grooming/customer/withinCustomDays/count")
    Integer selectCustomerIdIsWithInCustomDaysAppointment(
            @RequestParam("tokenBusinessId") Integer businessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam(name = "beforeDay", required = false) Integer beforeDay,
            @RequestParam(name = "afterDay", required = false) Integer afterDay);

    @PostMapping("/service/grooming/customer/within7days/count")
    Integer selectCustomerIdIsWithIn7AppointmentDate(
            @RequestParam("tokenBusinessId") Integer businessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestParam("customerId") Integer customerId);

    @PostMapping("/service/grooming/customer/withinAllDays")
    List<Integer> selectCustomerIdWithInAllDaysAppointment(
            @RequestParam("tokenBusinessId") Integer businessId, @RequestParam("tokenStaffId") Integer tokenStaffId);

    @PostMapping("/service/grooming/customer/withinAllDays/count")
    Integer selectCustomerIdIsWithInAllAppointmentDate(
            @RequestParam("tokenBusinessId") Integer businessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestParam("customerId") Integer customerId);

    @PostMapping("/service/grooming/business/daily")
    List<Integer> getBusinessIdsOnSendDaily();

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     */
    @PostMapping("/service/grooming/appointment/customer/last/batch")
    List<CustomerGroomingAppointmentDTO> getCustomerLastAppointments(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> customerIds);

    @GetMapping("/service/grooming/customer/last/service")
    ResponseResult<CustomerLastServiceDTO> getCustomerLastService(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestParam("customerId") Integer customerId);

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     */
    @GetMapping("/service/grooming/customer/last/alertNote")
    String getCustomerLastAlertNote(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    @PostMapping("/service/grooming/customer/rebook/reminder")
    PageDTO<CustomerRebookReminderDTO> getCustomerRebookReminderList(
            @RequestBody QueryReminderRebookParams queryReminderRebookParams);

    @PostMapping("/service/grooming/specificSupport/customer/rebook/reminder")
    PageDTO<CustomerRebookReminderDTO> getSpecificSupportCustomerRebookReminderList(
            @RequestBody QuerySpecificSupportReminderRebookParams queryReminderRebookParams);

    @GetMapping("/service/grooming/customer/rebook/reminder")
    CustomerRebookReminderDTO getCustomerRebookReminder(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("timezoneName") String timezoneName);

    /**
     * 获取预约和预约中每个pet的详情，不包含已删除的预约
     *
     * @param groomingIds
     * @return
     */
    @PostMapping("/service/grooming/appt/withPetDetail")
    List<GroomingBookingDTO> queryApptWithPetDetailByIds(@RequestBody List<Integer> groomingIds);

    /**
     * 窗口预约详情
     *
     * @param tokenBusinessId
     * @param id
     * @return
     */
    @GetMapping("/service/grooming/appointment/detail")
    ResponseResult<GroomingTicketWindowDetailDTO> queryTicketDetailWithWindow(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestParam("id") Integer id);

    /**
     * DONE(account structure): company 迁移后，查询 company 维度的数据
     */
    @GetMapping("/service/grooming/pet/last/service")
    ResponseResult<PetLastServiceDTO> getPetLastService(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestParam("petId") Integer petId);

    @GetMapping("/service/grooming/appointment")
    AppointmentWithPetDetailsDto getAppointmentWithPetDetails(@RequestParam("appointmentId") Integer appointmentId);

    @PostMapping("/service/grooming/appointment/list")
    List<AppointmentWithPetDetailsDto> getAppointmentListWithPetDetails(
            @RequestParam(value = "includeCancelled", required = false, defaultValue = "false")
                    Boolean includeCancelled,
            @RequestBody List<Integer> appointmentIds);

    @PutMapping("/service/grooming/appointment/confirm")
    ResponseResult<Integer> confirm(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestBody ConfirmParams editIdParams);

    @PutMapping("/service/grooming/appointment/cancel")
    ResponseResult<Integer> cancel(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestBody CancelParams editIdParams);

    @GetMapping("/service/grooming/appointment/customer/upcoming/url")
    ResponseResult<String> getCustomerUpcomingUrl(@RequestParam("customerId") Integer customerId);

    // TODO: pet parent 使用的内部接口，pet parent 已下线，不需要这个接口了
    @Deprecated
    @PostMapping("/service/grooming/appointment/customer/upcoming/latest")
    CustomerUpComingAppointDTO getLatestClientCustomerUpcomingAppoint(
            @RequestBody ClientCustomerUpcomingParams upcomingParams);

    // TODO: pet parent 使用的内部接口，pet parent 已下线，不需要这个接口了
    @Deprecated
    @PostMapping("/service/grooming/appointment/customer/upcoming/list")
    List<CustomerUpComingAppointDTO> getClientCustomerUpcomingAppoint(
            @RequestBody ClientCustomerUpcomingParams upcomingParams);

    // TODO: pet parent 使用的内部接口，pet parent 已下线，不需要这个接口了
    @Deprecated
    @GetMapping("/service/grooming/appointment/customer/upcoming")
    CustomerUpComingAppointDTO getClientUpcomingAppointById(@RequestParam("appointmentId") Integer appointmentId);

    /**
     * 给 mis 使用，简单查询商家预约
     */
    @PostMapping("/service/grooming/adminQueryAppointment")
    AppointmentListDto adminQueryAppointment(@RequestBody AdminQueryAppointmentParams param);

    /**
     * 给 mis 使用，删除已经 cancel 的 appointment
     */
    @PostMapping("/service/grooming/adminDeleteAppointment")
    Boolean adminDeleteCanceledAppointment(@RequestParam("appointmentId") Long appointmentId);

    /**
     * 获取最新创建且没有取消的 appointment
     *
     * @param businessId businessId
     * @param customerId customerId
     */
    @GetMapping("/service/grooming/getLastedNotCanceledAppointment")
    MoeGroomingAppointmentDTO getLastedNotCanceledAppointment(
            @RequestParam Integer businessId, @RequestParam Integer customerId);

    @PostMapping("service/grooming/appointment/created/between")
    CustomerAppointmentWithAmountList getAppointmentIdsCreatedBetween(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startTime") Long startTime,
            @RequestParam("endTime") Long endTime,
            @RequestBody CommonIdsParams customerIds,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize);

    @GetMapping("/service/grooming/appointment/getAppointmentById")
    AppointmentDTO getAppointmentById(@RequestParam("appointmentId") int appointmentId);

    /**
     * TODO(account structure): company 迁移后，查询 company 维度的数据
     */
    @GetMapping("/service/grooming/getNewCreateAppointment")
    MoeGroomingAppointmentDTO getNewCreateAppointment(
            @RequestParam Integer businessId, @RequestParam Integer customerId);

    /**
     * 获取 appointment date 在指定时间范围内有附带 sale product 的 invoice ids
     *
     * @param param {@link GetInvoiceIdsAppointmentDateBetweenParam}
     * @return invoice ids
     */
    @PostMapping("/service/grooming/getInvoiceIdsAppointmentHasProductAndDateBetween")
    Set<Integer> getInvoiceIdsAppointmentHasProductAndDateBetween(
            @RequestBody @Valid GetInvoiceIdsAppointmentDateBetweenParam param);

    @PutMapping("/service/grooming/appointment/ready/notification/result/update")
    void updateReadyNotificationResult(
            @RequestParam(value = "appointmentId", required = false) Integer appointmentId,
            @RequestParam(value = "failedReason", required = false) String failedReason);

    @GetMapping("/service/grooming/getStaffIdListAssignedAppointment")
    Set<Integer> getStaffIdListAssignedAppointment(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam(value = "endDate") String endDate);

    @GetMapping("/service/grooming/getStaffAssignedAppointmentByDate")
    Map<String, Set<Integer>> getStaffAssignedAppointmentByDate(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam(value = "endDate") String endDate);

    /**
     * 获取所有 source platform 为 Google 的 appointment 数量
     *
     * @param businessIds businessIds, null or empty list empty map
     * @return key: businessId, value: count
     */
    @PostMapping("/service/grooming/countAppointmentFromGoogle")
    Map<Integer, Long> countAppointmentFromGoogle(@RequestBody List<Integer> businessIds);

    @PutMapping("/service/grooming/appointment/status")
    int updateStatus(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam(value = "tokenStaffId", required = false) Integer tokenStaffId,
            @RequestBody StatusUpdateParams editIdParams);

    @GetMapping("/service/grooming/appointment/getAppointmentRelatedStaffIds")
    List<Integer> getAppointmentRelatedStaffIds(@RequestParam("appointmentId") Integer appointmentId);

    /**
     * 判断 staff 是否有指定 customer 的 appointment（不包括 cancel 的 appointment），包括 multi-staff appointment
     *
     * @param param {@link HasAppointmentParam}
     * @return true if it has appointment, otherwise false
     */
    @PostMapping("/service/grooming/appointment/hasAppointment")
    boolean hasAppointment(@RequestBody @Valid HasAppointmentParam param);

    /**
     * 根据日期范围查询 appointments，返回按照 service item type 分组结果。
     *
     * <p> appointment 如果有多个 service，只会被分到一个 service item type 中。
     *
     * <p> NOTE：不要尝试复用这个接口 :)
     *
     * @param param {@link ListGroupedAppointmentByDateRangeParam}
     * @return key: service item type, value: appointments
     */
    @PostMapping("/service/grooming/appointment/listGroupedAppointmentByDateRange")
    Map<Integer, List<AppointmentDTO>> listGroupedAppointmentByDateRange(
            @RequestBody ListGroupedAppointmentByDateRangeParam param);

    @Builder(toBuilder = true)
    record HasAppointmentParam(@NotNull Integer businessId, @NotNull Integer customerId, @NotNull Integer staffId) {}

    @Builder
    record ListGroupedAppointmentByDateRangeParam(@NotNull Integer businessId, @NotNull Range<String> dateRange) {}

    @GetMapping("/service/grooming/appointment/queryGroomingWaitlistAvailable")
    WaitlistAvailableDTO queryGroomingWaitlistAvailable(
            @RequestParam("companyId") Integer companyId,
            @RequestParam("businessId") Integer businessId,
            @RequestParam("pageNum") Integer pageNum,
            @RequestParam("pageSize") Integer pageSize);
}
