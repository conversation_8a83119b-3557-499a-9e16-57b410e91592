package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.BookOnlineLandingPageDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBookOnlineBusinessService {
    @GetMapping("/service/grooming/ob/business/config")
    BookOnlineLandingPageDTO getLandingPageConfig(@RequestParam Long businessId);
}
