package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RepeatPreviewInfoDTO extends ConflictInfoDTO {

    @Schema(description = "schedule 类型: 1-非 ss 预约, 2-ss 找到时间的预约, 3-ss 找不到时间的预约")
    private Integer scheduleType;

    @Schema(description = "是否是 history appointment")
    private Boolean isHistory;
}
