package com.moego.server.payment.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@Getter
@Setter
public class PlatformCareRecordView {
    // record id
    private Long id;
    // record code
    private String code;
    // email
    private String email;
    // agreement record uuid
    private String agreementRecordUuid;
    // account id
    private Long accountId;
    // record status
    private Integer status;
    // agreement signed time
    private Date signedTime;
    // record create time
    private Date createTime;
    private Date updateTime;
    // agreement id
    private Long agreementId;
    // company id
    private Long companyId;
    // order shipping status
    private String orderShippingStatus;
    // link
    private String link;
    // discount code
    private String discountCode;
    // show accounting
    private Byte showAccounting;
}
