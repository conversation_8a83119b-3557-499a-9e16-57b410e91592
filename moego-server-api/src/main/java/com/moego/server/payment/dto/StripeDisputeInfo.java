package com.moego.server.payment.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/12/15
 */
@Data
@Builder
public class StripeDisputeInfo {
    private String id;
    private Long amount;
    private String chargeId;
    private String currency;
    private String metadata;
    private String reason;
    private String status;
    private Long created;
    private Evidence evidence;
    private EvidenceDetails evidenceDetails;

    @Data
    @Builder
    public static class EvidenceDetails {
        Long dueBy;
        /** Whether evidence has been staged for this dispute. */
        Boolean hasEvidence;
        /**
         * Whether the last evidence submission was submitted past the due date. Defaults to {@code
         * false} if no evidence submissions have occurred. If {@code true}, then delivery of the latest
         * evidence is <em>not</em> guaranteed.
         */
        Boolean pastDue;
        /**
         * The number of times evidence has been submitted. Typically, you may only submit evidence
         * once.
         */
        Long submissionCount;
    }

    @Data
    @Builder
    public static class Evidence {
        /**
         * Any server or activity logs showing proof that the customer accessed or downloaded the
         * purchased digital product. This information should include IP addresses, corresponding
         * timestamps, and any detailed recorded activity.
         */
        String accessActivityLog;
        /**
         * The billing address provided by the customer.
         */
        String billingAddress;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) Your
         * subscription cancellation policy, as shown to the customer.
         */
        String cancellationPolicy;
        /**
         * An explanation of how and when the customer was shown your refund policy prior to purchase.
         */
        String cancellationPolicyDisclosure;
        /**
         * A justification for why the customer's subscription was not canceled.
         */
        String cancellationRebuttal;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) Any
         * communication with the customer that you feel is relevant to your case. Examples include
         * emails proving that the customer received the product or service, or demonstrating their use
         * of or satisfaction with the product or service.
         */
        String customerCommunication;
        /**
         * The email address of the customer.
         */
        String customerEmailAddress;
        /**
         * The name of the customer.
         */
        String customerName;
        /**
         * The IP address that the customer used when making the purchase.
         */
        String customerPurchaseIp;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) A relevant
         * document or contract showing the customer's signature.
         */
        String customerSignature;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) Documentation
         * for the prior charge that can uniquely identify the charge, such as a receipt, shipping
         * label, work order, etc. This document should be paired with a similar document from the
         * disputed payment that proves the two payments are separate.
         */
        String duplicateChargeDocumentation;
        /**
         * An explanation of the difference between the disputed charge versus the prior charge that
         * appears to be a duplicate.
         */
        String duplicateChargeExplanation;
        /**
         * The Stripe ID for the prior charge which appears to be a duplicate of the disputed charge.
         */
        String duplicateChargeId;
        /**
         * A description of the product or service that was sold.
         */
        String productDescription;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) Any receipt or
         * message sent to the customer notifying them of the charge.
         */
        String receipt;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) Your refund
         * policy, as shown to the customer.
         */
        String refundPolicy;
        /**
         * Documentation demonstrating that the customer was shown your refund policy prior to purchase.
         */
        String refundPolicyDisclosure;
        /**
         * A justification for why the customer is not entitled to a refund.
         */
        String refundRefusalExplanation;
        /**
         * The date on which the customer received or began receiving the purchased service, in a clear
         * human-readable format.
         */
        String serviceDate;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) Documentation
         * showing proof that a service was provided to the customer. This could include a copy of a
         * signed contract, work order, or other form of written agreement.
         */
        String serviceDocumentation;
        /**
         * The address to which a physical product was shipped. You should try to include as complete
         * address information as possible.
         */
        String shippingAddress;
        /**
         * The delivery service that shipped a physical product, such as Fedex, UPS, USPS, etc. If
         * multiple carriers were used for this purchase, please separate them with commas.
         */
        String shippingCarrier;
        /**
         * The date on which a physical product began its route to the shipping address, in a clear
         * human-readable format.
         */
        String shippingDate;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) Documentation
         * showing proof that a product was shipped to the customer at the same address the customer
         * provided to you. This could include a copy of the shipment receipt, shipping label, etc. It
         * should show the customer's full shipping address, if possible.
         */
        String shippingDocumentation;
        /**
         * The tracking number for a physical product, obtained from the delivery service. If multiple
         * tracking numbers were generated for this purchase, please separate them with commas.
         */
        String shippingTrackingNumber;
        /**
         * (ID of a <a href="https://stripe.com/docs/guides/file-upload">file upload</a>) Any additional
         * evidence or statements.
         */
        String uncategorizedFile;
        /**
         * Any additional evidence or statements.
         */
        String uncategorizedText;
    }
}
