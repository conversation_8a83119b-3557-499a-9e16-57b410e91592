// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/fulfillment/v1/instance_service.proto

package fulfillmentpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetServiceInstanceByIDsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceInstanceByIDsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceInstanceByIDsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceInstanceByIDsRequestMultiError, or nil if none found.
func (m *GetServiceInstanceByIDsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceInstanceByIDsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetServiceInstanceByIDsRequestMultiError(errors)
	}

	return nil
}

// GetServiceInstanceByIDsRequestMultiError is an error wrapping multiple
// validation errors returned by GetServiceInstanceByIDsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetServiceInstanceByIDsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceInstanceByIDsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceInstanceByIDsRequestMultiError) AllErrors() []error { return m }

// GetServiceInstanceByIDsRequestValidationError is the validation error
// returned by GetServiceInstanceByIDsRequest.Validate if the designated
// constraints aren't met.
type GetServiceInstanceByIDsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceInstanceByIDsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceInstanceByIDsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceInstanceByIDsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceInstanceByIDsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceInstanceByIDsRequestValidationError) ErrorName() string {
	return "GetServiceInstanceByIDsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceInstanceByIDsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceInstanceByIDsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceInstanceByIDsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceInstanceByIDsRequestValidationError{}

// Validate checks the field values on GetServiceInstanceByIDsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetServiceInstanceByIDsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceInstanceByIDsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetServiceInstanceByIDsResponseMultiError, or nil if none found.
func (m *GetServiceInstanceByIDsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceInstanceByIDsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetServiceInstances() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetServiceInstanceByIDsResponseValidationError{
						field:  fmt.Sprintf("ServiceInstances[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetServiceInstanceByIDsResponseValidationError{
						field:  fmt.Sprintf("ServiceInstances[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetServiceInstanceByIDsResponseValidationError{
					field:  fmt.Sprintf("ServiceInstances[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetServiceInstanceByIDsResponseMultiError(errors)
	}

	return nil
}

// GetServiceInstanceByIDsResponseMultiError is an error wrapping multiple
// validation errors returned by GetServiceInstanceByIDsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetServiceInstanceByIDsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceInstanceByIDsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceInstanceByIDsResponseMultiError) AllErrors() []error { return m }

// GetServiceInstanceByIDsResponseValidationError is the validation error
// returned by GetServiceInstanceByIDsResponse.Validate if the designated
// constraints aren't met.
type GetServiceInstanceByIDsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceInstanceByIDsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceInstanceByIDsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceInstanceByIDsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceInstanceByIDsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceInstanceByIDsResponseValidationError) ErrorName() string {
	return "GetServiceInstanceByIDsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetServiceInstanceByIDsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceInstanceByIDsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceInstanceByIDsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceInstanceByIDsResponseValidationError{}

// Validate checks the field values on ListServiceInstanceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListServiceInstanceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListServiceInstanceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListServiceInstanceRequestMultiError, or nil if none found.
func (m *ListServiceInstanceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListServiceInstanceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCompanyId() <= 0 {
		err := ListServiceInstanceRequestValidationError{
			field:  "CompanyId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBusinessId() <= 0 {
		err := ListServiceInstanceRequestValidationError{
			field:  "BusinessId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListServiceInstanceRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListServiceInstanceRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListServiceInstanceRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListServiceInstanceRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListServiceInstanceRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListServiceInstanceRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListServiceInstanceRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListServiceInstanceRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListServiceInstanceRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortType

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListServiceInstanceRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListServiceInstanceRequestValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListServiceInstanceRequestValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListServiceInstanceRequestMultiError(errors)
	}

	return nil
}

// ListServiceInstanceRequestMultiError is an error wrapping multiple
// validation errors returned by ListServiceInstanceRequest.ValidateAll() if
// the designated constraints aren't met.
type ListServiceInstanceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListServiceInstanceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListServiceInstanceRequestMultiError) AllErrors() []error { return m }

// ListServiceInstanceRequestValidationError is the validation error returned
// by ListServiceInstanceRequest.Validate if the designated constraints aren't met.
type ListServiceInstanceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListServiceInstanceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListServiceInstanceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListServiceInstanceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListServiceInstanceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListServiceInstanceRequestValidationError) ErrorName() string {
	return "ListServiceInstanceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListServiceInstanceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListServiceInstanceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListServiceInstanceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListServiceInstanceRequestValidationError{}

// Validate checks the field values on ListServiceInstanceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListServiceInstanceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListServiceInstanceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListServiceInstanceResponseMultiError, or nil if none found.
func (m *ListServiceInstanceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListServiceInstanceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetServiceInstances() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListServiceInstanceResponseValidationError{
						field:  fmt.Sprintf("ServiceInstances[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListServiceInstanceResponseValidationError{
						field:  fmt.Sprintf("ServiceInstances[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListServiceInstanceResponseValidationError{
					field:  fmt.Sprintf("ServiceInstances[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListServiceInstanceResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListServiceInstanceResponseValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListServiceInstanceResponseValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEnd

	// no validation rules for Total

	if len(errors) > 0 {
		return ListServiceInstanceResponseMultiError(errors)
	}

	return nil
}

// ListServiceInstanceResponseMultiError is an error wrapping multiple
// validation errors returned by ListServiceInstanceResponse.ValidateAll() if
// the designated constraints aren't met.
type ListServiceInstanceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListServiceInstanceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListServiceInstanceResponseMultiError) AllErrors() []error { return m }

// ListServiceInstanceResponseValidationError is the validation error returned
// by ListServiceInstanceResponse.Validate if the designated constraints
// aren't met.
type ListServiceInstanceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListServiceInstanceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListServiceInstanceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListServiceInstanceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListServiceInstanceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListServiceInstanceResponseValidationError) ErrorName() string {
	return "ListServiceInstanceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListServiceInstanceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListServiceInstanceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListServiceInstanceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListServiceInstanceResponseValidationError{}
