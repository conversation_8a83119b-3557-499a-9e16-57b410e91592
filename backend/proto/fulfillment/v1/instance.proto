syntax = "proto3";

package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "backend/proto/fulfillment/v1/common.proto"; // 这里需要引入common.proto

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// 服务实例
message ServiceInstance {
  // 服务实例ID
  int64 id = 1;
  // 商家ID
  int64 business_id = 2;
  // 客户ID
  int64 customer_id = 3;
  // 公司ID
  int64 company_id = 4;
  // 预约ID
  int64 appointment_id = 5;
  // 宠物ID
  int64 pet_id = 6;
  // 护理类型
  CareType care_type = 7;
  // 日期类型
  DateType date_type = 8;
  // 服务工厂ID
  int64 service_factory_id = 9;
  // 父服务实例ID
  int64 parent_id = 10;
  // 根服务实例ID
  int64 root_id = 11;
  // 开始时间
  // (-- api-linter: core::0142::time-field-names=disabled
  //     aip.dev/not-precedent: start_date is clear and appropriate --)
  google.protobuf.Timestamp start_date = 12;
  // 结束时间
  // (-- api-linter: core::0142::time-field-names=disabled
  //     aip.dev/not-precedent: end_date is clear and appropriate --)
  google.protobuf.Timestamp end_date = 13;
  // 创建时间
  // (-- api-linter: core::0142::time-field-names=disabled
  //     aip.dev/not-precedent: created_at is clear and appropriate --)
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: created_at is clear and appropriate --)
  google.protobuf.Timestamp created_at = 14;
  // 更新时间
  // (-- api-linter: core::0142::time-field-names=disabled
  //     aip.dev/not-precedent: updated_at is clear and appropriate --)
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: updated_at is clear and appropriate --)
  google.protobuf.Timestamp updated_at = 15;
}

// 服务实例过滤条件
message ServiceInstanceFilter {
  // 宠物ID列表
  repeated int64 pet_ids = 1;
  // 客户ID列表
  repeated int64 customer_ids = 2;
  // 护理类型列表
  repeated CareType care_types = 3;
  // 日期类型列表
  repeated DateType date_types = 4;
  // 根服务实例ID列表
  repeated int64 root_ids = 5;
}
