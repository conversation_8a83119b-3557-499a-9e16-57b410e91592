// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/fulfillment/v1/fulfillment_report_service.proto

package fulfillmentpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FulfillmentReportService_GetFulfillmentReportTemplate_FullMethodName        = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetFulfillmentReportTemplate"
	FulfillmentReportService_UpdateFulfillmentReportTemplate_FullMethodName     = "/backend.proto.fulfillment.v1.FulfillmentReportService/UpdateFulfillmentReportTemplate"
	FulfillmentReportService_GetFulfillmentReport_FullMethodName                = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetFulfillmentReport"
	FulfillmentReportService_UpdateFulfillmentReport_FullMethodName             = "/backend.proto.fulfillment.v1.FulfillmentReportService/UpdateFulfillmentReport"
	FulfillmentReportService_GetFulfillmentReportSummaryInfo_FullMethodName     = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetFulfillmentReportSummaryInfo"
	FulfillmentReportService_GetFulfillmentReportRecords_FullMethodName         = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetFulfillmentReportRecords"
	FulfillmentReportService_GetFulfillmentReportPreview_FullMethodName         = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetFulfillmentReportPreview"
	FulfillmentReportService_ListFulfillmentThemeConfig_FullMethodName          = "/backend.proto.fulfillment.v1.FulfillmentReportService/ListFulfillmentThemeConfig"
	FulfillmentReportService_GenerateMessageContent_FullMethodName              = "/backend.proto.fulfillment.v1.FulfillmentReportService/GenerateMessageContent"
	FulfillmentReportService_SendFulfillmentReport_FullMethodName               = "/backend.proto.fulfillment.v1.FulfillmentReportService/SendFulfillmentReport"
	FulfillmentReportService_GetFulfillmentReportSendHistory_FullMethodName     = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetFulfillmentReportSendHistory"
	FulfillmentReportService_GetFulfillmentReportSendResult_FullMethodName      = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetFulfillmentReportSendResult"
	FulfillmentReportService_ListFulfillmentReport_FullMethodName               = "/backend.proto.fulfillment.v1.FulfillmentReportService/ListFulfillmentReport"
	FulfillmentReportService_BatchDeleteFulfillmentReport_FullMethodName        = "/backend.proto.fulfillment.v1.FulfillmentReportService/BatchDeleteFulfillmentReport"
	FulfillmentReportService_BatchSendFulfillmentReport_FullMethodName          = "/backend.proto.fulfillment.v1.FulfillmentReportService/BatchSendFulfillmentReport"
	FulfillmentReportService_IncreaseFulfillmentOpenedCount_FullMethodName      = "/backend.proto.fulfillment.v1.FulfillmentReportService/IncreaseFulfillmentOpenedCount"
	FulfillmentReportService_SyncFulfillmentReportTemplate_FullMethodName       = "/backend.proto.fulfillment.v1.FulfillmentReportService/SyncFulfillmentReportTemplate"
	FulfillmentReportService_BatchSyncFulfillmentReportQuestions_FullMethodName = "/backend.proto.fulfillment.v1.FulfillmentReportService/BatchSyncFulfillmentReportQuestions"
	FulfillmentReportService_SyncFulfillmentReport_FullMethodName               = "/backend.proto.fulfillment.v1.FulfillmentReportService/SyncFulfillmentReport"
	FulfillmentReportService_SyncFulfillmentReportSendRecord_FullMethodName     = "/backend.proto.fulfillment.v1.FulfillmentReportService/SyncFulfillmentReportSendRecord"
	FulfillmentReportService_BatchMigrateTemplates_FullMethodName               = "/backend.proto.fulfillment.v1.FulfillmentReportService/BatchMigrateTemplates"
	FulfillmentReportService_BatchMigrateQuestions_FullMethodName               = "/backend.proto.fulfillment.v1.FulfillmentReportService/BatchMigrateQuestions"
	FulfillmentReportService_BatchMigrateReports_FullMethodName                 = "/backend.proto.fulfillment.v1.FulfillmentReportService/BatchMigrateReports"
	FulfillmentReportService_BatchMigrateRecords_FullMethodName                 = "/backend.proto.fulfillment.v1.FulfillmentReportService/BatchMigrateRecords"
	FulfillmentReportService_GetTemplatesByUniqueKeys_FullMethodName            = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetTemplatesByUniqueKeys"
	FulfillmentReportService_GetQuestionsByTemplateKeys_FullMethodName          = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetQuestionsByTemplateKeys"
	FulfillmentReportService_GetGroomingQuestionsByQuestionKeys_FullMethodName  = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetGroomingQuestionsByQuestionKeys"
	FulfillmentReportService_GetReportsByUniqueKeys_FullMethodName              = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetReportsByUniqueKeys"
	FulfillmentReportService_GetRecordsByReportKeys_FullMethodName              = "/backend.proto.fulfillment.v1.FulfillmentReportService/GetRecordsByReportKeys"
)

// FulfillmentReportServiceClient is the client API for FulfillmentReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// FulfillmentReportService
type FulfillmentReportServiceClient interface {
	// GetFulfillmentReportTemplate 获取履约报告模板
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportTemplate(ctx context.Context, in *GetFulfillmentReportTemplateRequest, opts ...grpc.CallOption) (*GetFulfillmentReportTemplateResponse, error)
	// UpdateFulfillmentReportTemplate 更新履约报告模板
	// (-- api-linter: core::0134::response-message-name=disabled --)
	UpdateFulfillmentReportTemplate(ctx context.Context, in *UpdateFulfillmentReportTemplateRequest, opts ...grpc.CallOption) (*UpdateFulfillmentReportTemplateResponse, error)
	// GetFulfillmentReport 获取履约报告
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReport(ctx context.Context, in *GetFulfillmentReportRequest, opts ...grpc.CallOption) (*GetFulfillmentReportResponse, error)
	// UpdateFulfillmentReport 更新履约报告
	// (-- api-linter: core::0134::response-message-name=disabled --)
	UpdateFulfillmentReport(ctx context.Context, in *UpdateFulfillmentReportRequest, opts ...grpc.CallOption) (*UpdateFulfillmentReportResponse, error)
	// GetFulfillmentReportSummaryInfo 获取履约报告详细信息
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportSummaryInfo(ctx context.Context, in *GetFulfillmentReportSummaryInfoRequest, opts ...grpc.CallOption) (*GetFulfillmentReportSummaryInfoResponse, error)
	// GetFulfillmentReportRecords 根据预约中的所有报告
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportRecords(ctx context.Context, in *GetFulfillmentReportRecordsRequest, opts ...grpc.CallOption) (*GetFulfillmentReportRecordsResponse, error)
	// GetFulfillmentReportPreview 获取 setting 中展示的 preview report 相关内容
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportPreview(ctx context.Context, in *GetFulfillmentReportPreviewRequest, opts ...grpc.CallOption) (*GetFulfillmentReportPreviewResponse, error)
	// listFulfillmentThemeConfig 获取所有主题配置
	// (-- api-linter: core::0131::response-message-name=disabled --)
	ListFulfillmentThemeConfig(ctx context.Context, in *ListFulfillmentThemeConfigRequest, opts ...grpc.CallOption) (*ListFulfillmentThemeConfigResponse, error)
	// GenerateMessageContent 生成消息内容
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GenerateMessageContent(ctx context.Context, in *GenerateMessageContentRequest, opts ...grpc.CallOption) (*GenerateMessageContentResponse, error)
	// SendFulfillmentReport 发送履约报告
	// (-- api-linter: core::0134::response-message-name=disabled --)
	SendFulfillmentReport(ctx context.Context, in *SendFulfillmentReportRequest, opts ...grpc.CallOption) (*SendFulfillmentReportResponse, error)
	// GetFulfillmentReportSendHistory 获取履约报告发送历史记录
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportSendHistory(ctx context.Context, in *GetFulfillmentReportSendHistoryRequest, opts ...grpc.CallOption) (*GetFulfillmentReportSendHistoryResponse, error)
	// GetFulfillmentReportSendResult 获取一个具体的履约报告发送结果
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportSendResult(ctx context.Context, in *GetFulfillmentReportSendResultRequest, opts ...grpc.CallOption) (*GetFulfillmentReportSendResultResponse, error)
	// ListFulfillmentReport 获取履约报告列表
	// (-- api-linter: core::0131::response-message-name=disabled --)
	ListFulfillmentReport(ctx context.Context, in *ListFulfillmentReportRequest, opts ...grpc.CallOption) (*ListFulfillmentReportResponse, error)
	// BatchDeleteFulfillmentReport 批量删除履约报告
	// (-- api-linter: core::0134::response-message-name=disabled --)
	// (-- api-linter: core::0235::plural-method-name=disabled --)
	BatchDeleteFulfillmentReport(ctx context.Context, in *BatchDeleteFulfillmentReportRequest, opts ...grpc.CallOption) (*BatchDeleteFulfillmentReportResponse, error)
	// BatchSendFulfillmentReport 批量发送履约报告
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchSendFulfillmentReport(ctx context.Context, in *BatchSendFulfillmentReportRequest, opts ...grpc.CallOption) (*BatchSendFulfillmentReportResponse, error)
	// IncreaseFulfillmentOpenedCount 增加履约报告打开次数
	// (-- api-linter: core::0134::response-message-name=disabled --)
	IncreaseFulfillmentOpenedCount(ctx context.Context, in *IncreaseFulfillmentOpenedCountRequest, opts ...grpc.CallOption) (*IncreaseFulfillmentOpenedCountResponse, error)
	// ------------------------------
	// fulfillment report 双写相关内部接口
	// ------------------------------
	// SyncFulfillmentReportTemplate 同步履约报告模板（双写）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	SyncFulfillmentReportTemplate(ctx context.Context, in *SyncFulfillmentReportTemplateRequest, opts ...grpc.CallOption) (*SyncFulfillmentReportTemplateResponse, error)
	// BatchSyncFulfillmentReportQuestions 批量同步履约报告模板问题（双写）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchSyncFulfillmentReportQuestions(ctx context.Context, in *BatchSyncFulfillmentReportQuestionsRequest, opts ...grpc.CallOption) (*BatchSyncFulfillmentReportQuestionsResponse, error)
	// SyncFulfillmentReport 同步履约报告（双写）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	SyncFulfillmentReport(ctx context.Context, in *SyncFulfillmentReportRequest, opts ...grpc.CallOption) (*SyncFulfillmentReportResponse, error)
	// SyncFulfillmentReportSendRecord 同步履约报告发送记录（双写）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	SyncFulfillmentReportSendRecord(ctx context.Context, in *SyncFulfillmentReportSendRecordRequest, opts ...grpc.CallOption) (*SyncFulfillmentReportSendRecordResponse, error)
	// ------------------------------
	// 批量迁移接口
	// ------------------------------
	// BatchMigrateTemplates 批量迁移模板（数据迁移专用）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchMigrateTemplates(ctx context.Context, in *BatchMigrateTemplatesRequest, opts ...grpc.CallOption) (*BatchMigrateTemplatesResponse, error)
	// BatchMigrateQuestions 批量迁移问题（数据迁移专用）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchMigrateQuestions(ctx context.Context, in *BatchMigrateQuestionsRequest, opts ...grpc.CallOption) (*BatchMigrateQuestionsResponse, error)
	// BatchMigrateReports 批量迁移报告（数据迁移专用）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchMigrateReports(ctx context.Context, in *BatchMigrateReportsRequest, opts ...grpc.CallOption) (*BatchMigrateReportsResponse, error)
	// BatchMigrateRecords 批量迁移发送记录（数据迁移专用）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchMigrateRecords(ctx context.Context, in *BatchMigrateRecordsRequest, opts ...grpc.CallOption) (*BatchMigrateRecordsResponse, error)
	// ------------------------------
	// 数据查询接口（用于双写验证和读数据源迁移）
	// ------------------------------
	// GetTemplatesByUniqueKeys 通过唯一键批量查询模板
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetTemplatesByUniqueKeys(ctx context.Context, in *GetTemplatesByUniqueKeysRequest, opts ...grpc.CallOption) (*GetTemplatesByUniqueKeysResponse, error)
	// GetQuestionsByTemplateKeys 通过模板唯一键批量查询问题
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetQuestionsByTemplateKeys(ctx context.Context, in *GetQuestionsByTemplateKeysRequest, opts ...grpc.CallOption) (*GetQuestionsByTemplateKeysResponse, error)
	// GetGroomingQuestionsByQuestionKeys 通过 question 唯一键批量查询问题
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetGroomingQuestionsByQuestionKeys(ctx context.Context, in *GetGroomingQuestionsByQuestionKeysRequest, opts ...grpc.CallOption) (*GetGroomingQuestionsByQuestionKeysResponse, error)
	// GetReportsByUniqueKeys 通过唯一键批量查询报告
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetReportsByUniqueKeys(ctx context.Context, in *GetReportsByUniqueKeysRequest, opts ...grpc.CallOption) (*GetReportsByUniqueKeysResponse, error)
	// GetRecordsByReportKeys 通过报告唯一键批量查询发送记录
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetRecordsByReportKeys(ctx context.Context, in *GetRecordsByReportKeysRequest, opts ...grpc.CallOption) (*GetRecordsByReportKeysResponse, error)
}

type fulfillmentReportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFulfillmentReportServiceClient(cc grpc.ClientConnInterface) FulfillmentReportServiceClient {
	return &fulfillmentReportServiceClient{cc}
}

func (c *fulfillmentReportServiceClient) GetFulfillmentReportTemplate(ctx context.Context, in *GetFulfillmentReportTemplateRequest, opts ...grpc.CallOption) (*GetFulfillmentReportTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFulfillmentReportTemplateResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetFulfillmentReportTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) UpdateFulfillmentReportTemplate(ctx context.Context, in *UpdateFulfillmentReportTemplateRequest, opts ...grpc.CallOption) (*UpdateFulfillmentReportTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateFulfillmentReportTemplateResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_UpdateFulfillmentReportTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetFulfillmentReport(ctx context.Context, in *GetFulfillmentReportRequest, opts ...grpc.CallOption) (*GetFulfillmentReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFulfillmentReportResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetFulfillmentReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) UpdateFulfillmentReport(ctx context.Context, in *UpdateFulfillmentReportRequest, opts ...grpc.CallOption) (*UpdateFulfillmentReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateFulfillmentReportResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_UpdateFulfillmentReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetFulfillmentReportSummaryInfo(ctx context.Context, in *GetFulfillmentReportSummaryInfoRequest, opts ...grpc.CallOption) (*GetFulfillmentReportSummaryInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFulfillmentReportSummaryInfoResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetFulfillmentReportSummaryInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetFulfillmentReportRecords(ctx context.Context, in *GetFulfillmentReportRecordsRequest, opts ...grpc.CallOption) (*GetFulfillmentReportRecordsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFulfillmentReportRecordsResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetFulfillmentReportRecords_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetFulfillmentReportPreview(ctx context.Context, in *GetFulfillmentReportPreviewRequest, opts ...grpc.CallOption) (*GetFulfillmentReportPreviewResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFulfillmentReportPreviewResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetFulfillmentReportPreview_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) ListFulfillmentThemeConfig(ctx context.Context, in *ListFulfillmentThemeConfigRequest, opts ...grpc.CallOption) (*ListFulfillmentThemeConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListFulfillmentThemeConfigResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_ListFulfillmentThemeConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GenerateMessageContent(ctx context.Context, in *GenerateMessageContentRequest, opts ...grpc.CallOption) (*GenerateMessageContentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateMessageContentResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GenerateMessageContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) SendFulfillmentReport(ctx context.Context, in *SendFulfillmentReportRequest, opts ...grpc.CallOption) (*SendFulfillmentReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendFulfillmentReportResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_SendFulfillmentReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetFulfillmentReportSendHistory(ctx context.Context, in *GetFulfillmentReportSendHistoryRequest, opts ...grpc.CallOption) (*GetFulfillmentReportSendHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFulfillmentReportSendHistoryResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetFulfillmentReportSendHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetFulfillmentReportSendResult(ctx context.Context, in *GetFulfillmentReportSendResultRequest, opts ...grpc.CallOption) (*GetFulfillmentReportSendResultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFulfillmentReportSendResultResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetFulfillmentReportSendResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) ListFulfillmentReport(ctx context.Context, in *ListFulfillmentReportRequest, opts ...grpc.CallOption) (*ListFulfillmentReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListFulfillmentReportResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_ListFulfillmentReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) BatchDeleteFulfillmentReport(ctx context.Context, in *BatchDeleteFulfillmentReportRequest, opts ...grpc.CallOption) (*BatchDeleteFulfillmentReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchDeleteFulfillmentReportResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_BatchDeleteFulfillmentReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) BatchSendFulfillmentReport(ctx context.Context, in *BatchSendFulfillmentReportRequest, opts ...grpc.CallOption) (*BatchSendFulfillmentReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchSendFulfillmentReportResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_BatchSendFulfillmentReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) IncreaseFulfillmentOpenedCount(ctx context.Context, in *IncreaseFulfillmentOpenedCountRequest, opts ...grpc.CallOption) (*IncreaseFulfillmentOpenedCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IncreaseFulfillmentOpenedCountResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_IncreaseFulfillmentOpenedCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) SyncFulfillmentReportTemplate(ctx context.Context, in *SyncFulfillmentReportTemplateRequest, opts ...grpc.CallOption) (*SyncFulfillmentReportTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncFulfillmentReportTemplateResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_SyncFulfillmentReportTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) BatchSyncFulfillmentReportQuestions(ctx context.Context, in *BatchSyncFulfillmentReportQuestionsRequest, opts ...grpc.CallOption) (*BatchSyncFulfillmentReportQuestionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchSyncFulfillmentReportQuestionsResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_BatchSyncFulfillmentReportQuestions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) SyncFulfillmentReport(ctx context.Context, in *SyncFulfillmentReportRequest, opts ...grpc.CallOption) (*SyncFulfillmentReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncFulfillmentReportResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_SyncFulfillmentReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) SyncFulfillmentReportSendRecord(ctx context.Context, in *SyncFulfillmentReportSendRecordRequest, opts ...grpc.CallOption) (*SyncFulfillmentReportSendRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncFulfillmentReportSendRecordResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_SyncFulfillmentReportSendRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) BatchMigrateTemplates(ctx context.Context, in *BatchMigrateTemplatesRequest, opts ...grpc.CallOption) (*BatchMigrateTemplatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchMigrateTemplatesResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_BatchMigrateTemplates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) BatchMigrateQuestions(ctx context.Context, in *BatchMigrateQuestionsRequest, opts ...grpc.CallOption) (*BatchMigrateQuestionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchMigrateQuestionsResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_BatchMigrateQuestions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) BatchMigrateReports(ctx context.Context, in *BatchMigrateReportsRequest, opts ...grpc.CallOption) (*BatchMigrateReportsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchMigrateReportsResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_BatchMigrateReports_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) BatchMigrateRecords(ctx context.Context, in *BatchMigrateRecordsRequest, opts ...grpc.CallOption) (*BatchMigrateRecordsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchMigrateRecordsResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_BatchMigrateRecords_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetTemplatesByUniqueKeys(ctx context.Context, in *GetTemplatesByUniqueKeysRequest, opts ...grpc.CallOption) (*GetTemplatesByUniqueKeysResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTemplatesByUniqueKeysResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetTemplatesByUniqueKeys_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetQuestionsByTemplateKeys(ctx context.Context, in *GetQuestionsByTemplateKeysRequest, opts ...grpc.CallOption) (*GetQuestionsByTemplateKeysResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetQuestionsByTemplateKeysResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetQuestionsByTemplateKeys_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetGroomingQuestionsByQuestionKeys(ctx context.Context, in *GetGroomingQuestionsByQuestionKeysRequest, opts ...grpc.CallOption) (*GetGroomingQuestionsByQuestionKeysResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGroomingQuestionsByQuestionKeysResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetGroomingQuestionsByQuestionKeys_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetReportsByUniqueKeys(ctx context.Context, in *GetReportsByUniqueKeysRequest, opts ...grpc.CallOption) (*GetReportsByUniqueKeysResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetReportsByUniqueKeysResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetReportsByUniqueKeys_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentReportServiceClient) GetRecordsByReportKeys(ctx context.Context, in *GetRecordsByReportKeysRequest, opts ...grpc.CallOption) (*GetRecordsByReportKeysResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRecordsByReportKeysResponse)
	err := c.cc.Invoke(ctx, FulfillmentReportService_GetRecordsByReportKeys_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FulfillmentReportServiceServer is the server API for FulfillmentReportService service.
// All implementations must embed UnimplementedFulfillmentReportServiceServer
// for forward compatibility.
//
// FulfillmentReportService
type FulfillmentReportServiceServer interface {
	// GetFulfillmentReportTemplate 获取履约报告模板
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportTemplate(context.Context, *GetFulfillmentReportTemplateRequest) (*GetFulfillmentReportTemplateResponse, error)
	// UpdateFulfillmentReportTemplate 更新履约报告模板
	// (-- api-linter: core::0134::response-message-name=disabled --)
	UpdateFulfillmentReportTemplate(context.Context, *UpdateFulfillmentReportTemplateRequest) (*UpdateFulfillmentReportTemplateResponse, error)
	// GetFulfillmentReport 获取履约报告
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReport(context.Context, *GetFulfillmentReportRequest) (*GetFulfillmentReportResponse, error)
	// UpdateFulfillmentReport 更新履约报告
	// (-- api-linter: core::0134::response-message-name=disabled --)
	UpdateFulfillmentReport(context.Context, *UpdateFulfillmentReportRequest) (*UpdateFulfillmentReportResponse, error)
	// GetFulfillmentReportSummaryInfo 获取履约报告详细信息
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportSummaryInfo(context.Context, *GetFulfillmentReportSummaryInfoRequest) (*GetFulfillmentReportSummaryInfoResponse, error)
	// GetFulfillmentReportRecords 根据预约中的所有报告
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportRecords(context.Context, *GetFulfillmentReportRecordsRequest) (*GetFulfillmentReportRecordsResponse, error)
	// GetFulfillmentReportPreview 获取 setting 中展示的 preview report 相关内容
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportPreview(context.Context, *GetFulfillmentReportPreviewRequest) (*GetFulfillmentReportPreviewResponse, error)
	// listFulfillmentThemeConfig 获取所有主题配置
	// (-- api-linter: core::0131::response-message-name=disabled --)
	ListFulfillmentThemeConfig(context.Context, *ListFulfillmentThemeConfigRequest) (*ListFulfillmentThemeConfigResponse, error)
	// GenerateMessageContent 生成消息内容
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GenerateMessageContent(context.Context, *GenerateMessageContentRequest) (*GenerateMessageContentResponse, error)
	// SendFulfillmentReport 发送履约报告
	// (-- api-linter: core::0134::response-message-name=disabled --)
	SendFulfillmentReport(context.Context, *SendFulfillmentReportRequest) (*SendFulfillmentReportResponse, error)
	// GetFulfillmentReportSendHistory 获取履约报告发送历史记录
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportSendHistory(context.Context, *GetFulfillmentReportSendHistoryRequest) (*GetFulfillmentReportSendHistoryResponse, error)
	// GetFulfillmentReportSendResult 获取一个具体的履约报告发送结果
	// (-- api-linter: core::0131::response-message-name=disabled --)
	GetFulfillmentReportSendResult(context.Context, *GetFulfillmentReportSendResultRequest) (*GetFulfillmentReportSendResultResponse, error)
	// ListFulfillmentReport 获取履约报告列表
	// (-- api-linter: core::0131::response-message-name=disabled --)
	ListFulfillmentReport(context.Context, *ListFulfillmentReportRequest) (*ListFulfillmentReportResponse, error)
	// BatchDeleteFulfillmentReport 批量删除履约报告
	// (-- api-linter: core::0134::response-message-name=disabled --)
	// (-- api-linter: core::0235::plural-method-name=disabled --)
	BatchDeleteFulfillmentReport(context.Context, *BatchDeleteFulfillmentReportRequest) (*BatchDeleteFulfillmentReportResponse, error)
	// BatchSendFulfillmentReport 批量发送履约报告
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchSendFulfillmentReport(context.Context, *BatchSendFulfillmentReportRequest) (*BatchSendFulfillmentReportResponse, error)
	// IncreaseFulfillmentOpenedCount 增加履约报告打开次数
	// (-- api-linter: core::0134::response-message-name=disabled --)
	IncreaseFulfillmentOpenedCount(context.Context, *IncreaseFulfillmentOpenedCountRequest) (*IncreaseFulfillmentOpenedCountResponse, error)
	// ------------------------------
	// fulfillment report 双写相关内部接口
	// ------------------------------
	// SyncFulfillmentReportTemplate 同步履约报告模板（双写）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	SyncFulfillmentReportTemplate(context.Context, *SyncFulfillmentReportTemplateRequest) (*SyncFulfillmentReportTemplateResponse, error)
	// BatchSyncFulfillmentReportQuestions 批量同步履约报告模板问题（双写）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchSyncFulfillmentReportQuestions(context.Context, *BatchSyncFulfillmentReportQuestionsRequest) (*BatchSyncFulfillmentReportQuestionsResponse, error)
	// SyncFulfillmentReport 同步履约报告（双写）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	SyncFulfillmentReport(context.Context, *SyncFulfillmentReportRequest) (*SyncFulfillmentReportResponse, error)
	// SyncFulfillmentReportSendRecord 同步履约报告发送记录（双写）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	SyncFulfillmentReportSendRecord(context.Context, *SyncFulfillmentReportSendRecordRequest) (*SyncFulfillmentReportSendRecordResponse, error)
	// ------------------------------
	// 批量迁移接口
	// ------------------------------
	// BatchMigrateTemplates 批量迁移模板（数据迁移专用）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchMigrateTemplates(context.Context, *BatchMigrateTemplatesRequest) (*BatchMigrateTemplatesResponse, error)
	// BatchMigrateQuestions 批量迁移问题（数据迁移专用）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchMigrateQuestions(context.Context, *BatchMigrateQuestionsRequest) (*BatchMigrateQuestionsResponse, error)
	// BatchMigrateReports 批量迁移报告（数据迁移专用）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchMigrateReports(context.Context, *BatchMigrateReportsRequest) (*BatchMigrateReportsResponse, error)
	// BatchMigrateRecords 批量迁移发送记录（数据迁移专用）
	// (-- api-linter: core::0134::response-message-name=disabled --)
	BatchMigrateRecords(context.Context, *BatchMigrateRecordsRequest) (*BatchMigrateRecordsResponse, error)
	// ------------------------------
	// 数据查询接口（用于双写验证和读数据源迁移）
	// ------------------------------
	// GetTemplatesByUniqueKeys 通过唯一键批量查询模板
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetTemplatesByUniqueKeys(context.Context, *GetTemplatesByUniqueKeysRequest) (*GetTemplatesByUniqueKeysResponse, error)
	// GetQuestionsByTemplateKeys 通过模板唯一键批量查询问题
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetQuestionsByTemplateKeys(context.Context, *GetQuestionsByTemplateKeysRequest) (*GetQuestionsByTemplateKeysResponse, error)
	// GetGroomingQuestionsByQuestionKeys 通过 question 唯一键批量查询问题
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetGroomingQuestionsByQuestionKeys(context.Context, *GetGroomingQuestionsByQuestionKeysRequest) (*GetGroomingQuestionsByQuestionKeysResponse, error)
	// GetReportsByUniqueKeys 通过唯一键批量查询报告
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetReportsByUniqueKeys(context.Context, *GetReportsByUniqueKeysRequest) (*GetReportsByUniqueKeysResponse, error)
	// GetRecordsByReportKeys 通过报告唯一键批量查询发送记录
	// (-- api-linter: core::0131::response-message-name=disabled --)
	// (-- api-linter: core::0136::prepositions=disabled --)
	GetRecordsByReportKeys(context.Context, *GetRecordsByReportKeysRequest) (*GetRecordsByReportKeysResponse, error)
	mustEmbedUnimplementedFulfillmentReportServiceServer()
}

// UnimplementedFulfillmentReportServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFulfillmentReportServiceServer struct{}

func (UnimplementedFulfillmentReportServiceServer) GetFulfillmentReportTemplate(context.Context, *GetFulfillmentReportTemplateRequest) (*GetFulfillmentReportTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFulfillmentReportTemplate not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) UpdateFulfillmentReportTemplate(context.Context, *UpdateFulfillmentReportTemplateRequest) (*UpdateFulfillmentReportTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFulfillmentReportTemplate not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetFulfillmentReport(context.Context, *GetFulfillmentReportRequest) (*GetFulfillmentReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFulfillmentReport not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) UpdateFulfillmentReport(context.Context, *UpdateFulfillmentReportRequest) (*UpdateFulfillmentReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFulfillmentReport not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetFulfillmentReportSummaryInfo(context.Context, *GetFulfillmentReportSummaryInfoRequest) (*GetFulfillmentReportSummaryInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFulfillmentReportSummaryInfo not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetFulfillmentReportRecords(context.Context, *GetFulfillmentReportRecordsRequest) (*GetFulfillmentReportRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFulfillmentReportRecords not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetFulfillmentReportPreview(context.Context, *GetFulfillmentReportPreviewRequest) (*GetFulfillmentReportPreviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFulfillmentReportPreview not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) ListFulfillmentThemeConfig(context.Context, *ListFulfillmentThemeConfigRequest) (*ListFulfillmentThemeConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFulfillmentThemeConfig not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GenerateMessageContent(context.Context, *GenerateMessageContentRequest) (*GenerateMessageContentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateMessageContent not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) SendFulfillmentReport(context.Context, *SendFulfillmentReportRequest) (*SendFulfillmentReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendFulfillmentReport not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetFulfillmentReportSendHistory(context.Context, *GetFulfillmentReportSendHistoryRequest) (*GetFulfillmentReportSendHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFulfillmentReportSendHistory not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetFulfillmentReportSendResult(context.Context, *GetFulfillmentReportSendResultRequest) (*GetFulfillmentReportSendResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFulfillmentReportSendResult not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) ListFulfillmentReport(context.Context, *ListFulfillmentReportRequest) (*ListFulfillmentReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFulfillmentReport not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) BatchDeleteFulfillmentReport(context.Context, *BatchDeleteFulfillmentReportRequest) (*BatchDeleteFulfillmentReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteFulfillmentReport not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) BatchSendFulfillmentReport(context.Context, *BatchSendFulfillmentReportRequest) (*BatchSendFulfillmentReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchSendFulfillmentReport not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) IncreaseFulfillmentOpenedCount(context.Context, *IncreaseFulfillmentOpenedCountRequest) (*IncreaseFulfillmentOpenedCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IncreaseFulfillmentOpenedCount not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) SyncFulfillmentReportTemplate(context.Context, *SyncFulfillmentReportTemplateRequest) (*SyncFulfillmentReportTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncFulfillmentReportTemplate not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) BatchSyncFulfillmentReportQuestions(context.Context, *BatchSyncFulfillmentReportQuestionsRequest) (*BatchSyncFulfillmentReportQuestionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchSyncFulfillmentReportQuestions not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) SyncFulfillmentReport(context.Context, *SyncFulfillmentReportRequest) (*SyncFulfillmentReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncFulfillmentReport not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) SyncFulfillmentReportSendRecord(context.Context, *SyncFulfillmentReportSendRecordRequest) (*SyncFulfillmentReportSendRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncFulfillmentReportSendRecord not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) BatchMigrateTemplates(context.Context, *BatchMigrateTemplatesRequest) (*BatchMigrateTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchMigrateTemplates not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) BatchMigrateQuestions(context.Context, *BatchMigrateQuestionsRequest) (*BatchMigrateQuestionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchMigrateQuestions not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) BatchMigrateReports(context.Context, *BatchMigrateReportsRequest) (*BatchMigrateReportsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchMigrateReports not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) BatchMigrateRecords(context.Context, *BatchMigrateRecordsRequest) (*BatchMigrateRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchMigrateRecords not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetTemplatesByUniqueKeys(context.Context, *GetTemplatesByUniqueKeysRequest) (*GetTemplatesByUniqueKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTemplatesByUniqueKeys not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetQuestionsByTemplateKeys(context.Context, *GetQuestionsByTemplateKeysRequest) (*GetQuestionsByTemplateKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQuestionsByTemplateKeys not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetGroomingQuestionsByQuestionKeys(context.Context, *GetGroomingQuestionsByQuestionKeysRequest) (*GetGroomingQuestionsByQuestionKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroomingQuestionsByQuestionKeys not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetReportsByUniqueKeys(context.Context, *GetReportsByUniqueKeysRequest) (*GetReportsByUniqueKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReportsByUniqueKeys not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) GetRecordsByReportKeys(context.Context, *GetRecordsByReportKeysRequest) (*GetRecordsByReportKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordsByReportKeys not implemented")
}
func (UnimplementedFulfillmentReportServiceServer) mustEmbedUnimplementedFulfillmentReportServiceServer() {
}
func (UnimplementedFulfillmentReportServiceServer) testEmbeddedByValue() {}

// UnsafeFulfillmentReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FulfillmentReportServiceServer will
// result in compilation errors.
type UnsafeFulfillmentReportServiceServer interface {
	mustEmbedUnimplementedFulfillmentReportServiceServer()
}

func RegisterFulfillmentReportServiceServer(s grpc.ServiceRegistrar, srv FulfillmentReportServiceServer) {
	// If the following call pancis, it indicates UnimplementedFulfillmentReportServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FulfillmentReportService_ServiceDesc, srv)
}

func _FulfillmentReportService_GetFulfillmentReportTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFulfillmentReportTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetFulfillmentReportTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportTemplate(ctx, req.(*GetFulfillmentReportTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_UpdateFulfillmentReportTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFulfillmentReportTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).UpdateFulfillmentReportTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_UpdateFulfillmentReportTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).UpdateFulfillmentReportTemplate(ctx, req.(*UpdateFulfillmentReportTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetFulfillmentReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFulfillmentReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetFulfillmentReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReport(ctx, req.(*GetFulfillmentReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_UpdateFulfillmentReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFulfillmentReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).UpdateFulfillmentReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_UpdateFulfillmentReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).UpdateFulfillmentReport(ctx, req.(*UpdateFulfillmentReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetFulfillmentReportSummaryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFulfillmentReportSummaryInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportSummaryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetFulfillmentReportSummaryInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportSummaryInfo(ctx, req.(*GetFulfillmentReportSummaryInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetFulfillmentReportRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFulfillmentReportRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetFulfillmentReportRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportRecords(ctx, req.(*GetFulfillmentReportRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetFulfillmentReportPreview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFulfillmentReportPreviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportPreview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetFulfillmentReportPreview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportPreview(ctx, req.(*GetFulfillmentReportPreviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_ListFulfillmentThemeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFulfillmentThemeConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).ListFulfillmentThemeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_ListFulfillmentThemeConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).ListFulfillmentThemeConfig(ctx, req.(*ListFulfillmentThemeConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GenerateMessageContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateMessageContentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GenerateMessageContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GenerateMessageContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GenerateMessageContent(ctx, req.(*GenerateMessageContentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_SendFulfillmentReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendFulfillmentReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).SendFulfillmentReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_SendFulfillmentReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).SendFulfillmentReport(ctx, req.(*SendFulfillmentReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetFulfillmentReportSendHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFulfillmentReportSendHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportSendHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetFulfillmentReportSendHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportSendHistory(ctx, req.(*GetFulfillmentReportSendHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetFulfillmentReportSendResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFulfillmentReportSendResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportSendResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetFulfillmentReportSendResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetFulfillmentReportSendResult(ctx, req.(*GetFulfillmentReportSendResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_ListFulfillmentReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFulfillmentReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).ListFulfillmentReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_ListFulfillmentReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).ListFulfillmentReport(ctx, req.(*ListFulfillmentReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_BatchDeleteFulfillmentReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteFulfillmentReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).BatchDeleteFulfillmentReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_BatchDeleteFulfillmentReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).BatchDeleteFulfillmentReport(ctx, req.(*BatchDeleteFulfillmentReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_BatchSendFulfillmentReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendFulfillmentReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).BatchSendFulfillmentReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_BatchSendFulfillmentReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).BatchSendFulfillmentReport(ctx, req.(*BatchSendFulfillmentReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_IncreaseFulfillmentOpenedCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncreaseFulfillmentOpenedCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).IncreaseFulfillmentOpenedCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_IncreaseFulfillmentOpenedCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).IncreaseFulfillmentOpenedCount(ctx, req.(*IncreaseFulfillmentOpenedCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_SyncFulfillmentReportTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncFulfillmentReportTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).SyncFulfillmentReportTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_SyncFulfillmentReportTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).SyncFulfillmentReportTemplate(ctx, req.(*SyncFulfillmentReportTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_BatchSyncFulfillmentReportQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSyncFulfillmentReportQuestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).BatchSyncFulfillmentReportQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_BatchSyncFulfillmentReportQuestions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).BatchSyncFulfillmentReportQuestions(ctx, req.(*BatchSyncFulfillmentReportQuestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_SyncFulfillmentReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncFulfillmentReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).SyncFulfillmentReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_SyncFulfillmentReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).SyncFulfillmentReport(ctx, req.(*SyncFulfillmentReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_SyncFulfillmentReportSendRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncFulfillmentReportSendRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).SyncFulfillmentReportSendRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_SyncFulfillmentReportSendRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).SyncFulfillmentReportSendRecord(ctx, req.(*SyncFulfillmentReportSendRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_BatchMigrateTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchMigrateTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).BatchMigrateTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_BatchMigrateTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).BatchMigrateTemplates(ctx, req.(*BatchMigrateTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_BatchMigrateQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchMigrateQuestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).BatchMigrateQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_BatchMigrateQuestions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).BatchMigrateQuestions(ctx, req.(*BatchMigrateQuestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_BatchMigrateReports_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchMigrateReportsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).BatchMigrateReports(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_BatchMigrateReports_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).BatchMigrateReports(ctx, req.(*BatchMigrateReportsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_BatchMigrateRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchMigrateRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).BatchMigrateRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_BatchMigrateRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).BatchMigrateRecords(ctx, req.(*BatchMigrateRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetTemplatesByUniqueKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTemplatesByUniqueKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetTemplatesByUniqueKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetTemplatesByUniqueKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetTemplatesByUniqueKeys(ctx, req.(*GetTemplatesByUniqueKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetQuestionsByTemplateKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuestionsByTemplateKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetQuestionsByTemplateKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetQuestionsByTemplateKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetQuestionsByTemplateKeys(ctx, req.(*GetQuestionsByTemplateKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetGroomingQuestionsByQuestionKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroomingQuestionsByQuestionKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetGroomingQuestionsByQuestionKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetGroomingQuestionsByQuestionKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetGroomingQuestionsByQuestionKeys(ctx, req.(*GetGroomingQuestionsByQuestionKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetReportsByUniqueKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReportsByUniqueKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetReportsByUniqueKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetReportsByUniqueKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetReportsByUniqueKeys(ctx, req.(*GetReportsByUniqueKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentReportService_GetRecordsByReportKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordsByReportKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentReportServiceServer).GetRecordsByReportKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FulfillmentReportService_GetRecordsByReportKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentReportServiceServer).GetRecordsByReportKeys(ctx, req.(*GetRecordsByReportKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FulfillmentReportService_ServiceDesc is the grpc.ServiceDesc for FulfillmentReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FulfillmentReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.fulfillment.v1.FulfillmentReportService",
	HandlerType: (*FulfillmentReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFulfillmentReportTemplate",
			Handler:    _FulfillmentReportService_GetFulfillmentReportTemplate_Handler,
		},
		{
			MethodName: "UpdateFulfillmentReportTemplate",
			Handler:    _FulfillmentReportService_UpdateFulfillmentReportTemplate_Handler,
		},
		{
			MethodName: "GetFulfillmentReport",
			Handler:    _FulfillmentReportService_GetFulfillmentReport_Handler,
		},
		{
			MethodName: "UpdateFulfillmentReport",
			Handler:    _FulfillmentReportService_UpdateFulfillmentReport_Handler,
		},
		{
			MethodName: "GetFulfillmentReportSummaryInfo",
			Handler:    _FulfillmentReportService_GetFulfillmentReportSummaryInfo_Handler,
		},
		{
			MethodName: "GetFulfillmentReportRecords",
			Handler:    _FulfillmentReportService_GetFulfillmentReportRecords_Handler,
		},
		{
			MethodName: "GetFulfillmentReportPreview",
			Handler:    _FulfillmentReportService_GetFulfillmentReportPreview_Handler,
		},
		{
			MethodName: "ListFulfillmentThemeConfig",
			Handler:    _FulfillmentReportService_ListFulfillmentThemeConfig_Handler,
		},
		{
			MethodName: "GenerateMessageContent",
			Handler:    _FulfillmentReportService_GenerateMessageContent_Handler,
		},
		{
			MethodName: "SendFulfillmentReport",
			Handler:    _FulfillmentReportService_SendFulfillmentReport_Handler,
		},
		{
			MethodName: "GetFulfillmentReportSendHistory",
			Handler:    _FulfillmentReportService_GetFulfillmentReportSendHistory_Handler,
		},
		{
			MethodName: "GetFulfillmentReportSendResult",
			Handler:    _FulfillmentReportService_GetFulfillmentReportSendResult_Handler,
		},
		{
			MethodName: "ListFulfillmentReport",
			Handler:    _FulfillmentReportService_ListFulfillmentReport_Handler,
		},
		{
			MethodName: "BatchDeleteFulfillmentReport",
			Handler:    _FulfillmentReportService_BatchDeleteFulfillmentReport_Handler,
		},
		{
			MethodName: "BatchSendFulfillmentReport",
			Handler:    _FulfillmentReportService_BatchSendFulfillmentReport_Handler,
		},
		{
			MethodName: "IncreaseFulfillmentOpenedCount",
			Handler:    _FulfillmentReportService_IncreaseFulfillmentOpenedCount_Handler,
		},
		{
			MethodName: "SyncFulfillmentReportTemplate",
			Handler:    _FulfillmentReportService_SyncFulfillmentReportTemplate_Handler,
		},
		{
			MethodName: "BatchSyncFulfillmentReportQuestions",
			Handler:    _FulfillmentReportService_BatchSyncFulfillmentReportQuestions_Handler,
		},
		{
			MethodName: "SyncFulfillmentReport",
			Handler:    _FulfillmentReportService_SyncFulfillmentReport_Handler,
		},
		{
			MethodName: "SyncFulfillmentReportSendRecord",
			Handler:    _FulfillmentReportService_SyncFulfillmentReportSendRecord_Handler,
		},
		{
			MethodName: "BatchMigrateTemplates",
			Handler:    _FulfillmentReportService_BatchMigrateTemplates_Handler,
		},
		{
			MethodName: "BatchMigrateQuestions",
			Handler:    _FulfillmentReportService_BatchMigrateQuestions_Handler,
		},
		{
			MethodName: "BatchMigrateReports",
			Handler:    _FulfillmentReportService_BatchMigrateReports_Handler,
		},
		{
			MethodName: "BatchMigrateRecords",
			Handler:    _FulfillmentReportService_BatchMigrateRecords_Handler,
		},
		{
			MethodName: "GetTemplatesByUniqueKeys",
			Handler:    _FulfillmentReportService_GetTemplatesByUniqueKeys_Handler,
		},
		{
			MethodName: "GetQuestionsByTemplateKeys",
			Handler:    _FulfillmentReportService_GetQuestionsByTemplateKeys_Handler,
		},
		{
			MethodName: "GetGroomingQuestionsByQuestionKeys",
			Handler:    _FulfillmentReportService_GetGroomingQuestionsByQuestionKeys_Handler,
		},
		{
			MethodName: "GetReportsByUniqueKeys",
			Handler:    _FulfillmentReportService_GetReportsByUniqueKeys_Handler,
		},
		{
			MethodName: "GetRecordsByReportKeys",
			Handler:    _FulfillmentReportService_GetRecordsByReportKeys_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/fulfillment/v1/fulfillment_report_service.proto",
}
