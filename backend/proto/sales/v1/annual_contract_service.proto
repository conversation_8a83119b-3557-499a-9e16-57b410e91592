syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

import "backend/proto/sales/v1/sales_enums.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// AnnualContractService
service AnnualContractService {
  // CreateAnnualContract
  rpc CreateAnnualContract(CreateAnnualContractRequest) returns (AnnualContract);
  // GetAnnualContract
  rpc GetAnnualContract(GetAnnualContractRequest) returns (AnnualContract);
  // ListAnnualContracts
  rpc ListAnnualContracts(ListAnnualContractsRequest) returns (ListAnnualContractsResponse);
  // CountAnnualContracts
  rpc CountAnnualContracts(CountAnnualContractsRequest) returns (CountAnnualContractsResponse);
  // SignAnnualContract
  rpc SignAnnualContract(SignAnnualContractRequest) returns (SignAnnualContractResponse);
  // DeleteAnnualContract
  rpc DeleteAnnualContract(DeleteAnnualContractRequest) returns (google.protobuf.Empty);
}

// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: We need to do this because 没必要嵌套一层，不好用. --)
// CreateAnnualContractRequest
message CreateAnnualContractRequest {
  // creator
  string creator = 1 [(validate.rules).string = {min_len: 1}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // subscription plan
  SubscriptionPlan subscription_plan = 3 [(validate.rules).enum = {
    defined_only: true,
    not_in: [0]
  }];
  // subscription term months
  // e.g., 12 means 12 months (1 year)
  int32 subscription_term_months = 4 [(validate.rules).int32 = {gt: 0}];
  // discount percentage of subscription
  // e.g., "10" means 10%
  string discount_percentage = 5 [(validate.rules).string = {pattern: "^[0-9]+(\\.[0-9]{1,2})?$"}];

  // number of boarding & daycare locations
  int32 bd_location_count = 6 [(validate.rules).int32 = {gte: 0}];
  // number of grooming locations
  int32 grooming_location_count = 7 [(validate.rules).int32 = {gte: 0}];
  // number of grooming vans
  int32 grooming_van_count = 8 [(validate.rules).int32 = {gte: 0}];
}

// GetAnnualContractRequest
message GetAnnualContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: We need to do this because we don't use parent. --)
// ListAnnualContractsRequest
message ListAnnualContractsRequest {
  // 每页返回条数，最大不超过 100，默认建议为 20
  int32 page_size = 1 [(validate.rules).int32 = {gt: 0, lte: 100}];

  // 上一页返回的分页 token（用于获取下一页）
  string page_token = 2;

  // filters
  AnnualContractQueryFilters filters = 3;
}

// ListAnnualContractsResponse
message ListAnnualContractsResponse {
  // 合同列表
  repeated AnnualContract annual_contracts = 1;

  // 下一页的分页 token，如果为空表示无更多结果
  string next_page_token = 2;
}

// CountAnnualContractsRequest
message CountAnnualContractsRequest {
  // filters
  AnnualContractQueryFilters filters = 1;
}

// CountAnnualContractsResponse
message CountAnnualContractsResponse {
  // count
  int64 count = 1;
}

// SignAnnualContractRequest
message SignAnnualContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
  // signature uri (url)
  string signature_uri = 2 [(validate.rules).string = {uri: true}];
}

// SignAnnualContractResponse
message SignAnnualContractResponse {
  // contract
  AnnualContract contract = 1;
}

// DeleteAnnualContractRequest
message DeleteAnnualContractRequest {
  // id
  string id = 1 [(validate.rules).string = {min_len: 1}];
}

// AnnualContract
message AnnualContract {
  // id
  string id = 1;
  // template id
  string template_id = 2;
  // metadata
  Metadata metadata = 3;
  // params
  Parameters parameters = 4;
  // content
  string content = 5;
  // creator
  string creator = 6;
  // create time
  google.protobuf.Timestamp create_time = 7;
  // update time
  google.protobuf.Timestamp update_time = 8;
  // sign time
  optional google.protobuf.Timestamp sign_time = 9;
  // signature uri (url)
  optional string signature_uri = 10;

  // metadata
  message Metadata {
    // company id
    int64 company_id = 1;
    // account id
    int64 account_id = 2;
    // subscription plan
    SubscriptionPlan subscription_plan = 4;
  }

  // parameters for rendering
  message Parameters {
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // company name
    string company_name = 1;
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // owner name
    string owner_name = 2;
    // owner email
    string owner_email = 3;
    // address
    string address = 4;
    // (-- api-linter: core::0122::name-suffix=disabled
    //     aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
    // subscription plan name
    string subscription_plan_name = 5;
    // subscription term months
    int32 subscription_term_months = 6;
    // discount percentage
    string discount_percentage = 7;
    // total amount
    string total_amount = 8;
    // grooming location
    optional ProductLineItem grooming_location = 9;
    // boarding & daycare location
    optional ProductLineItem bd_location = 10;
    // grooming van
    optional ProductLineItem grooming_van = 11;
  }

  // product line item
  message ProductLineItem {
    // The number of units.
    int32 quantity = 1;
    // The price per unit before any discounts.
    string unit_price = 2;
    // The final price per unit after discounts have been applied.
    string discounted_unit_price = 3;
  }
}

// Query filters for AnnualContract
message AnnualContractQueryFilters {
  // company_id
  optional int64 company_id = 1;
  // account_id
  optional int64 account_id = 2;
  // owner email (prefix like)
  optional string owner_email = 3;
  // creator (prefix like)
  optional string creator = 4;
  // if the contract is signed
  optional bool signed = 5;
}
