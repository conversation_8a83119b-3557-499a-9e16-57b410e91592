// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: backend/proto/offering/v1/service_service.proto

package offeringpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DeleteServiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteServiceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteServiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteServiceRequestMultiError, or nil if none found.
func (m *DeleteServiceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteServiceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceId

	if len(errors) > 0 {
		return DeleteServiceRequestMultiError(errors)
	}

	return nil
}

// DeleteServiceRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteServiceRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteServiceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteServiceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteServiceRequestMultiError) AllErrors() []error { return m }

// DeleteServiceRequestValidationError is the validation error returned by
// DeleteServiceRequest.Validate if the designated constraints aren't met.
type DeleteServiceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteServiceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteServiceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteServiceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteServiceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteServiceRequestValidationError) ErrorName() string {
	return "DeleteServiceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteServiceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteServiceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteServiceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteServiceRequestValidationError{}

// Validate checks the field values on DeleteServiceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteServiceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteServiceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteServiceResponseMultiError, or nil if none found.
func (m *DeleteServiceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteServiceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteServiceResponseMultiError(errors)
	}

	return nil
}

// DeleteServiceResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteServiceResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteServiceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteServiceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteServiceResponseMultiError) AllErrors() []error { return m }

// DeleteServiceResponseValidationError is the validation error returned by
// DeleteServiceResponse.Validate if the designated constraints aren't met.
type DeleteServiceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteServiceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteServiceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteServiceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteServiceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteServiceResponseValidationError) ErrorName() string {
	return "DeleteServiceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteServiceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteServiceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteServiceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteServiceResponseValidationError{}

// Validate checks the field values on UpdateServiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateServiceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateServiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateServiceRequestMultiError, or nil if none found.
func (m *UpdateServiceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateServiceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetService()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateServiceRequestValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateServiceRequestValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateServiceRequestValidationError{
				field:  "Service",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateServiceRequestMultiError(errors)
	}

	return nil
}

// UpdateServiceRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateServiceRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateServiceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateServiceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateServiceRequestMultiError) AllErrors() []error { return m }

// UpdateServiceRequestValidationError is the validation error returned by
// UpdateServiceRequest.Validate if the designated constraints aren't met.
type UpdateServiceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateServiceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateServiceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateServiceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateServiceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateServiceRequestValidationError) ErrorName() string {
	return "UpdateServiceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateServiceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateServiceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateServiceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateServiceRequestValidationError{}

// Validate checks the field values on UpdateServiceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateServiceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateServiceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateServiceResponseMultiError, or nil if none found.
func (m *UpdateServiceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateServiceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateServiceResponseMultiError(errors)
	}

	return nil
}

// UpdateServiceResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateServiceResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateServiceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateServiceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateServiceResponseMultiError) AllErrors() []error { return m }

// UpdateServiceResponseValidationError is the validation error returned by
// UpdateServiceResponse.Validate if the designated constraints aren't met.
type UpdateServiceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateServiceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateServiceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateServiceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateServiceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateServiceResponseValidationError) ErrorName() string {
	return "UpdateServiceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateServiceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateServiceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateServiceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateServiceResponseValidationError{}

// Validate checks the field values on CreateServiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateServiceRequestMultiError, or nil if none found.
func (m *CreateServiceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetService()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateServiceRequestValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateServiceRequestValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateServiceRequestValidationError{
				field:  "Service",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateServiceRequestMultiError(errors)
	}

	return nil
}

// CreateServiceRequestMultiError is an error wrapping multiple validation
// errors returned by CreateServiceRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateServiceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceRequestMultiError) AllErrors() []error { return m }

// CreateServiceRequestValidationError is the validation error returned by
// CreateServiceRequest.Validate if the designated constraints aren't met.
type CreateServiceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceRequestValidationError) ErrorName() string {
	return "CreateServiceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceRequestValidationError{}

// Validate checks the field values on CreateServiceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateServiceResponseMultiError, or nil if none found.
func (m *CreateServiceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceId

	if len(errors) > 0 {
		return CreateServiceResponseMultiError(errors)
	}

	return nil
}

// CreateServiceResponseMultiError is an error wrapping multiple validation
// errors returned by CreateServiceResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateServiceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceResponseMultiError) AllErrors() []error { return m }

// CreateServiceResponseValidationError is the validation error returned by
// CreateServiceResponse.Validate if the designated constraints aren't met.
type CreateServiceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceResponseValidationError) ErrorName() string {
	return "CreateServiceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceResponseValidationError{}
