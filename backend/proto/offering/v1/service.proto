syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/offering/v1/common.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// Defines the structure for a service, which acts as a blueprint for creating specific service instances.
message Service {
  // Primary key ID of the service
  int64 id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The level of the organization (e.g., "enterprise", "company").
  OrganizationType organization_type = 2 [(google.api.field_behavior) = IMMUTABLE];

  // The ID of the organization.
  int64 organization_id = 3 [(google.api.field_behavior) = IMMUTABLE];

  // The ID of the care type associated with this service.
  int64 care_type_id = 4 [(google.api.field_behavior) = REQUIRED];

  // The ID of the category this service.
  int64 category_id = 5 [(google.api.field_behavior) = REQUIRED];

  // Name of the service, unique within the same company
  string name = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional description of the service
  string description = 7 [(google.api.field_behavior) = OPTIONAL];

  // A color code associated with the service for UI purposes.
  string color_code = 8 [(google.api.field_behavior) = OPTIONAL];

  // The sorting order of the service.
  int64 sort = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A list of image URLs for the service.
  repeated string images = 10 [(google.api.field_behavior) = OPTIONAL];

  // The source of the service.
  backend.proto.offering.v1.ServiceSource source = 11 [(google.api.field_behavior) = REQUIRED];

  // Whether the service is currently active
  bool is_active = 12 [(google.api.field_behavior) = REQUIRED];

  // The timestamp when the service was created.
  google.protobuf.Timestamp create_time = 20 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The timestamp when the service was last updated.
  google.protobuf.Timestamp update_time = 21 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The timestamp when the service was deleted.
  google.protobuf.Timestamp delete_time = 22 [(google.api.field_behavior) = OUTPUT_ONLY];

  // A map of attribute values for this service .
  // The key is the string representation of the AttributeKey enum.
  map<string, google.protobuf.Value> attributes = 99 [(google.api.field_behavior) = OPTIONAL];
}
