// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: backend/proto/offering/v1/service.proto

package offeringpb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Defines the structure for a service, which acts as a blueprint for creating specific service instances.
type Service struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the service
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The level of the organization (e.g., "enterprise", "company").
	OrganizationType OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.offering.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The ID of the care type associated with this service.
	CareTypeId int64 `protobuf:"varint,4,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The ID of the category this service.
	CategoryId int64 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// Name of the service, unique within the same company
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// Optional description of the service
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// A color code associated with the service for UI purposes.
	ColorCode string `protobuf:"bytes,8,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// The sorting order of the service.
	Sort int64 `protobuf:"varint,9,opt,name=sort,proto3" json:"sort,omitempty"`
	// A list of image URLs for the service.
	Images []string `protobuf:"bytes,10,rep,name=images,proto3" json:"images,omitempty"`
	// The source of the service.
	Source ServiceSource `protobuf:"varint,11,opt,name=source,proto3,enum=backend.proto.offering.v1.ServiceSource" json:"source,omitempty"`
	// Whether the service is currently active
	IsActive bool `protobuf:"varint,12,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// The timestamp when the service was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the service was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The timestamp when the service was deleted.
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
	// A map of attribute values for this service .
	// The key is the string representation of the AttributeKey enum.
	Attributes    map[string]*structpb.Value `protobuf:"bytes,99,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Service) Reset() {
	*x = Service{}
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *Service) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Service) GetOrganizationType() OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return OrganizationType_ORGANIZATION_TYPE_UNSPECIFIED
}

func (x *Service) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *Service) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *Service) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Service) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *Service) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *Service) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *Service) GetSource() ServiceSource {
	if x != nil {
		return x.Source
	}
	return ServiceSource_SERVICE_SOURCE_UNSPECIFIED
}

func (x *Service) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Service) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Service) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Service) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *Service) GetAttributes() map[string]*structpb.Value {
	if x != nil {
		return x.Attributes
	}
	return nil
}

var File_backend_proto_offering_v1_service_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_service_proto_rawDesc = "" +
	"\n" +
	"'backend/proto/offering/v1/service.proto\x12\x19backend.proto.offering.v1\x1a&backend/proto/offering/v1/common.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf1\x06\n" +
	"\aService\x12\x13\n" +
	"\x02id\x18\x01 \x01(\x03B\x03\xe0A\x03R\x02id\x12]\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2+.backend.proto.offering.v1.OrganizationTypeB\x03\xe0A\x05R\x10organizationType\x12,\n" +
	"\x0forganization_id\x18\x03 \x01(\x03B\x03\xe0A\x05R\x0eorganizationId\x12%\n" +
	"\fcare_type_id\x18\x04 \x01(\x03B\x03\xe0A\x02R\n" +
	"careTypeId\x12$\n" +
	"\vcategory_id\x18\x05 \x01(\x03B\x03\xe0A\x02R\n" +
	"categoryId\x12\x17\n" +
	"\x04name\x18\x06 \x01(\tB\x03\xe0A\x02R\x04name\x12%\n" +
	"\vdescription\x18\a \x01(\tB\x03\xe0A\x01R\vdescription\x12\"\n" +
	"\n" +
	"color_code\x18\b \x01(\tB\x03\xe0A\x01R\tcolorCode\x12\x17\n" +
	"\x04sort\x18\t \x01(\x03B\x03\xe0A\x03R\x04sort\x12\x1b\n" +
	"\x06images\x18\n" +
	" \x03(\tB\x03\xe0A\x01R\x06images\x12E\n" +
	"\x06source\x18\v \x01(\x0e2(.backend.proto.offering.v1.ServiceSourceB\x03\xe0A\x02R\x06source\x12 \n" +
	"\tis_active\x18\f \x01(\bB\x03\xe0A\x02R\bisActive\x12@\n" +
	"\vcreate_time\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12@\n" +
	"\vupdate_time\x18\x15 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\x16 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"deleteTime\x12W\n" +
	"\n" +
	"attributes\x18c \x03(\v22.backend.proto.offering.v1.Service.AttributesEntryB\x03\xe0A\x01R\n" +
	"attributes\x1aU\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12,\n" +
	"\x05value\x18\x02 \x01(\v2\x16.google.protobuf.ValueR\x05value:\x028\x01Bk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_service_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_service_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_service_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_service_proto_rawDescData
}

var file_backend_proto_offering_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_offering_v1_service_proto_goTypes = []any{
	(*Service)(nil),               // 0: backend.proto.offering.v1.Service
	nil,                           // 1: backend.proto.offering.v1.Service.AttributesEntry
	(OrganizationType)(0),         // 2: backend.proto.offering.v1.OrganizationType
	(ServiceSource)(0),            // 3: backend.proto.offering.v1.ServiceSource
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
	(*structpb.Value)(nil),        // 5: google.protobuf.Value
}
var file_backend_proto_offering_v1_service_proto_depIdxs = []int32{
	2, // 0: backend.proto.offering.v1.Service.organization_type:type_name -> backend.proto.offering.v1.OrganizationType
	3, // 1: backend.proto.offering.v1.Service.source:type_name -> backend.proto.offering.v1.ServiceSource
	4, // 2: backend.proto.offering.v1.Service.create_time:type_name -> google.protobuf.Timestamp
	4, // 3: backend.proto.offering.v1.Service.update_time:type_name -> google.protobuf.Timestamp
	4, // 4: backend.proto.offering.v1.Service.delete_time:type_name -> google.protobuf.Timestamp
	1, // 5: backend.proto.offering.v1.Service.attributes:type_name -> backend.proto.offering.v1.Service.AttributesEntry
	5, // 6: backend.proto.offering.v1.Service.AttributesEntry.value:type_name -> google.protobuf.Value
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_service_proto_init() }
func file_backend_proto_offering_v1_service_proto_init() {
	if File_backend_proto_offering_v1_service_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_service_proto_rawDesc), len(file_backend_proto_offering_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_offering_v1_service_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_service_proto = out.File
	file_backend_proto_offering_v1_service_proto_goTypes = nil
	file_backend_proto_offering_v1_service_proto_depIdxs = nil
}
