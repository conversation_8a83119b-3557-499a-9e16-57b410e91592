syntax = "proto3";

package backend.proto.offering.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// 分页信息
message PaginationRef {
  // 偏移量，默认0
  int32 offset = 1;
  // 每页数量，默认200
  int32 limit = 2;
}

// Organization level.
enum OrganizationType {
  // Unspecified organization type.
  ORGANIZATION_TYPE_UNSPECIFIED = 0;
  // Enterprise level.
  ORGANIZATION_TYPE_ENTERPRISE = 1;
  // Company level.
  ORGANIZATION_TYPE_COMPANY = 2;
  // Business level.
  ORGANIZATION_TYPE_BUSINESS = 3;
}

// Defines the unique keys for all supported attributes in the system.
// This enum is the single source of truth for what attributes are available.
enum AttributeKey {
  // Unspecified attribute key.
  ATTRIBUTE_KEY_UNSPECIFIED = 0;
  // Duration in minutes.
  ATTRIBUTE_KEY_DURATION = 1;
  // Max stay duration in minutes.
  ATTRIBUTE_KEY_MAX_DURATION = 2;
  // Indicates if lodging is required.
  ATTRIBUTE_KEY_IS_LODGING_REQUIRED = 3;
  // Indicates if an evaluation is required.
  ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED = 4;
  // Indicates if an evaluation for observation is required.
  ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED = 5;
  // The ID of the required evaluation.
  ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID = 6;
  // The number of sessions.
  ATTRIBUTE_KEY_SESSION_COUNT = 7;
  // The minimum duration of a session in minutes.
  ATTRIBUTE_KEY_SESSION_DURATION_MIN = 8;
  // The capacity of the class.
  ATTRIBUTE_KEY_CLASS_CAPACITY = 9;
  // Indicates if a prerequisite class is required.
  ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED = 10;
  // A list of prerequisite class IDs.
  ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS = 11;
  // Indicates if staff can be auto-assigned.
  ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN = 12;
  // Indicates if the service is resettable.
  ATTRIBUTE_KEY_IS_RESETTABLE = 13;
  // The interval in days for resetting.
  ATTRIBUTE_KEY_RESET_INTERVAL_DAYS = 14;
  // Indicates if pet type binding is supported.
  ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING = 15;
  // Indicates if coat type binding is supported.
  ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING = 16;
  // Indicates if pet size binding is supported.
  ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING = 17;
  // Indicates if pet code binding is supported.
  ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING = 18;
  // Indicates if staff binding is supported.
  ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING = 19;
  // Indicates if lodging type binding is supported.
  ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING = 20;
  // Indicates if auto-rollover is supported.
  ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER = 21;
}

// 排序方式
enum SortType {
  // SORT_TYPE_UNSPECIFIED 默认排序类型
  SORT_TYPE_UNSPECIFIED = 0;
  // SORT_TYPE_START_TIME 按服务开始时间
  SORT_TYPE_START_TIME = 1;
}

// 系统预设的服务类型
enum SystemCareType {
  // 未知护理类型
  SYSTEM_CARE_TYPE_UNSPECIFIED = 0;
  // GROOMING类型
  SYSTEM_CARE_TYPE_GROOMING = 1;
  // BOARDING类型
  SYSTEM_CARE_TYPE_BOARDING = 2;
  // DAYCARE类型
  SYSTEM_CARE_TYPE_DAYCARE = 3;
  // EVALUATION类型
  SYSTEM_CARE_TYPE_EVALUATION = 4;
  // DOG_WALKING类型
  SYSTEM_CARE_TYPE_DOG_WALKING = 5;
  // GROUP_CLASS类型
  SYSTEM_CARE_TYPE_GROUP_CLASS = 6;
  // 自定义类型
  SYSTEM_CARE_TYPE_CUSTOM = 99;
}

// 价格单位, 1-per session, 2-per night, 3-per hour, 4-per day
enum PriceUnit {
  // 未指定
  PRICE_UNIT_UNSPECIFIED = 0;
  // 每次服务
  PRICE_UNIT_PER_SESSION = 1;
  // 每晚
  PRICE_UNIT_PER_NIGHT = 2;
  // 每小时
  PRICE_UNIT_PER_HOUR = 3;
  // 每天
  PRICE_UNIT_PER_DAY = 4;
}

// 服务来源
enum ServiceSource {
  // 未指定
  SERVICE_SOURCE_UNSPECIFIED = 0;
  // MoeGo 平台
  SERVICE_SOURCE_PLATFORM = 1;
  // Enterprise Hub 企业
  SERVICE_SOURCE_ENTERPRISE = 2;
}

// ValueType defines the data type of an attribute's value, for frontend rendering.
enum ValueType {
  // Unspecified value type.
  VALUE_TYPE_UNSPECIFIED = 0;
  // A single-line string.
  VALUE_TYPE_STRING = 1;
  // A multi-line text.
  VALUE_TYPE_TEXT = 2;
  // A numerical value (integer or decimal).
  VALUE_TYPE_NUMBER = 3;
  // A boolean value (true or false).
  VALUE_TYPE_BOOLEAN = 4;
  // A single selection from a list of options.
  VALUE_TYPE_SINGLE_SELECT = 5;
  // Multiple selections from a list of options.
  VALUE_TYPE_MULTI_SELECT = 6;
  // A date value.
  VALUE_TYPE_DATE = 7;
  // A date and time value.
  VALUE_TYPE_DATETIME = 8;
  // A JSON object.
  VALUE_TYPE_JSON = 9;
}