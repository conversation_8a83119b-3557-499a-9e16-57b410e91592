package service

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	aistudiologic "github.com/MoeGolibrary/moego/backend/app/aistudio/logic/aistudio"
)

// MockTaskRunner is a mock implementation of the TaskRunner interface for testing.
type MockTaskRunner struct {
	RunFunc func(ctx context.Context, taskName string, taskID int64, userInputEnvs map[string]string) (*aistudiologic.TaskResponse, error)
}

// RunAiStudioTask implements the TaskRunner interface for MockTaskRunner.
func (m *MockTaskRunner) RunAiStudioTask(ctx context.Context, taskName string, taskID int64, userInputEnvs map[string]string) (*aistudiologic.TaskResponse, error) {
	if m.RunFunc != nil {
		return m.RunFunc(ctx, taskName, taskID, userInputEnvs)
	}
	return nil, fmt.Errorf("RunFunc not implemented")
}

func TestRunAiStudioTaskBySlack(t *testing.T) {
	tests := []struct {
		name          string
		inputText     string
		expectedReply string
		expectedErr   error
		mockRunFunc   func(ctx context.Context, taskName string, taskID int64, userInputEnvs map[string]string) (*aistudiologic.TaskResponse, error)
	}{
		{
			name:          "Run command with key-value pairs",
			inputText:     "@bot run my-task key1=val1 key2=val2",
			expectedReply: "Task my-task executed with key1=val1, key2=val2",
			expectedErr:   nil,
			mockRunFunc: func(ctx context.Context, taskName string, taskID int64, userInputEnvs map[string]string) (*aistudiologic.TaskResponse, error) {
				assert.Equal(t, "my-task", taskName)
				assert.Equal(t, map[string]string{"key1": "val1", "key2": "val2"}, userInputEnvs)
				return &aistudiologic.TaskResponse{Result: fmt.Sprintf("Task %s executed with key1=%s, key2=%s", taskName, userInputEnvs["key1"], userInputEnvs["key2"])}, nil
			},
		},
		{
			name:          "Run command with positional values",
			inputText:     "@bot my-task val1 val2",
			expectedReply: "Task my-task executed with 0=val1, 1=val2",
			expectedErr:   nil,
			mockRunFunc: func(ctx context.Context, taskName string, taskID int64, userInputEnvs map[string]string) (*aistudiologic.TaskResponse, error) {
				assert.Equal(t, "my-task", taskName)
				assert.Equal(t, map[string]string{"0": "val1", "1": "val2"}, userInputEnvs)
				return &aistudiologic.TaskResponse{Result: fmt.Sprintf("Task %s executed with 0=%s, 1=%s", taskName, userInputEnvs["0"], userInputEnvs["1"])}, nil
			},
		},
		{
			name:          "Help command",
			inputText:     "@bot help",
			expectedReply: "help:\n模式1: @PlatformsApp run $TaskName key1=val1 key2=val2\n模式2: @PlatformsApp $TaskName val1 val2, 其中val1在模版中对应{{0}}",
			expectedErr:   nil,
			mockRunFunc:   nil, // Should not be called
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRunner := &MockTaskRunner{
				RunFunc: tt.mockRunFunc,
			}

			reply, err := RunAiStudioTaskBySlack(mockRunner, tt.inputText)

			assert.Equal(t, tt.expectedReply, reply)
			assert.Equal(t, tt.expectedErr, err)
		})
	}
}
