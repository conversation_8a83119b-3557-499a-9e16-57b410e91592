package watcher

import (
	"fmt"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/datadog"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
)

func TestJiraIncidentEvaluator_EvaluateBug(t *testing.T) {
	// Define a fixed time in Beijing for testing "Dedicate" period (e.g., 10 AM)
	dedicateTime := time.Date(2025, time.July, 8, 10, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60))
	// Define a fixed time in Beijing for testing "OncallTeam" period (e.g., 3 PM)
	oncallTime := time.Date(2025, time.July, 8, 15, 0, 0, 0, time.FixedZone("Asia/Shanghai", 8*60*60))

	// Mock configuration
	mockConfig := &configloader.CSPageConfig{
		ComponentsSquadsMapping: map[string]string{
			"Workflow":      global.SquadCRM,
			"System access": "Infra",
			"Staff":         "HR",
		},
	}
	var mockCfg atomic.Pointer[configloader.CSPageConfig]
	mockCfg.Store(mockConfig)

	tests := []struct {
		name        string
		issue       *jira.Issue
		refUser     []string
		mockTime    time.Time // New field for mocking time
		expectedInc *datadog.Incident
		expectedErr error
	}{
		{
			name: "P0 T1 GoLive - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 GoLive - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"Workflow"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.SquadCRM,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 HyperCare with RefUsers - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				Components:    []string{"Workflow"},
				CustomerStage: global.CustomerStageHyperCare,
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  []string{"<EMAIL>", "<EMAIL>"},
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageHyperCare,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 HyperCare with RefUsers - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				Components:    []string{"Workflow"},
				CustomerStage: global.CustomerStageHyperCare,
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  []string{"<EMAIL>", "<EMAIL>"},
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.SquadCRM,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageHyperCare,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 PostLive with component - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{"Workflow"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  []string{"<EMAIL>"},
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStagePostLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 PostLive with component - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{"Workflow"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  []string{"<EMAIL>"},
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.SquadCRM,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStagePostLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 PostLive without component - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStagePostLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 PostLive without component - Oncall Time (Error Expected)",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP0,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:     nil,
			mockTime:    oncallTime,
			expectedInc: nil,
			expectedErr: fmt.Errorf("no team found for issue []"),
		},
		{
			name: "P1 T1 GoLive with component - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP1,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"System access"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP1,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   true,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P1 T1 GoLive with component - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP1,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"System access"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP1,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   true,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P1 T1 PostLive with component",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP1,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{"Staff"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP1,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStagePostLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   true,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P1 T1 PostLive without component",
			issue: &jira.Issue{
				IssuePriority: global.PriorityP1,
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP1,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStagePostLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   true,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P234 T1 GoLive - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: "P2",
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      "P2",
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P234 T1 GoLive - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: "P2",
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"System access"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      "P2",
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P234 T1 HyperCare - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: "P2",
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageHyperCare,
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      "P2",
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageHyperCare,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P234 T1 HyperCare - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: "P2",
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageHyperCare,
				Components:    []string{"System access"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      "P2",
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageHyperCare,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P234 T1 PostLive - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: "P2",
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      "P2",
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStagePostLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P234 T1 PostLive - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: "P2",
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStagePostLive,
				Components:    []string{"Staff"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      "P2",
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStagePostLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P234 General",
			issue: &jira.Issue{
				IssuePriority: "P3",
				T1OrGeneral:   global.TierOther,
				CustomerStage: global.CustomerStagePostLive,
			},
			refUser:     nil,
			mockTime:    dedicateTime,
			expectedInc: nil,
			expectedErr: nil,
		},
		{
			name: "Unknown Priority - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority: "Unknown",
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      "Unknown",
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "Unknown Priority - Oncall Time",
			issue: &jira.Issue{
				IssuePriority: "Unknown",
				T1OrGeneral:   global.TierT1,
				CustomerStage: global.CustomerStageGoLive,
				Components:    []string{"Staff"},
				Assignee:      jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:      jira.UserInfo{EmailAddress: "<EMAIL>"},
			},
			refUser:  nil,
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      "Unknown",
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: false,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 GoLive with CreatedByCustom - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority:   global.PriorityP0,
				T1OrGeneral:     global.TierT1,
				CustomerStage:   global.CustomerStageGoLive,
				Assignee:        jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:        jira.UserInfo{EmailAddress: "<EMAIL>"},
				CreatedByCustom: "Aathi Parthiban",
			},
			refUser:  []string{"<EMAIL>"},
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 GoLive with CreatedByCustom - Oncall Time",
			issue: &jira.Issue{
				IssuePriority:   global.PriorityP0,
				T1OrGeneral:     global.TierT1,
				CustomerStage:   global.CustomerStageGoLive,
				Components:      []string{"Workflow"},
				Assignee:        jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:        jira.UserInfo{EmailAddress: "<EMAIL>"},
				CreatedByCustom: "Aathi Parthiban",
			},
			refUser:  []string{"<EMAIL>"},
			mockTime: oncallTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.SquadCRM,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 GoLive with CreatedByCustom not in MoegoContacts - Dedicate Time",
			issue: &jira.Issue{
				IssuePriority:   global.PriorityP0,
				T1OrGeneral:     global.TierT1,
				CustomerStage:   global.CustomerStageGoLive,
				Assignee:        jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:        jira.UserInfo{EmailAddress: "<EMAIL>"},
				CreatedByCustom: "NonExistent User",
			},
			refUser:  []string{"<EMAIL>"},
			mockTime: dedicateTime,
			expectedInc: &datadog.Incident{
				OncallTeam:         global.TeamDedicate,
				IssuePriority:      global.PriorityP0,
				Tier:               global.TierT1,
				CustomerStage:      global.CustomerStageGoLive,
				NeedCreateIncident: true,
				NeedT1Slack:        true,
				NeedBuzzAssignee:   false,
				RefUsers:           []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
			},
			expectedErr: nil,
		},
		{
			name: "P0 T1 GoLive with CreatedByCustom not in MoegoContacts - Oncall Time (Error Expected)",
			issue: &jira.Issue{
				IssuePriority:   global.PriorityP0,
				T1OrGeneral:     global.TierT1,
				CustomerStage:   global.CustomerStageGoLive,
				Assignee:        jira.UserInfo{EmailAddress: "<EMAIL>"},
				Reporter:        jira.UserInfo{EmailAddress: "<EMAIL>"},
				CreatedByCustom: "NonExistent User",
			},
			refUser:     []string{"<EMAIL>"},
			mockTime:    oncallTime,
			expectedInc: nil,
			expectedErr: fmt.Errorf("no team found for issue []"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			evaluator := &JiraIncidentEvaluator{
				refUser:      tt.refUser,
				timeGetter:   func() time.Time { return tt.mockTime }, // Inject mock time
				csPageConfig: &mockCfg,
			}
			retIncident, err := evaluator.EvaluateBug(tt.issue)

			assert.Equal(t, tt.expectedErr, err)
			if tt.expectedInc == nil {
				assert.Nil(t, retIncident)
			} else {
				// Set the issue field for comparison, as it's passed by reference
				tt.expectedInc.Issue = tt.issue
				assert.Equal(t, tt.expectedInc, retIncident)
			}
		})
	}
}
