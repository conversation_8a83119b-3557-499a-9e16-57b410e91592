load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = [
        "jira_sla_reminder.go",
        "page_watcher.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/cs_page_watcher/configloader",
        "//backend/app/cs_page_watcher/global",
        "//backend/app/cs_page_watcher/logic/jiraslareminder",
        "//backend/app/cs_page_watcher/logic/watcher",
        "//backend/app/cs_page_watcher/pkg",
        "//backend/app/cs_page_watcher/repo/datadog",
        "//backend/app/cs_page_watcher/repo/entity",
        "//backend/app/cs_page_watcher/repo/jira",
        "//backend/app/cs_page_watcher/repo/slack",
        "//backend/common/rpc/framework/log",
        "//backend/proto/cs_page_watcher/v1:cs_page_watcher",
        "@com_github_cenkalti_backoff_v4//:backoff",
        "@com_github_samber_lo//:lo",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "service_test",
    srcs = [
        "jira_sla_reminder_test.go",
        "page_watcher_test.go",
    ],
    embed = [":service"],
    deps = [
        "//backend/app/cs_page_watcher/configloader",
        "//backend/app/cs_page_watcher/logic/jiraslareminder",
        "//backend/app/cs_page_watcher/logic/watcher",
        "//backend/app/cs_page_watcher/repo/jira",
        "//backend/app/cs_page_watcher/repo/slack",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/framework",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
    ],
)
