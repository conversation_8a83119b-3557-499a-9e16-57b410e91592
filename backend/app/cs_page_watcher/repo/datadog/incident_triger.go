package datadog

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/DataDog/datadog-api-client-go/v2/api/datadog"
	"github.com/DataDog/datadog-api-client-go/v2/api/datadogV2"
	backoff "github.com/cenkalti/backoff/v4"
	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// IncidentGateway 定义了向值班系统发送告警通知的契约。
type IncidentGateway interface {
	// TriggerIncident 发送告警通知到值班系统。
	TriggerIncident(ctx context.Context, incident *Incident) error
	GetCommander(team string) (commanderEmail string, err error)
}

func NewIncidentGateway(
	cfg *configloader.Config,
	slackClient slack.Client, csPageReaderWriter entity.CsPageReaderWriter) IncidentGateway {
	return &IncidentGatewayImpl{
		slackClient:        slackClient,
		cfg:                cfg,
		csPageReaderWriter: csPageReaderWriter,
	}
}

type IncidentGatewayImpl struct {
	slackClient        slack.Client
	cfg                *configloader.Config
	csPageReaderWriter entity.CsPageReaderWriter
}

type DutyResponder struct {
	commanderID    string
	commanderEmail string
	teamHandle     string
}

func (d *IncidentGatewayImpl) oncallTeam2Commander(
	apiKey, appKey, team string) (ret *DutyResponder, err error) {
	ret = new(DutyResponder)

	schedule, ok := global.TeamSchedules[team]
	if !ok {
		err = fmt.Errorf("oncallTeam2Commander: team %v not found", team)
		return
	}
	if schedule.ScheduleID == "" {
		err = fmt.Errorf("oncallTeam2Commander: scheduleID not found")
		return
	}
	ret.teamHandle = schedule.TeamHandle

	ctx := context.WithValue(
		context.Background(),
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: apiKey,
			},
			"appKeyAuth": {
				Key: appKey,
			},
		},
	)
	ctx = context.WithValue(ctx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "us5.datadoghq.com",
		})
	configuration := datadog.NewConfiguration()

	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewOnCallApi(apiClient)

	resp, _, err := api.GetScheduleOnCallUser(ctx, schedule.ScheduleID,
		datadogV2.GetScheduleOnCallUserOptionalParameters{
			Include: datadog.PtrString("user"),
		})
	if err != nil {
		err = fmt.Errorf("oncallTeam2Commander: GetScheduleOnCallUser error:%v", err)
		return
	}
	if len(resp.Included) > 0 {
		ret.commanderID = *resp.Included[0].ScheduleUser.Id
		ret.commanderEmail = *resp.Included[0].ScheduleUser.Attributes.Email
	}
	if ret.commanderID == "" {
		err = fmt.Errorf("oncallTeam2Commander: commanderID not found")
		return
	}

	if schedule.TeamHandle == "" {
		return
	}

	return
}

func (d *IncidentGatewayImpl) GetCommander(team string) (commanderEmail string, err error) {
	responder, err := d.oncallTeam2Commander(
		d.cfg.CsPageWatcher.DatadogAPIKey,
		d.cfg.CsPageWatcher.DatadogAPPKey,
		team)
	if err != nil {
		return "", fmt.Errorf("failed to get commander for team %s: %w", team, err)
	}
	return responder.commanderEmail, nil
}

func (d *IncidentGatewayImpl) pageATeam(ctx context.Context, apiKey, appKey, title, teamHandle string) error {
	log.InfoContextf(ctx, "pageATeam: will page a team: teamHandle:%v", teamHandle)
	// UserDataID := "c65695fb-b21b-11ef-91b2-dee2953e9407"
	ddCtx := context.WithValue(
		ctx,
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: apiKey,
			},
			"appKeyAuth": {
				Key: appKey,
			},
		},
	)
	ddCtx = context.WithValue(ddCtx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "coral.oncall.datadoghq.com",
		})

	handler := datadogV2.OnCallPageTargetType(datadogV2.ONCALLPAGETARGETTYPE_TEAM_HANDLE)
	body := datadogV2.CreatePageRequest{
		Data: &datadogV2.CreatePageRequestData{
			Attributes: &datadogV2.CreatePageRequestDataAttributes{
				Title:   title,
				Urgency: datadogV2.PageUrgency(datadogV2.URGENCY_HIGH),
				Target: datadogV2.CreatePageRequestDataAttributesTarget{
					Identifier: datadog.PtrString(teamHandle),
					Type:       &handler,
				},
			},
			Type: datadogV2.CREATEPAGEREQUESTDATATYPE_PAGES,
		},
	}

	configuration := datadog.NewConfiguration()
	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewOnCallPagingApi(apiClient)
	resp, _, err := api.CreateOnCallPage(ddCtx, body)
	if err != nil {
		err = fmt.Errorf("pageATeam: CreateOnCallPage error:%v", err)
		log.ErrorContext(ctx, err)
		return err
	}
	log.InfoContextf(ctx, "pageATeam: oncall data: %s", lo.Must(json.Marshal(resp.GetData())))
	return nil
}

func getInitSlackMsg(incident *Incident, devEmail string) string {
	return fmt.Sprintf(`Ticket: https://moego.atlassian.net/browse/%v
	Logo: %v
	Summary: %v
	Issue Description: %v
	Created By: %v
	Submitted by(Jira Reporter):%v	
	Priority: %v
	QA Owner(Jira Assignee): %v
	Eng Owner: %v`,
		incident.Issue.Key,
		incident.Issue.LogoName,
		incident.Issue.Summary,
		incident.Issue.IssueDescription,
		incident.Issue.CreatedByCustom,
		incident.Issue.Reporter.EmailAddress,
		incident.IssuePriority,
		incident.Issue.Assignee.EmailAddress,
		devEmail,
	)
}

// TriggerIncident implements IncidentGateway.
func (d *IncidentGatewayImpl) TriggerIncident(ctx context.Context, incident *Incident) (err error) {
	log.InfoContextf(ctx, "TriggerIncident: will create incident: %s", lo.Must(json.Marshal(incident)))
	var datadogIncidentID int64
	var incidentSlackChannelID string
	var t1NotifyMessageTimestamp string
	var responder = &DutyResponder{}

	ddAPIKey := d.cfg.CsPageWatcher.DatadogAPIKey
	ddAPPKey := d.cfg.CsPageWatcher.DatadogAPPKey

	if incident.NeedCreateIncident {
		datadogIncidentID, incidentSlackChannelID, responder, err =
			d.handleIncidentCreation(ctx, incident, ddAPIKey, ddAPPKey)
		if err != nil {
			return err
		}
	}

	devEmail := responder.commanderEmail
	if devEmail == "" {
		devEmail = "unknown"
		if len(incident.Issue.DevEngineer) > 0 {
			devEmail = incident.Issue.DevEngineer[0].EmailAddress
		}
	}

	if incident.NeedT1Slack {
		t1NotifyMessageTimestamp, err = d.handleT1SlackNotification(ctx, incident, devEmail)
		if err != nil {
			log.ErrorContextf(ctx, "TriggerIncident: sendInitMessage2T1Channel failed after retries: %v", err)
			t1NotifyMessageTimestamp = ""
		}
	}

	if incident.NeedBuzzAssignee {
		err = backoff.Retry(func() error {
			return d.slackNotifyAssignee(ctx, incident, devEmail)
		}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
		if err != nil {
			log.ErrorContextf(ctx, "TriggerIncident: slackNotifyAssignee failed after retries: %v", err)
		}
	}

	err = d.csPageReaderWriter.CreateCsPage(&entity.CsPage{
		CsPageJiraTicket:         incident.Issue.Key,
		DatadogIncidentID:        fmt.Sprint(datadogIncidentID),
		IncidentSlackChannelID:   incidentSlackChannelID,
		T1NotifyMessageTimestamp: t1NotifyMessageTimestamp,
		CreateTime:               time.Now(),
	})
	return
}

func (d *IncidentGatewayImpl) handleIncidentCreation(
	ctx context.Context, incident *Incident, ddAPIKey, ddAPPKey string) (
	datadogIncidentID int64, incidentSlackChannelID string, responder *DutyResponder, err error) {
	responder, err = d.oncallTeam2Commander(ddAPIKey, ddAPPKey, incident.OncallTeam)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerIncident: get command id failed: %v", err)
		return 0, "", nil, err
	}

	title := fmt.Sprintf("[%v] CS Page Incident: %v", incident.Issue.Key, incident.Issue.Summary)
	summary := fmt.Sprintf(`jira url: https://moego.atlassian.net/browse/%v
summary: %v`, incident.Issue.Key, incident.Issue.Summary)

	datadogIncidentID, err = createIncident(
		ddAPIKey, ddAPPKey, responder.commanderID, title, summary)
	if err != nil {
		log.ErrorContextf(ctx, "TriggerIncident: createIncident failed: %v", err)
		return 0, "", nil, err
	}

	time.Sleep(time.Second * 5)
	err = d.pageATeam(ctx, ddAPIKey, ddAPPKey, title, responder.teamHandle)

	err = backoff.Retry(func() error {
		incidentSlackChannelID, err = d.getIncidentInfo(ddAPIKey, ddAPPKey, fmt.Sprint(datadogIncidentID))
		if err != nil {
			log.ErrorContextf(ctx, "TriggerIncident: getIncidentInfo failed: %v", err)
		}
		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		log.ErrorContextf(ctx, "TriggerIncident: getIncidentInfo failed after retries: %v", err)
		incidentSlackChannelID = ""
	}

	// 尝试将refUser邀请进incidentChannel
	if incidentSlackChannelID != "" {
		_ = d.addMembers2IncidentChannel(ctx, incidentSlackChannelID, incident.RefUsers)
	}

	// 发送一条初始化消息到incident群里
	err = backoff.Retry(func() error {
		_, err = d.slackClient.SendMessage(
			incidentSlackChannelID, getInitSlackMsg(incident, responder.commanderEmail))
		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	if err != nil {
		log.ErrorContextf(ctx, "TriggerIncident: sendInitMessage2IncidentChannel failed after retries: %v", err)
	}
	return datadogIncidentID, incidentSlackChannelID, responder, nil
}

func (d *IncidentGatewayImpl) handleT1SlackNotification(
	ctx context.Context, incident *Incident, commanderEmail string) (t1NotifyMessageTimestamp string, err error) {
	err = backoff.Retry(func() error {
		t1NotifyMessageTimestamp, err = d.sendInitMessage2T1Channel(
			getInitSlackMsg(incident, commanderEmail))
		if err != nil {
			log.ErrorContextf(ctx, "handleT1SlackNotification: sendInitMessage2T1Channel failed: %v", err)
		}
		return err
	}, backoff.WithMaxRetries(backoff.NewExponentialBackOff(), 3))
	return t1NotifyMessageTimestamp, err
}

func (d *IncidentGatewayImpl) slackNotifyAssignee(
	ctx context.Context, incident *Incident, devEmail string) (err error) {
	msg := getInitSlackMsg(incident, devEmail)
	err = d.slackClient.SendMessageToPerson(incident.Issue.Assignee.EmailAddress, msg)
	if err != nil {
		log.ErrorContextf(ctx, "slackNotifyAssignee: NotifyAssignee failed: %v", err)
	}
	return
}

func (d *IncidentGatewayImpl) addMembers2IncidentChannel(
	ctx context.Context, incidentSlackChannelID string, refUsers []string) (err error) {
	err = d.slackClient.JoinChannel(incidentSlackChannelID)
	if err != nil {
		log.ErrorContextf(ctx, "addMembers2IncidentChannel: JoinChannel failed: %v", err)
		return
	}
	time.Sleep(time.Second)
	// 去除重复的refUsers
	refUsers = lo.Uniq(refUsers)
	err = d.slackClient.AddMembersToChannel(incidentSlackChannelID, refUsers)
	if err != nil {
		log.ErrorContextf(ctx, "addMembers2IncidentChannel: AddMembersToChannel failed: %v", err)
	}
	return
}

func (d *IncidentGatewayImpl) getIncidentInfo(
	apiKey, appKey, publicID string) (slackChannelID string, err error) {
	ctx := context.WithValue(
		context.Background(),
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: apiKey,
			},
			"appKeyAuth": {
				Key: appKey,
			},
		},
	)
	ctx = context.WithValue(ctx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "us5.datadoghq.com",
		})
	configuration := datadog.NewConfiguration()
	configuration.SetUnstableOperationEnabled("v2.ListIncidentIntegrations", true)

	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewIncidentsApi(apiClient)
	resp, _, err := api.ListIncidentIntegrations(ctx, publicID)
	if err != nil {
		return "", err
	}
	for _, v := range resp.Data {
		attr := v.Attributes
		if attr != nil && attr.IntegrationType == 1 {
			metadata := attr.Metadata.SlackIntegrationMetadata
			if metadata != nil {
				channels := metadata.Channels
				if len(channels) > 0 {
					return channels[0].ChannelId, nil
				}
			}
		}
	}
	return "", fmt.Errorf("cannot find slack channel id")
}

func (d *IncidentGatewayImpl) sendInitMessage2T1Channel(msg string) (ts string, err error) {
	ts, err = d.slackClient.SendMessage(d.cfg.CsPageWatcher.T1SlackChannelID, msg)
	if err != nil {
		return
	}
	return
}

func createIncident(apiKey, appKey,
	commanderUserID, title, summary string) (publicID int64, err error) {
	log.Infof("createIncident: will create Incident, commanderUserID:%v, title:%v", commanderUserID, title)
	// UserDataID := "c65695fb-b21b-11ef-91b2-dee2953e9407"
	body := datadogV2.IncidentCreateRequest{
		Data: datadogV2.IncidentCreateData{
			Type: datadogV2.INCIDENTTYPE_INCIDENTS,
			Attributes: datadogV2.IncidentCreateAttributes{
				Title:               title,
				CustomerImpacted:    true,
				CustomerImpactScope: datadog.PtrString(summary),
				Fields: map[string]datadogV2.IncidentFieldAttributes{
					"state": {
						IncidentFieldAttributesSingleValue: &datadogV2.IncidentFieldAttributesSingleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESSINGLEVALUETYPE_DROPDOWN.Ptr(),
							Value: *datadog.NewNullableString(datadog.PtrString("active")),
						}},
					"summary": {
						IncidentFieldAttributesSingleValue: &datadogV2.IncidentFieldAttributesSingleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESSINGLEVALUETYPE_TEXTBOX.Ptr(),
							Value: *datadog.NewNullableString(datadog.PtrString(summary)),
						}},
					"detection_method": {
						IncidentFieldAttributesSingleValue: &datadogV2.IncidentFieldAttributesSingleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESSINGLEVALUETYPE_DROPDOWN.Ptr(),
							Value: *datadog.NewNullableString(datadog.PtrString("customer")),
						}},
					"severity": {
						IncidentFieldAttributesSingleValue: &datadogV2.IncidentFieldAttributesSingleValue{
							Type:  datadogV2.INCIDENTFIELDATTRIBUTESSINGLEVALUETYPE_DROPDOWN.Ptr(),
							Value: *datadog.NewNullableString(datadog.PtrString("SEV-3")),
						}},
				},
			},
			Relationships: &datadogV2.IncidentCreateRelationships{
				CommanderUser: *datadogV2.NewNullableNullableRelationshipToUser(
					&datadogV2.NullableRelationshipToUser{
						Data: *datadogV2.NewNullableNullableRelationshipToUserData(
							&datadogV2.NullableRelationshipToUserData{
								Type: datadogV2.USERSTYPE_USERS,
								Id:   commanderUserID,
							}),
					}),
			},
		},
	}
	ctx := context.WithValue(
		context.Background(),
		datadog.ContextAPIKeys,
		map[string]datadog.APIKey{
			"apiKeyAuth": {
				Key: apiKey,
			},
			"appKeyAuth": {
				Key: appKey,
			},
		},
	)
	ctx = context.WithValue(ctx,
		datadog.ContextServerVariables,
		map[string]string{
			"site": "us5.datadoghq.com",
		})
	configuration := datadog.NewConfiguration()
	configuration.SetUnstableOperationEnabled("v2.CreateIncident", true)
	apiClient := datadog.NewAPIClient(configuration)
	api := datadogV2.NewIncidentsApi(apiClient)
	resp, _, err := api.CreateIncident(ctx, body)
	if err != nil {
		err = fmt.Errorf("createIncident, Error when calling `IncidentsApi.CreateIncident`: %s", err.Error())
		return
	}
	publicID = *resp.Data.Attributes.PublicId
	return
}

func NewDatadogIncidentGatewayImpl() IncidentGateway {
	return &IncidentGatewayImpl{}
}
