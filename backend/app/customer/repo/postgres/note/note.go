package note

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	Create(ctx context.Context, note *Note) error
	Update(ctx context.Context, note *Note) error
	List(ctx context.Context, datum *ListNotesDatum) ([]*Note, error)
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, note *Note) error {
	if err := i.db.WithContext(ctx).Create(note).Error; err != nil {
		log.ErrorContextf(ctx, "Create Note err, err:%+v", err)
		return err
	}
	return nil
}

func (i *impl) Update(ctx context.Context, note *Note) error {
	if err := i.db.WithContext(ctx).Updates(note).Error; err != nil {
		log.ErrorContextf(ctx, "Update Note err, err:%+v", err)
		return err
	}
	return nil
}

type ListNotesDatum struct {
	CustomerID *int64
	IDs        []int64
}

func (i *impl) List(ctx context.Context, datum *ListNotesDatum) ([]*Note, error) {
	query := i.db.WithContext(ctx).Table("note").Where("delete_time IS NULL")

	if datum.CustomerID != nil {
		query = query.Where("customer_id = ?", *datum.CustomerID)
	}
	if len(datum.IDs) > 0 {
		query = query.Where("id IN ?", datum.IDs)
	}

	var res []*Note
	if err := query.
		Order("update_time desc").
		Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List Note err, err:%+v", err)
		return nil, err
	}

	return res, nil
}

func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("note").
		Where("id = ?", id).
		Update("delete_by", staffID).
		Update("delete_time", time.Now()).
		Error; err != nil {
		log.ErrorContextf(ctx, "Delete Note err, id:%d, staffID:%d", id, staffID)
		return err
	}
	return nil
}
