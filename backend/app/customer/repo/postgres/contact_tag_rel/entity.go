package contacttagrel

import (
	"time"

	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type ContactTagRel struct {
	ID               int64                           `gorm:"column:id;primaryKey;autoIncrement"`
	OrganizationType customerpb.OrganizationRef_Type `gorm:"column:organization_type;serializer:proto_enum"`
	OrganizationID   int64                           `gorm:"column:organization_id"`
	ContactID        int64                           `gorm:"column:contact_id"`
	TagID            int64                           `gorm:"column:tag_id"`
	CreatedTime      time.Time                       `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime      time.Time                       `gorm:"column:updated_time;autoUpdateTime"`
	DeletedTime      *time.Time                      `gorm:"column:deleted_time"`
}

// TableName 设置表名
func (ContactTagRel) TableName() string {
	return "contact_tag_rel"
}

type ListFilter struct {
	ContactID int64
	TagID     int64
}
