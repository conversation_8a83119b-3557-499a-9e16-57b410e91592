package source

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// ReadWriter defines CRUD operations for Source.
type ReadWriter interface {
	WithTransaction(context.Context, func(api ReadWriter) error) error
	Create(ctx context.Context, src *Source) error
	Update(ctx context.Context, src *Source) error
	List(ctx context.Context, datum *ListSourcesDatum) ([]*Source, error)
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

func New() ReadWriter {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) WithTransaction(ctx context.Context, fn func(api ReadWriter) error) error {
	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txReadWriter := &impl{db: tx}
		return fn(txReadWriter)
	})
}

// Create inserts a new Source record.
func (i *impl) Create(ctx context.Context, src *Source) error {
	if err := i.db.WithContext(ctx).Create(src).Error; err != nil {
		log.ErrorContextf(ctx, "Create Source err, err:%+v", err)
		return err
	}
	return nil
}

// Update updates an existing Source record.
func (i *impl) Update(ctx context.Context, src *Source) error {
	if err := i.db.WithContext(ctx).Updates(src).Error; err != nil {
		log.ErrorContextf(ctx, "Update Source err, err:%+v", err)
		return err
	}
	return nil
}

// ListSourcesDatum is the filter for listing sources.
type ListSourcesDatum struct {
	CompanyID *int64
	IDs       []int64
	Names     []string
}

// List returns a list of Source records matching the filter.
func (i *impl) List(ctx context.Context, datum *ListSourcesDatum) ([]*Source, error) {
	query := i.db.WithContext(ctx).Table("source").Where("delete_time IS NULL")

	if datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}
	if len(datum.IDs) > 0 {
		query = query.Where("id IN (?)", datum.IDs)
	}
	if len(datum.Names) > 0 {
		query = query.Where("name IN ?", datum.Names)
	}

	var res []*Source
	if err := query.Order("update_time desc").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List Source err, err:%+v", err)
		return nil, err
	}
	return res, nil
}

// Delete performs a soft delete on a Source record.
func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("source").
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"delete_by":   staffID,
			"delete_time": time.Now(),
		}).Error; err != nil {
		log.ErrorContextf(ctx, "Delete Source err, id:%d, staffID:%d", id, staffID)
		return err
	}
	return nil
}
