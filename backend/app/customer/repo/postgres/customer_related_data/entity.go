package customerrelateddata

import (
	"time"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// CustomerRelatedData 客户相关数据表
type CustomerRelatedData struct {
	ID                     int64                                `gorm:"primaryKey;column:id"`
	CustomerID             int64                                `gorm:"column:customer_id"`
	BusinessID             int32                                `gorm:"column:business_id"`
	CompanyID              int64                                `gorm:"column:company_id"`
	ClientColor            string                               `gorm:"column:client_color"`
	IsBlockMessage         int32                                `gorm:"column:is_block_message"`
	IsBlockOnlineBooking   int32                                `gorm:"column:is_block_online_booking"`
	LoginEmail             string                               `gorm:"column:login_email"`
	ReferralSourceID       int32                                `gorm:"column:referral_source_id"`
	ReferralSourceDesc     string                               `gorm:"column:referral_source_desc"`
	SendAutoEmail          int32                                `gorm:"column:send_auto_email"`
	SendAutoMessage        int32                                `gorm:"column:send_auto_message"`
	SendAppAutoMessage     int32                                `gorm:"column:send_app_auto_message"`
	UnconfirmedReminderBy  int32                                `gorm:"column:unconfirmed_reminder_by"`
	PreferredGroomerID     int32                                `gorm:"column:preferred_groomer_id"`
	PreferredFrequencyDay  int32                                `gorm:"column:preferred_frequency_day"`
	PreferredFrequencyType int32                                `gorm:"column:preferred_frequency_type"`
	LastServiceTime        string                               `gorm:"column:last_service_time"`
	Source                 string                               `gorm:"column:source"`
	ExternalID             string                               `gorm:"column:external_id"`
	CreateBy               int32                                `gorm:"column:create_by"`
	UpdateBy               int32                                `gorm:"column:update_by"`
	IsRecurring            *int32                               `gorm:"column:is_recurring"`
	ShareApptStatus        int32                                `gorm:"column:share_appt_status"`
	ShareRangeType         int32                                `gorm:"column:share_range_type"`
	ShareRangeValue        int32                                `gorm:"column:share_range_value"`
	ShareApptJSON          *string                              `gorm:"column:share_appt_json"`
	PreferredDay           string                               `gorm:"column:preferred_day"`
	PreferredTime          string                               `gorm:"column:preferred_time"`
	AccountID              int64                                `gorm:"column:account_id"`
	CustomerCode           string                               `gorm:"column:customer_code"`
	IsUnsubscribed         bool                                 `gorm:"column:is_unsubscribed"`
	Birthday               *time.Time                           `gorm:"column:birthday"`
	ActionState            string                               `gorm:"column:action_state"`
	CustomizeLifeCycleID   int64                                `gorm:"column:customize_life_cycle_id"`
	CustomizeActionStateID int64                                `gorm:"column:customize_action_state_id"`
	State                  customerpb.CustomerRelatedData_State `gorm:"column:state;serializer:proto_enum"`
	DeletedTime            *time.Time                           `gorm:"column:deleted_time"`
	CreatedTime            time.Time                            `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime            time.Time                            `gorm:"column:updated_time;autoUpdateTime"`
}

// TableName 设置表名
func (CustomerRelatedData) TableName() string {
	return "customer_related_data"
}

// ListFilter 列表查询过滤条件
type ListFilter struct {
	IDs         []int64
	CustomerIDs []int64
	BusinessIDs []int32
	CompanyIDs  []int64
	States      []customerpb.CustomerRelatedData_State
}

// Pagination 分页信息
type Pagination struct {
	PageSize        int32
	Cursor          *postgres.Cursor
	ReturnTotalSize bool
}

// OrderBy 排序信息
type OrderBy struct {
	Field     customerpb.ListCustomerRelatedDataRequest_Sorting_Field
	Direction customerpb.ListCustomerRelatedDataRequest_Sorting_Direction
}

// CursorResult 游标分页结果
type CursorResult struct {
	Data       []*CustomerRelatedData
	HasNext    bool
	TotalCount *int64
}
