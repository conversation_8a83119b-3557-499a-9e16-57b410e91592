package activityrel

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	activityrelRepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/activity_rel"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Logic struct {
	activityRelRepo activityrelRepo.ReadWriter
}

func New() *Logic {
	return &Logic{
		activityRelRepo: activityrelRepo.New(),
	}
}

// CoverCustomerTags 覆盖客户的标签
func (l *Logic) CoverCustomerTags(ctx context.Context, datum *CoverCustomerTagsDatum) error {
	// check
	if datum == nil || datum.CustomerID <= 0 {
		log.ErrorContextf(ctx, "CoverCustomerTags params invalid, datum:%+v", datum)
		return status.Error(codes.InvalidArgument, "params is invalid")
	}

	// save to db
	if err := l.activityRelRepo.WithTransaction(ctx, func(api activityrelRepo.ReadWriter) error {
		// delete customer all tag
		if err := api.DeleteCustomerActivityRel(ctx, &activityrelRepo.DeleteCustomerActivityRel{
			CustomerID:   datum.CustomerID,
			ActivityType: activityrelRepo.TagActivityRelType,
			StaffID:      datum.StaffID,
		}); err != nil {
			return err
		}
		// add customer tag
		rels := make([]*activityrelRepo.ActivityRel, 0, len(datum.TagIDs))
		for _, tagID := range datum.TagIDs {
			rels = append(rels, &activityrelRepo.ActivityRel{
				CompanyID:    datum.CompanyID,
				BusinessID:   datum.BusinessID,
				CustomerID:   datum.CustomerID,
				ActivityID:   tagID,
				ActivityType: activityrelRepo.TagActivityRelType,
				CreateBy:     datum.StaffID,
				UpdateBy:     datum.StaffID,
				CreateTime:   time.Now(),
				UpdateTime:   time.Now(),
			})
		}
		if len(rels) == 0 {
			return nil
		}
		if err := api.CreateBatch(ctx, rels); err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "CoverCustomerTags WithTransaction err, err:%+v", err)
		return err
	}
	return nil
}

func (l *Logic) BatchAddCustomersTags(ctx context.Context, datum *BatchAddCustomersTags) error {
	// check
	if datum == nil || len(datum.CustomerIDs) == 0 || len(datum.TagIDs) == 0 {
		log.ErrorContextf(ctx, "BatchAddCustomersTags params invalid, datum:%+v", datum)
		return status.Error(codes.InvalidArgument, "params is invalid")
	}

	// conv rel
	rels := make([]*activityrelRepo.ActivityRel, 0, len(datum.CustomerIDs)*len(datum.TagIDs))
	for _, customerID := range datum.CustomerIDs {
		for _, tagID := range datum.TagIDs {
			rels = append(rels, &activityrelRepo.ActivityRel{
				CompanyID:    datum.CompanyID,
				BusinessID:   datum.BusinessID,
				CustomerID:   customerID,
				ActivityID:   tagID,
				ActivityType: activityrelRepo.TagActivityRelType,
				CreateBy:     datum.StaffID,
				UpdateBy:     datum.StaffID,
				CreateTime:   time.Now(),
				UpdateTime:   time.Now(),
			})
		}
	}

	// save to db
	if err := l.activityRelRepo.UpsertBatch(ctx, rels); err != nil {
		log.ErrorContextf(ctx, "BatchAddCustomersTags UpsertBatch err, err:%+v", err)
		return err
	}
	return nil
}
