load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "task",
    srcs = [
        "entity.go",
        "task.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/task",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/db/customertask",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/proto/customer/v1:customer",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "task_test",
    srcs = ["task_test.go"],
    embed = [":task"],
    deps = [
        "//backend/app/customer/repo/db",
        "//backend/app/customer/repo/db/customertask",
        "//backend/app/customer/repo/db/customertask/mock",
        "//backend/app/customer/utils",
        "//backend/proto/customer/v1:customer",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
