package customer_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/customer"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer"
	customermock "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer/mock"
	dbmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/mock"
	petmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/pet/mock"
	searchmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/search/mock"
	"github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

func TestNew(t *testing.T) {
	t.Run("测试创建新的Logic实例", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)
		require.NotNil(t, logic)
	})

	t.Run("New方法", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		db.SetDB(&gorm.DB{})
		logic := customer.New()
		require.NotNil(t, logic)
	})
}

func TestCreate(t *testing.T) {
	t.Run("测试创建客户成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			PhoneNumber: "*********",
			LifeCycle:   2,
			ActionState: 0,
			Source:      "walk_in",
			AvatarPath:  "",
			GivenName:   "*248",
			FamilyName:  "lead",
			Email:       utils.ToPointer(""),
			Type:        2,
			Address: &customer.Address{
				Address1:  "SF",
				Address2:  "s",
				City:      "SF",
				State:     "CA",
				Country:   "US",
				Zipcode:   "923919",
				Lat:       "37.7749295",
				Lng:       "-122.4194155",
				Status:    customerpb.Address_NORMAL,
				IsPrimary: pointer.Get(customerpb.Address_PRIMARY),
			},
			CompanyID: 103227,
			AccountID: 317291,
		}

		// 设置mock期望
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, filter *customerrepo.ContactFilter, params *customerrepo.GetContactParams) ([]*customerrepo.Contact, error) {
				require.Equal(t, customerpb.CustomerContact_MAIN, filter.Type)
				require.Equal(t, customerpb.CustomerContact_NORMAL, filter.Status)
				require.Equal(t, int64(103227), params.CompanyID)
				require.Equal(t, "*********", params.PhoneNumber)
				return []*customerrepo.Contact{}, nil
			},
		)
		customerRepo.EXPECT().CreateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, customer *customerrepo.BusinessCustomer) (*customerrepo.BusinessCustomer, error) {
				require.Equal(t, "*248", customer.FirstName)
				require.Equal(t, "lead", customer.LastName)
				require.Equal(t, int64(103227), customer.CompanyID)
				require.Equal(t, int64(317291), customer.AccountID)
				customer.ID = 1
				return customer, nil
			},
		)
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, params *customerrepo.GetCustomerParams) (*customerrepo.BusinessCustomer, error) {
				require.Equal(t, int64(1), params.ID)
				return &customerrepo.BusinessCustomer{
					ID:        1,
					FirstName: "*248",
					LastName:  "lead",
				}, nil
			},
		).AnyTimes()

		// 执行测试
		customer, err := logic.Create(context.Background(), req)
		require.NoError(t, err)
		require.Equal(t, int64(1), customer.ID)
	})

	t.Run("测试创建客户失败-手机号为空", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			GivenName:  "测试",
			FamilyName: "用户",
		}

		// 执行测试
		_, err := logic.Create(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("测试创建客户-已存在相同手机号的客户", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			PhoneNumber: "***********",
			GivenName:   "测试",
			FamilyName:  "用户",
		}

		// 设置mock期望
		existingContacts := []*customerrepo.Contact{
			{
				ID:          1,
				CustomerID:  100,
				PhoneNumber: "***********",
			},
		}
		existingCustomer := &customerrepo.BusinessCustomer{
			ID:        100,
			FirstName: "已存在",
			LastName:  "用户",
		}

		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).Return(existingContacts, nil)
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(existingCustomer, nil)

		// 执行测试
		customer, err := logic.Create(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, int64(100), customer.ID)
	})

	t.Run("测试创建客户-包含联系人信息", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)
		search := searchmock.NewMockReadWriter(ctrl)
		pet := petmock.NewMockReadWriter(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			PhoneNumber: "***********",
			GivenName:   "测试",
			FamilyName:  "用户",
			Contact: &customer.Contact{
				GivenName:   "联系人",
				FamilyName:  "测试",
				PhoneNumber: "***********",
				Email:       "<EMAIL>",
				Type:        customerpb.CustomerContact_MAIN,
				IsPrimary:   customerpb.CustomerContact_PRIMARY,
			},
			CompanyID: 1,
		}

		// 设置mock期望
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, filter *customerrepo.ContactFilter, params *customerrepo.GetContactParams) ([]*customerrepo.Contact, error) {
				require.Equal(t, customerpb.CustomerContact_MAIN, filter.Type)
				require.Equal(t, customerpb.CustomerContact_NORMAL, filter.Status)
				require.Equal(t, int64(1), params.CompanyID)
				require.Equal(t, "***********", params.PhoneNumber)
				return []*customerrepo.Contact{}, nil
			},
		)
		customerRepo.EXPECT().CreateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, customer *customerrepo.BusinessCustomer) (*customerrepo.BusinessCustomer, error) {
				require.Equal(t, "测试", customer.FirstName)
				require.Equal(t, "用户", customer.LastName)
				require.Equal(t, int64(1), customer.CompanyID)
				customer.ID = 1
				return customer, nil
			},
		)
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, params *customerrepo.GetCustomerParams) (*customerrepo.BusinessCustomer, error) {
				require.Equal(t, int64(1), params.ID)
				return &customerrepo.BusinessCustomer{
					ID:        1,
					FirstName: "测试",
					LastName:  "用户",
				}, nil
			},
		).AnyTimes()
		search.EXPECT().BulkDocument(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		pet.EXPECT().ListCustomerPet(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

		// 执行测试
		customer, err := logic.Create(context.Background(), req)
		require.NoError(t, err)
		require.Equal(t, int64(1), customer.ID)
	})

	t.Run("测试创建客户-GetContacts失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			PhoneNumber: "***********",
			GivenName:   "测试",
			FamilyName:  "用户",
		}

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "数据库错误")
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		_, err := logic.Create(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})

	t.Run("测试创建客户-CreateCustomer失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			PhoneNumber: "***********",
			GivenName:   "测试",
			FamilyName:  "用户",
			CompanyID:   1,
		}

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "创建客户失败")
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, filter *customerrepo.ContactFilter, params *customerrepo.GetContactParams) ([]*customerrepo.Contact, error) {
				require.Equal(t, customerpb.CustomerContact_MAIN, filter.Type)
				require.Equal(t, customerpb.CustomerContact_NORMAL, filter.Status)
				require.Equal(t, int64(1), params.CompanyID)
				require.Equal(t, "***********", params.PhoneNumber)
				return []*customerrepo.Contact{}, nil
			},
		)
		customerRepo.EXPECT().CreateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, customer *customerrepo.BusinessCustomer) (*customerrepo.BusinessCustomer, error) {
				require.Equal(t, "测试", customer.FirstName)
				require.Equal(t, "用户", customer.LastName)
				require.Equal(t, int64(1), customer.CompanyID)
				return nil, expectedErr
			},
		)

		// 执行测试
		_, err := logic.Create(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})
}

func TestGet(t *testing.T) {
	t.Run("测试获取客户成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.GetCustomerParams{
			ID: 1,
		}

		// 设置mock期望
		expectedCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, params *customerrepo.GetCustomerParams) (*customerrepo.BusinessCustomer, error) {
				require.Equal(t, int64(1), params.ID)
				return expectedCustomer, nil
			},
		)

		// 执行测试
		result, err := logic.Get(context.Background(), params)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
	})

	t.Run("测试获取客户失败-客户不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.GetCustomerParams{
			ID: 999,
		}

		// 设置mock期望
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		customer, err := logic.Get(context.Background(), params)
		require.Nil(t, customer)
		require.Nil(t, err)
	})

	t.Run("测试获取客户-包含联系人和地址", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.GetCustomerParams{
			ID: 1,
		}

		// 设置mock期望
		expectedCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Contacts: []*customerrepo.Contact{
				{
					ID:          1,
					CustomerID:  1,
					FirstName:   "联系人",
					LastName:    "测试",
					PhoneNumber: "***********",
					Email:       "<EMAIL>",
					IsPrimary:   1, // PRIMARY
				},
			},
			Addresses: []*customerrepo.Address{
				{
					ID:         1,
					CustomerID: 1,
					Address1:   "测试地址1",
					City:       "测试城市",
					State:      "测试省份",
					Country:    "测试国家",
					IsPrimary:  pointer.Get(int8(1)), // PRIMARY
				},
			},
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(expectedCustomer, nil)

		// 执行测试
		result, err := logic.Get(context.Background(), params)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, int64(1), result.ID)
		require.NotNil(t, result.Contact)
		require.Equal(t, "联系人", result.Contact.GivenName)
		require.NotNil(t, result.Address)
		require.Equal(t, "测试地址1", result.Address.Address1)
	})

	t.Run("测试获取客户-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.GetCustomerParams{
			ID: 1,
		}

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "数据库错误")
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.Get(context.Background(), params)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestList(t *testing.T) {
	t.Run("测试获取客户列表成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.ListCustomersParams{
			PageSize: 10,
			PageNum:  1,
		}

		// 设置mock期望
		expectedCustomers := []*customerrepo.BusinessCustomer{
			{
				ID:        1,
				FirstName: "测试1",
				LastName:  "用户1",
			},
			{
				ID:        2,
				FirstName: "测试2",
				LastName:  "用户2",
			},
		}
		customerRepo.EXPECT().CountCustomers(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(2), nil)
		customerRepo.EXPECT().ListCustomers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedCustomers, nil)

		// 执行测试
		result, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Len(t, result.Items, 2)
		require.Equal(t, int64(1), result.Items[0].ID)
		require.Equal(t, int64(2), result.Items[1].ID)
	})
	t.Run("测试获取客户列表-空结果", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.ListCustomersParams{
			CompanyID: 1,
			PageSize:  10,
			PageNum:   1,
		}

		// 设置mock期望
		customerRepo.EXPECT().CountCustomers(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, query *customerrepo.Query, filter *customerrepo.Filter) (int64, error) {
				require.Equal(t, int64(1), query.CompanyID)
				return int64(0), nil
			},
		)
		customerRepo.EXPECT().ListCustomers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, query *customerrepo.Query, filter *customerrepo.Filter, pagination *db.Pagination, opts *customerrepo.LoadOptions) ([]*customerrepo.BusinessCustomer, error) {
				require.Equal(t, int64(1), query.CompanyID)
				require.Equal(t, 10, pagination.Limit)
				require.Equal(t, 0, pagination.Offset)
				return []*customerrepo.BusinessCustomer{}, nil
			},
		)

		// 执行测试
		result, err := logic.List(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, result.Items)
	})
	t.Run("测试获取客户列表-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.ListCustomersParams{
			CompanyID: 1,
			PageSize:  10,
			PageNum:   1,
		}

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "数据库错误")
		customerRepo.EXPECT().CountCustomers(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, query *customerrepo.Query, filter *customerrepo.Filter) (int64, error) {
				require.Equal(t, int64(1), query.CompanyID)
				return int64(0), expectedErr
			},
		)

		// 执行测试
		result, err := logic.List(context.Background(), params)
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestUpdate(t *testing.T) {
	t.Run("测试更新客户成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID:          1,
			GivenName:   "更新后的名字",
			FamilyName:  "更新后的姓氏",
			PhoneNumber: "+8613912345678",
		}

		// 设置mock期望
		expectedCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
		}
		customerRepo.EXPECT().GetContacts(gomock.Any(), &customerrepo.ContactFilter{
			Type:   customerpb.CustomerContact_MAIN,
			Status: customerpb.CustomerContact_NORMAL,
		}, &customerrepo.GetContactParams{
			PhoneNumber: req.PhoneNumber,
			CompanyID:   req.CompanyID,
		}).Return([]*customerrepo.Contact{}, nil)
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(expectedCustomer, nil).AnyTimes()
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).Return(nil)

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.NoError(t, err)
	})

	t.Run("测试更新客户-ID为0", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID:         0, // 无效ID
			GivenName:  "更新后的名字",
			FamilyName: "更新后的姓氏",
		}

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("测试更新客户-客户不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID:         999,
			GivenName:  "更新后的名字",
			FamilyName: "更新后的姓氏",
		}

		// 设置mock期望
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(nil, nil)

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))
	})

	t.Run("测试更新客户-包含联系人和地址", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID:          1,
			GivenName:   "更新后的名字",
			FamilyName:  "更新后的姓氏",
			PhoneNumber: "+8613912345678",
			Contact: &customer.Contact{
				ID:          1,
				GivenName:   "更新后的联系人",
				FamilyName:  "姓氏",
				PhoneNumber: "+8613912345678",
				Email:       "<EMAIL>",
				IsPrimary:   customerpb.CustomerContact_PRIMARY,
			},
			Address: &customer.Address{
				ID:        1,
				Address1:  "更新后的地址1",
				City:      "更新后的城市",
				State:     "更新后的省份",
				Country:   "更新后的国家",
				Zipcode:   "更新后的邮编",
				Lat:       "更新后的纬度",
				Lng:       "更新后的经度",
				IsPrimary: pointer.Get(customerpb.Address_PRIMARY),
			},
		}

		expectedCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Contacts: []*customerrepo.Contact{
				{
					ID:          1,
					CustomerID:  1,
					Type:        int8(customerpb.CustomerContact_MAIN),
					PhoneNumber: "***********",
					IsPrimary:   int8(customerpb.CustomerContact_PRIMARY),
				},
			},
			Addresses: []*customerrepo.Address{
				{
					ID:         1,
					CustomerID: 999,
					Address1:   "原地址1",
					IsPrimary:  pointer.Get(int8(1)),
				},
			},
		}
		// get contact
		customerRepo.EXPECT().GetContacts(gomock.Any(), &customerrepo.ContactFilter{
			Type:   customerpb.CustomerContact_MAIN,
			Status: customerpb.CustomerContact_NORMAL,
		}, &customerrepo.GetContactParams{
			PhoneNumber: req.PhoneNumber,
			CompanyID:   req.CompanyID,
		}).Return([]*customerrepo.Contact{
			{
				ID:          1,
				CustomerID:  1,
				Type:        int8(customerpb.CustomerContact_MAIN),
				PhoneNumber: "***********",
				IsPrimary:   int8(customerpb.CustomerContact_PRIMARY),
			},
		}, nil)
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(expectedCustomer, nil).AnyTimes()
		// get address
		customerRepo.EXPECT().GetAddress(gomock.Any(), gomock.Eq(int64(1))).Return(expectedCustomer.Addresses[0], nil)
		// 添加GetAddresses的期望
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(int64(999)), gomock.Any()).Return([]*customerrepo.Address{}, nil)
		// 添加GetContacts的期望
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*customerrepo.Contact{}, nil)
		// batch update address
		customerRepo.EXPECT().BatchUpdateAddress(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, addresses []*customerrepo.Address) error {
			require.Equal(t, int(1), addresses[0].ID)
			require.Equal(t, "更新后的地址1", addresses[0].Address1)
			require.Equal(t, "更新后的城市", addresses[0].City)
			require.Equal(t, "更新后的省份", addresses[0].State)
			require.Equal(t, "更新后的国家", addresses[0].Country)
			require.Equal(t, "更新后的邮编", addresses[0].Zipcode)
			require.Equal(t, "更新后的纬度", addresses[0].Lat)
			require.Equal(t, "更新后的经度", addresses[0].Lng)
			require.Equal(t, int8(1), *addresses[0].IsPrimary)
			return nil
		})
		// 确保UpdateCustomer被正确调用
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).Return(nil)

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.NoError(t, err)
	})

	t.Run("测试更新客户-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID:         1,
			GivenName:  "更新后的名字",
			FamilyName: "更新后的姓氏",
		}

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "数据库错误")
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})

	t.Run("测试更新客户-更新主要联系人电话号码", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID:          1,
			PhoneNumber: "+8613912345678",
		}

		// 设置mock期望
		existingCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Contacts: []*customerrepo.Contact{
				{
					ID:          1,
					CustomerID:  1,
					Type:        int8(customerpb.CustomerContact_MAIN),
					PhoneNumber: "***********",
					IsPrimary:   1,
				},
			},
		}
		customerRepo.EXPECT().GetContacts(gomock.Any(), &customerrepo.ContactFilter{
			Type:   customerpb.CustomerContact_MAIN,
			Status: customerpb.CustomerContact_NORMAL,
		}, &customerrepo.GetContactParams{
			PhoneNumber: req.PhoneNumber,
			CompanyID:   req.CompanyID,
		}).Return([]*customerrepo.Contact{}, nil)
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(existingCustomer, nil)
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, customer *customerrepo.BusinessCustomer) error {
			// 验证电话号码已更新
			require.Equal(t, "+8613912345678", customer.Contacts[0].PhoneNumber)
			return nil
		})

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.NoError(t, err)
	})

	t.Run("测试更新客户-更新主要联系人为非主要，报错", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID:          1,
			PhoneNumber: "+8613912345678",
			Contact: &customer.Contact{
				ID:        1,
				GivenName: "联系人1",
				IsPrimary: customerpb.CustomerContact_NO_PRIMARY,
			},
		}

		// 设置mock期望
		existingCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Contacts: []*customerrepo.Contact{
				{
					ID:          1,
					CustomerID:  1,
					Type:        int8(customerpb.CustomerContact_MAIN),
					PhoneNumber: "***********",
					IsPrimary:   1,
				},
			},
		}

		// 第一次 GetContacts 调用 - 检查手机号是否已存在
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, filter *customerrepo.ContactFilter, params *customerrepo.GetContactParams) ([]*customerrepo.Contact, error) {
				require.Equal(t, customerpb.CustomerContact_MAIN, filter.Type)
				require.Equal(t, customerpb.CustomerContact_NORMAL, filter.Status)
				require.Equal(t, req.PhoneNumber, params.PhoneNumber)
				require.Equal(t, req.CompanyID, params.CompanyID)
				return []*customerrepo.Contact{}, nil
			})

		// GetCustomer 调用
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, params *customerrepo.GetCustomerParams) (*customerrepo.BusinessCustomer, error) {
				require.Equal(t, req.ID, params.ID)
				return existingCustomer, nil
			})

		// 第二次 GetContacts 调用 - 获取客户的所有联系人
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, filter *customerrepo.ContactFilter, params *customerrepo.GetContactParams) ([]*customerrepo.Contact, error) {
				require.Equal(t, customerpb.CustomerContact_NORMAL, filter.Status)
				require.Equal(t, existingCustomer.ID, params.CustomerID)
				return existingCustomer.Contacts, nil
			})

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("测试更新客户-设置非主要联系人", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID: 1,
			Contact: &customer.Contact{
				ID:        2,
				GivenName: "联系人2",
				IsPrimary: customerpb.CustomerContact_NO_PRIMARY,
			},
		}

		// 设置mock期望
		existingCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Contacts: []*customerrepo.Contact{
				{
					ID:         1,
					CustomerID: 1,
					FirstName:  "联系人1",
					IsPrimary:  1,
				},
			},
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(existingCustomer, nil)
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).Return(existingCustomer.Contacts, nil)
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, customer *customerrepo.BusinessCustomer) error {
			// 验证新联系人已添加且为非主要联系人
			require.Len(t, customer.Contacts, 1)
			// 验证新联系人
			require.Equal(t, "联系人2", customer.Contacts[0].FirstName)
			require.Equal(t, int8(0), customer.Contacts[0].IsPrimary)
			return nil
		})

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.NoError(t, err)
	})

	t.Run("测试更新客户-设置主要联系人", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID: 1,
			Contact: &customer.Contact{
				ID:          1,
				GivenName:   "联系人1",
				PhoneNumber: "+8613912345678",
				IsPrimary:   customerpb.CustomerContact_PRIMARY,
			},
		}

		// 设置mock期望
		existingCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Contacts: []*customerrepo.Contact{
				{
					ID:          1,
					CustomerID:  1,
					FirstName:   "联系人1",
					PhoneNumber: "+8613912345678111",
					IsPrimary:   1,
				},
			},
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(existingCustomer, nil)
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).Return(existingCustomer.Contacts, nil)
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, customer *customerrepo.BusinessCustomer) error {
			// 验证新联系人已添加且为非主要联系人
			require.Len(t, customer.Contacts, 1)
			// 验证新联系人
			require.Equal(t, "联系人1", customer.Contacts[0].FirstName)
			require.Equal(t, int8(1), customer.Contacts[0].IsPrimary)
			return nil
		})

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.NoError(t, err)
	})

	t.Run("测试更新客户-设置新的主要联系人", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Customer{
			ID: 1,
			Contact: &customer.Contact{
				ID:          2,
				GivenName:   "联系人2",
				PhoneNumber: "+8613912345678",
				IsPrimary:   customerpb.CustomerContact_PRIMARY,
			},
		}

		// 设置mock期望
		existingCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Contacts: []*customerrepo.Contact{
				{
					ID:          1,
					CustomerID:  1,
					FirstName:   "联系人1",
					PhoneNumber: "+8613912345678111",
					IsPrimary:   1,
				},
			},
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(existingCustomer, nil)
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).Return(existingCustomer.Contacts, nil)
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, customer *customerrepo.BusinessCustomer) error {
			// 验证新联系人已添加且为非主要联系人
			require.Len(t, customer.Contacts, 2)
			// 旧的联系人
			require.Equal(t, "联系人1", customer.Contacts[0].FirstName)
			require.Equal(t, int8(0), customer.Contacts[0].IsPrimary)
			// 新的联系人
			require.Equal(t, "联系人2", customer.Contacts[1].FirstName)
			require.Equal(t, int8(1), customer.Contacts[1].IsPrimary)
			return nil
		})

		// 执行测试
		err := logic.Update(context.Background(), req)
		require.NoError(t, err)
	})
}

func TestDelete(t *testing.T) {
	t.Run("测试删除客户成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)

		// 设置mock期望
		expectedCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Status:    int8(customerpb.Customer_NORMAL.Number()),
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(expectedCustomer, nil)
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).Return(nil)

		// 执行测试
		err := logic.Delete(context.Background(), customerID)
		require.NoError(t, err)
	})

	t.Run("测试删除客户-客户不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(999)

		// 设置mock期望
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(nil, nil)

		// 执行测试
		err := logic.Delete(context.Background(), customerID)
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))
	})

	t.Run("测试删除客户-数据库错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "数据库错误")
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		err := logic.Delete(context.Background(), customerID)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})

	t.Run("测试删除客户-同时删除联系人和地址", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)

		// 设置mock期望
		existingCustomer := &customerrepo.BusinessCustomer{
			ID:        1,
			FirstName: "测试",
			LastName:  "用户",
			Status:    int8(customerpb.Customer_NORMAL.Number()),
			Contacts: []*customerrepo.Contact{
				{
					ID:         1,
					CustomerID: 1,
					Status:     int8(customerpb.CustomerContact_NORMAL),
					UpdateTime: time.Now().Unix(),
				},
			},
			Addresses: []*customerrepo.Address{
				{
					ID:         1,
					CustomerID: 1,
					Status:     int8(customerpb.Address_NORMAL),
					UpdateTime: time.Now().Unix(),
				},
			},
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(existingCustomer, nil)
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, customer *customerrepo.BusinessCustomer) error {
			// 验证客户状态已更新
			require.Equal(t, int8(customerpb.Customer_DELETED.Number()), customer.Status)

			// 验证联系人状态已更新
			require.Equal(t, int8(customerpb.CustomerContact_DELETED.Number()), customer.Contacts[0].Status)

			// 验证地址状态已更新
			require.Equal(t, int8(customerpb.Address_DELETED.Number()), customer.Addresses[0].Status)

			return nil
		})

		// 执行测试
		err := logic.Delete(context.Background(), customerID)
		require.NoError(t, err)
	})
}

func TestCustomerEntity(t *testing.T) {
	t.Run("测试Contact.ConvIsPrimaryInt", func(t *testing.T) {
		tests := []struct {
			name     string
			contact  *customer.Contact
			expected int8
		}{
			{
				name:     "nil联系人",
				contact:  nil,
				expected: 0,
			},
			{
				name: "主要联系人",
				contact: &customer.Contact{
					IsPrimary: customerpb.CustomerContact_PRIMARY,
				},
				expected: 1,
			},
			{
				name: "非主要联系人",
				contact: &customer.Contact{
					IsPrimary: customerpb.CustomerContact_NO_PRIMARY,
				},
				expected: 0,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := tt.contact.ConvIsPrimary()
				require.Equal(t, tt.expected, result)
			})
		}
	})

	t.Run("测试Contact.GetIsPrimary", func(t *testing.T) {
		tests := []struct {
			name     string
			contact  *customer.Contact
			expected customerpb.CustomerContact_IsPrimary
		}{
			{
				name:     "nil联系人",
				contact:  nil,
				expected: customerpb.CustomerContact_IS_PRIMARY_UNSPECIFIED,
			},
			{
				name: "主要联系人",
				contact: &customer.Contact{
					IsPrimary: 1,
				},
				expected: customerpb.CustomerContact_PRIMARY,
			},
			{
				name: "非主要联系人",
				contact: &customer.Contact{
					IsPrimary: 0,
				},
				expected: customerpb.CustomerContact_NO_PRIMARY,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := tt.contact.GetIsPrimary()
				require.Equal(t, tt.expected, result)
			})
		}
	})

	t.Run("测试Address.ConvIsPrimaryInt", func(t *testing.T) {
		tests := []struct {
			name     string
			address  *customer.Address
			expected *int8
		}{
			{
				name:     "nil地址",
				address:  nil,
				expected: nil,
			},
			{
				name: "主要地址",
				address: &customer.Address{
					IsPrimary: pointer.Get(customerpb.Address_PRIMARY),
				},
				expected: pointer.Get(int8(1)),
			},
			{
				name: "非主要地址",
				address: &customer.Address{
					IsPrimary: pointer.Get(customerpb.Address_NO_PRIMARY),
				},
				expected: pointer.Get(int8(0)),
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := tt.address.ConvIsPrimaryInt()
				require.Equal(t, tt.expected, result)
			})
		}
	})

	t.Run("测试Address.GetIsPrimary", func(t *testing.T) {
		tests := []struct {
			name     string
			address  *customer.Address
			expected customerpb.Address_IsPrimary
		}{
			{
				name:     "nil地址",
				address:  nil,
				expected: customerpb.Address_IS_PRIMARY_UNSPECIFIED,
			},
			{
				name: "主要地址",
				address: &customer.Address{
					IsPrimary: pointer.Get(customerpb.Address_PRIMARY),
				},
				expected: customerpb.Address_PRIMARY,
			},
			{
				name: "非主要地址",
				address: &customer.Address{
					IsPrimary: pointer.Get(customerpb.Address_NO_PRIMARY),
				},
				expected: customerpb.Address_NO_PRIMARY,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := tt.address.GetIsPrimary()
				require.Equal(t, tt.expected, result)
			})
		}
	})

	t.Run("测试Customer.ToDB", func(t *testing.T) {
		birthTime := time.Now()
		c := &customer.Customer{
			CompanyID:           100,
			PreferredBusinessID: 200,
			AccountID:           300,
			GivenName:           "张",
			FamilyName:          "三",
			Email:               utils.ToPointer("<EMAIL>"),
			AvatarPath:          "/path/to/avatar",
			BirthTime:           &birthTime,
			Source:              "walk_in",
			Type:                customerpb.Customer_LEAD,
			LifeCycle:           customerpb.Customer_LIFE_CYCLE_LEAD,
			ActionState:         customerpb.Customer_LEAD_STATE,
			AllocateStaffID:     400,
		}

		dbCustomer := c.ToDB()

		require.Equal(t, c.CompanyID, dbCustomer.CompanyID)
		require.Equal(t, int(c.PreferredBusinessID), dbCustomer.BusinessID)
		require.Equal(t, c.AccountID, dbCustomer.AccountID)
		require.Equal(t, c.GivenName, dbCustomer.FirstName)
		require.Equal(t, c.FamilyName, dbCustomer.LastName)
		require.Equal(t, c.Email, dbCustomer.Email)
		require.Equal(t, c.AvatarPath, dbCustomer.AvatarPath)
		require.Equal(t, c.BirthTime, dbCustomer.Birthday)
		require.Equal(t, c.Source, dbCustomer.Source)
		require.Equal(t, c.Type, dbCustomer.Type)
		require.Equal(t, c.LifeCycle, dbCustomer.LifeCycle)
		require.Equal(t, c.ActionState, dbCustomer.ActionState)
		require.Equal(t, c.AllocateStaffID, dbCustomer.AllocateStaffID)
	})
	t.Run("测试Customer.ToPB", func(t *testing.T) {
		birthTime := time.Now()
		c := &customer.Customer{
			ID:                  1,
			CompanyID:           100,
			PreferredBusinessID: 200,
			AccountID:           300,
			GivenName:           "张",
			FamilyName:          "三",
			Email:               utils.ToPointer("<EMAIL>"),
			PhoneNumber:         "***********",
			AvatarPath:          "/path/to/avatar",
			BirthTime:           &birthTime,
			CustomerCode:        "CUST001",
			Type:                customerpb.Customer_LEAD,
			LifeCycle:           customerpb.Customer_LIFE_CYCLE_LEAD,
			ActionState:         customerpb.Customer_LEAD_STATE,
			AllocateStaffID:     400,
			Address: &customer.Address{
				Address1:  "北京市海淀区",
				Address2:  "中关村",
				City:      "北京",
				State:     "北京",
				Zipcode:   "100000",
				Country:   "CN",
				Status:    customerpb.Address_NORMAL,
				IsPrimary: pointer.Get(customerpb.Address_PRIMARY),
			},
			Contact: &customer.Contact{
				ID:              1,
				BusinessID:      200,
				CustomerID:      1,
				GivenName:       "李",
				FamilyName:      "四",
				PhoneNumber:     "***********",
				Email:           "<EMAIL>",
				Title:           "经理",
				Type:            customerpb.CustomerContact_MAIN,
				IsPrimary:       customerpb.CustomerContact_PRIMARY,
				State:           customerpb.CustomerContact_NORMAL,
				CreateTime:      time.Now(),
				UpdateTime:      time.Now(),
				CompanyID:       100,
				E164PhoneNumber: "+86***********",
			},
		}

		pbCustomer := c.ToPB()

		require.Equal(t, c.ID, pbCustomer.Id)
		require.Equal(t, c.CompanyID, pbCustomer.CompanyId)
		require.Equal(t, c.PreferredBusinessID, pbCustomer.PreferredBusinessId)
		require.Equal(t, c.AccountID, pbCustomer.AccountId)
		require.Equal(t, c.GivenName, pbCustomer.GivenName)
		require.Equal(t, c.FamilyName, pbCustomer.FamilyName)
		require.Equal(t, utils.ToValue(c.Email), pbCustomer.Email)
		require.Equal(t, c.PhoneNumber, pbCustomer.PhoneNumber)
		require.Equal(t, c.AvatarPath, pbCustomer.AvatarPath)
		require.NotNil(t, pbCustomer.BirthTime)
		require.Equal(t, c.CustomerCode, pbCustomer.CustomerCode)
		require.Equal(t, c.Type, pbCustomer.Type)
		require.Equal(t, c.LifeCycle, pbCustomer.LifeCycle)
		require.Equal(t, c.ActionState, pbCustomer.ActionState)
		require.Equal(t, c.AllocateStaffID, pbCustomer.AllocateStaffId)

		// 验证地址
		require.NotNil(t, pbCustomer.Address)
		require.Equal(t, c.Address.Address1, pbCustomer.Address.Address1)
		require.Equal(t, c.Address.Address2, pbCustomer.Address.Address2)
		require.Equal(t, c.Address.City, pbCustomer.Address.City)
		require.Equal(t, c.Address.State, pbCustomer.Address.State)
		require.Equal(t, c.Address.Zipcode, pbCustomer.Address.Zipcode)
		require.Equal(t, c.Address.Country, pbCustomer.Address.RegionCode)
		require.Equal(t, c.Address.Status, pbCustomer.Address.Status)
		require.Equal(t, c.Address.GetIsPrimary(), pbCustomer.Address.IsPrimary)

		// 验证联系人
		require.NotNil(t, pbCustomer.Contact)
		require.Equal(t, int64(c.Contact.ID), pbCustomer.Contact.Id)
		require.Equal(t, int64(c.Contact.BusinessID), pbCustomer.Contact.BusinessId)
		require.Equal(t, int64(c.Contact.CustomerID), pbCustomer.Contact.CustomerId)
		require.Equal(t, c.Contact.GivenName, pbCustomer.Contact.GivenName)
		require.Equal(t, c.Contact.FamilyName, pbCustomer.Contact.FamilyName)
		require.Equal(t, c.Contact.PhoneNumber, pbCustomer.Contact.PhoneNumber)
		require.Equal(t, c.Contact.Email, pbCustomer.Contact.Email)
		require.Equal(t, c.Contact.Title, pbCustomer.Contact.Title)
		require.Equal(t, c.Contact.Type, pbCustomer.Contact.Type)
		require.Equal(t, c.Contact.State, pbCustomer.Contact.State)
		require.Equal(t, c.Contact.IsPrimary, pbCustomer.Contact.IsPrimary)
		require.NotNil(t, pbCustomer.Contact.CreateTime)
		require.NotNil(t, pbCustomer.Contact.UpdateTime)
		require.Equal(t, c.Contact.CompanyID, pbCustomer.Contact.CompanyId)
		require.Equal(t, c.Contact.E164PhoneNumber, pbCustomer.Contact.E164PhoneNumber)
	})

	t.Run("测试Customer.ToPB-空字段", func(t *testing.T) {
		c := &customer.Customer{
			ID:        1,
			CompanyID: 100,
			// 其他字段为默认值
		}

		pbCustomer := c.ToPB()

		require.Equal(t, c.ID, pbCustomer.Id)
		require.Equal(t, c.CompanyID, pbCustomer.CompanyId)
		require.Nil(t, pbCustomer.BirthTime)
		require.Nil(t, pbCustomer.Address)
		require.Nil(t, pbCustomer.Contact)
	})
}

func TestGetByPhoneNumber(t *testing.T) {
	t.Run("测试通过手机号获取客户-成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(100)

		// 设置mock期望
		expectedCustomer := &customerrepo.BusinessCustomer{
			ID:        customerID,
			FirstName: "测试",
			LastName:  "用户",
		}

		customerRepo.EXPECT().GetCustomer(gomock.Any(), &customerrepo.GetCustomerParams{
			ID: customerID,
		}).Return(expectedCustomer, nil)

		// 执行测试
		result, err := logic.Get(context.Background(), &customer.GetCustomerParams{
			ID: customerID,
		})
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, customerID, result.ID)
	})

	t.Run("测试通过手机号获取客户-客户不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(100)

		// 设置mock期望
		customerRepo.EXPECT().GetCustomer(gomock.Any(), &customerrepo.GetCustomerParams{
			ID: customerID,
		}).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		result, err := logic.Get(context.Background(), &customer.GetCustomerParams{
			ID: customerID,
		})
		require.NoError(t, err)
		require.Nil(t, result)
	})

	t.Run("测试通过手机号获取客户-获取客户失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(100)

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "数据库错误")
		customerRepo.EXPECT().GetCustomer(gomock.Any(), &customerrepo.GetCustomerParams{
			ID: customerID,
		}).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.Get(context.Background(), &customer.GetCustomerParams{
			ID: customerID,
		})
		require.Error(t, err)
		require.Nil(t, result)
		require.Equal(t, expectedErr, err)
	})
}

func TestUpdateAddress(t *testing.T) {
	t.Run("测试更新地址-成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)
		req := &customer.Address{
			ID:         1,
			CustomerID: customerID,
			Address1:   "更新后的地址1",
			City:       "更新后的城市",
			State:      "更新后的省份",
			Country:    "更新后的国家",
			IsPrimary:  pointer.Get(customerpb.Address_PRIMARY),
		}

		// 设置mock期望
		existingAddress := &customerrepo.Address{
			ID:         1,
			CustomerID: int(customerID),
			Address1:   "原地址1",
			IsPrimary:  pointer.Get(int8(1)),
		}
		customerRepo.EXPECT().GetAddress(gomock.Any(), gomock.Eq(int64(1))).Return(existingAddress, nil)
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(customerID), gomock.Any()).Return([]*customerrepo.Address{existingAddress}, nil)
		customerRepo.EXPECT().BatchUpdateAddress(gomock.Any(), gomock.Any()).Return(nil)

		// 执行测试
		err := logic.UpdateAddress(context.Background(), req)
		require.NoError(t, err)
	})

	t.Run("测试更新地址-第一条地址必须为primary", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)
		req := &customer.Address{
			ID:         1,
			CustomerID: customerID,
			Address1:   "新地址1",
			IsPrimary:  pointer.Get(customerpb.Address_NO_PRIMARY), // 尝试设置为非primary
		}

		// 设置mock期望
		existingAddress := &customerrepo.Address{
			ID:         1,
			CustomerID: int(customerID),
			Address1:   "原地址1",
			IsPrimary:  pointer.Get(int8(1)),
		}
		customerRepo.EXPECT().GetAddress(gomock.Any(), gomock.Eq(int64(1))).Return(existingAddress, nil)

		// 执行测试
		err := logic.UpdateAddress(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("测试更新地址-设置新的primary地址", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)
		req := &customer.Address{
			ID:         2,
			CustomerID: customerID,
			Address1:   "新地址2",
			IsPrimary:  pointer.Get(customerpb.Address_PRIMARY),
		}

		// 设置mock期望
		existingAddresses := []*customerrepo.Address{
			{
				ID:         1,
				CustomerID: int(customerID),
				Address1:   "原地址1",
				IsPrimary:  pointer.Get(int8(1)),
			},
			{
				ID:         2,
				CustomerID: int(customerID),
				Address1:   "原地址2",
				IsPrimary:  pointer.Get(int8(0)),
			},
		}
		customerRepo.EXPECT().GetAddress(gomock.Any(), gomock.Eq(int64(2))).Return(existingAddresses[1], nil)
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(customerID), gomock.Any()).Return(existingAddresses, nil)
		customerRepo.EXPECT().BatchUpdateAddress(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, addresses []*customerrepo.Address) error {
			// 验证原来的primary地址被设置为非primary
			require.Equal(t, pointer.Get(int8(0)), addresses[0].IsPrimary)
			// 验证新的地址被设置为primary
			require.Equal(t, pointer.Get(int8(1)), addresses[1].IsPrimary)
			return nil
		})

		// 执行测试
		err := logic.UpdateAddress(context.Background(), req)
		require.NoError(t, err)
	})

	t.Run("测试更新地址-只有一条地址不能设为非primary", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)
		req := &customer.Address{
			ID:         1,
			CustomerID: customerID,
			Address1:   "更新后的地址1",
			IsPrimary:  pointer.Get(customerpb.Address_NO_PRIMARY),
		}

		// 设置mock期望
		existingAddress := &customerrepo.Address{
			ID:         1,
			CustomerID: int(customerID),
			Address1:   "原地址",
			IsPrimary:  pointer.Get(int8(1)),
		}
		customerRepo.EXPECT().GetAddress(gomock.Any(), gomock.Eq(int64(1))).Return(existingAddress, nil)

		// 执行测试
		err := logic.UpdateAddress(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("测试更新地址-获取现有地址失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)
		req := &customer.Address{
			ID:         1,
			CustomerID: customerID,
			Address1:   "更新后的地址1",
		}

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "数据库错误")
		customerRepo.EXPECT().GetAddress(gomock.Any(), gomock.Eq(int64(1))).Return(nil, expectedErr)

		// 执行测试
		err := logic.UpdateAddress(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})

	t.Run("测试更新地址-更新现有地址", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)
		req := &customer.Address{
			ID:         1,
			CustomerID: customerID,
			Address1:   "更新后的地址1",
			City:       "更新后的城市",
			State:      "更新后的省份",
			Country:    "更新后的国家",
			IsPrimary:  pointer.Get(customerpb.Address_PRIMARY),
		}

		// 设置mock期望
		existingAddress := &customerrepo.Address{
			ID:         1,
			CustomerID: int(customerID),
			Address1:   "原地址1",
			City:       "原城市",
			State:      "原省份",
			Country:    "原国家",
			IsPrimary:  pointer.Get(int8(1)),
		}
		customerRepo.EXPECT().GetAddress(gomock.Any(), gomock.Eq(int64(1))).Return(existingAddress, nil)
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(customerID), gomock.Any()).Return([]*customerrepo.Address{existingAddress}, nil)
		customerRepo.EXPECT().BatchUpdateAddress(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, addresses []*customerrepo.Address) error {
			// 验证地址被正确更新
			require.Equal(t, "更新后的地址1", addresses[0].Address1)
			require.Equal(t, "更新后的城市", addresses[0].City)
			require.Equal(t, "更新后的省份", addresses[0].State)
			require.Equal(t, "更新后的国家", addresses[0].Country)
			return nil
		})

		// 执行测试
		err := logic.UpdateAddress(context.Background(), req)
		require.NoError(t, err)
	})
}

func TestCreateAddress(t *testing.T) {
	t.Run("测试创建地址-成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Address{
			CustomerID: 1,
			CompanyID:  100,
			Address1:   "测试地址1",
			Address2:   "测试地址2",
			City:       "测试城市",
			State:      "测试省份",
			Zipcode:    "123456",
			Country:    "CN",
			Lat:        "39.9042",
			Lng:        "116.4074",
			IsPrimary:  pointer.Get(customerpb.Address_PRIMARY),
		}

		// 设置mock期望
		expectedID := int64(1)
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(req.CustomerID), gomock.Any()).Return([]*customerrepo.Address{}, nil)

		// 添加对Tx方法的期望
		customerRepo.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(repo customerrepo.Repository) error) error {
				return fn(customerRepo)
			})

		// 添加对CreateAddress方法的期望
		customerRepo.EXPECT().CreateAddress(gomock.Any(), gomock.Any()).Return(expectedID, nil)

		// 执行测试
		id, err := logic.CreateAddress(context.Background(), req)
		require.NoError(t, err)
		require.Equal(t, expectedID, id)
	})

	t.Run("测试创建地址-创建失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Address{
			CustomerID: 1,
			Address1:   "测试地址",
			IsPrimary:  pointer.Get(customerpb.Address_PRIMARY),
		}

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "创建地址失败")
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(req.CustomerID), gomock.Any()).Return([]*customerrepo.Address{}, nil)

		// 添加对Tx方法的期望
		customerRepo.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(repo customerrepo.Repository) error) error {
				return fn(customerRepo)
			})

		// 添加对CreateAddress方法的期望
		customerRepo.EXPECT().CreateAddress(gomock.Any(), gomock.Any()).Return(int64(0), expectedErr)

		// 执行测试
		id, err := logic.CreateAddress(context.Background(), req)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
		require.Equal(t, int64(0), id)
	})

	t.Run("测试创建地址-在已有primary地址时创建新的primary地址", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		req := &customer.Address{
			CustomerID: 1,
			CompanyID:  100,
			Address1:   "测试地址",
			IsPrimary:  pointer.Get(customerpb.Address_PRIMARY),
		}

		// 设置mock期望
		existingAddresses := []*customerrepo.Address{
			{
				ID:        1,
				Address1:  "原地址1",
				IsPrimary: pointer.Get(int8(1)),
			},
		}
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(req.CustomerID), gomock.Any()).Return(existingAddresses, nil)

		// 添加对Tx方法的期望
		customerRepo.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(repo customerrepo.Repository) error) error {
				return fn(customerRepo)
			})

		// 添加对CreateAddress方法的期望
		// 创建新的primary地址
		customerRepo.EXPECT().CreateAddress(gomock.Any(), gomock.Any()).Return(int64(2), nil)

		// 添加对BatchUpdateAddress方法的期望
		customerRepo.EXPECT().BatchUpdateAddress(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, addresses []*customerrepo.Address) error {
				require.Equal(t, int(1), addresses[0].ID)
				require.Equal(t, pointer.Get(int8(0)), addresses[0].IsPrimary)
				return nil
			})

		// 执行测试
		id, err := logic.CreateAddress(context.Background(), req)
		require.NoError(t, err)
		require.Equal(t, int64(2), id)
	})
}

func TestDeleteAddress(t *testing.T) {
	t.Run("测试删除地址-成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		addressID := int64(1)

		// 设置mock期望
		customerRepo.EXPECT().DeleteAddress(gomock.Any(), gomock.Eq(addressID)).Return(nil)

		// 执行测试
		err := logic.DeleteAddress(context.Background(), addressID)
		require.NoError(t, err)
	})

	t.Run("测试删除地址-删除失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		addressID := int64(1)

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "删除地址失败")
		customerRepo.EXPECT().DeleteAddress(gomock.Any(), gomock.Eq(addressID)).Return(expectedErr)

		// 执行测试
		err := logic.DeleteAddress(context.Background(), addressID)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
	})
}

func TestListAddresses(t *testing.T) {
	t.Run("测试获取地址列表-成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.ListAddressesParams{
			CustomerID: 1,
			PageNum:    1,
			PageSize:   10,
		}

		// 设置mock期望
		expectedAddresses := []*customerrepo.Address{
			{
				ID:         1,
				CustomerID: 1,
				CompanyID:  100,
				Address1:   "地址1",
				City:       "城市1",
				State:      "省份1",
				Country:    "CN",
				IsPrimary:  pointer.Get(int8(1)),
				Status:     int8(customerpb.Address_NORMAL.Number()),
				CreateTime: time.Now().Unix(),
				UpdateTime: time.Now().Unix(),
			},
			{
				ID:         2,
				CustomerID: 1,
				CompanyID:  100,
				Address1:   "地址2",
				City:       "城市2",
				State:      "省份2",
				Country:    "CN",
				IsPrimary:  pointer.Get(int8(0)),
				Status:     int8(customerpb.Address_NORMAL.Number()),
				CreateTime: time.Now().Unix(),
				UpdateTime: time.Now().Unix(),
			},
		}

		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(params.CustomerID), gomock.Any()).DoAndReturn(
			func(ctx context.Context, customerID int64, pagination *db.Pagination) ([]*customerrepo.Address, error) {
				// 验证分页参数
				require.Equal(t, (params.PageNum-1)*params.PageSize, pagination.Offset)
				require.Equal(t, params.PageSize, pagination.Limit)
				return expectedAddresses, nil
			})

		// 执行测试
		result, err := logic.ListAddresses(context.Background(), params)
		require.NoError(t, err)
		require.Len(t, result, 2)

		// 验证第一个地址
		require.Equal(t, int(1), result[0].ID)
		require.Equal(t, int64(1), result[0].CustomerID)
		require.Equal(t, int64(100), result[0].CompanyID)
		require.Equal(t, "地址1", result[0].Address1)
		require.Equal(t, "城市1", result[0].City)
		require.Equal(t, "省份1", result[0].State)
		require.Equal(t, "CN", result[0].Country)
		require.Equal(t, customerpb.Address_PRIMARY, result[0].GetIsPrimary())
		require.Equal(t, customerpb.Address_NORMAL, result[0].Status)

		// 验证第二个地址
		require.Equal(t, int(2), result[1].ID)
		require.Equal(t, int64(1), result[1].CustomerID)
		require.Equal(t, int64(100), result[1].CompanyID)
		require.Equal(t, "地址2", result[1].Address1)
		require.Equal(t, "城市2", result[1].City)
		require.Equal(t, "省份2", result[1].State)
		require.Equal(t, "CN", result[1].Country)
		require.Equal(t, customerpb.Address_NO_PRIMARY, result[1].GetIsPrimary())
		require.Equal(t, customerpb.Address_NORMAL, result[1].Status)
	})

	t.Run("测试获取地址列表-空结果", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.ListAddressesParams{
			CustomerID: 1,
			PageNum:    1,
			PageSize:   10,
		}

		// 设置mock期望
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(params.CustomerID), gomock.Any()).Return([]*customerrepo.Address{}, nil)

		// 执行测试
		result, err := logic.ListAddresses(context.Background(), params)
		require.NoError(t, err)
		require.Empty(t, result)
	})

	t.Run("测试获取地址列表-获取失败", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		params := &customer.ListAddressesParams{
			CustomerID: 1,
			PageNum:    1,
			PageSize:   10,
		}

		// 设置mock期望
		expectedErr := status.Error(codes.Internal, "获取地址列表失败")
		customerRepo.EXPECT().GetAddresses(gomock.Any(), gomock.Eq(params.CustomerID), gomock.Any()).Return(nil, expectedErr)

		// 执行测试
		result, err := logic.ListAddresses(context.Background(), params)
		require.Error(t, err)
		require.Equal(t, expectedErr, err)
		require.Nil(t, result)
	})
}

func TestAddressToPB(t *testing.T) {
	t.Run("测试地址转换为PB-成功", func(t *testing.T) {
		// 准备测试数据
		createTime := time.Now()
		updateTime := time.Now()
		address := &customer.Address{
			ID:         1,
			CustomerID: 100,
			CompanyID:  200,
			Address1:   "测试地址1",
			Address2:   "测试地址2",
			City:       "测试城市",
			State:      "测试省份",
			Zipcode:    "123456",
			Country:    "CN",
			Lat:        "39.9042",
			Lng:        "116.4074",
			Status:     customerpb.Address_NORMAL,
			IsPrimary:  pointer.Get(customerpb.Address_PRIMARY),
			CreateTime: createTime,
			UpdateTime: updateTime,
		}

		// 执行转换
		pbAddress := address.ToPB()

		// 验证转换结果
		require.Equal(t, int64(1), pbAddress.Id)
		require.Equal(t, int64(100), pbAddress.CustomerId)
		require.Equal(t, "测试地址1", pbAddress.Address1)
		require.Equal(t, "测试地址2", pbAddress.Address2)
		require.Equal(t, "测试城市", pbAddress.City)
		require.Equal(t, "测试省份", pbAddress.State)
		require.Equal(t, "123456", pbAddress.Zipcode)
		require.Equal(t, "CN", pbAddress.RegionCode)
		require.Equal(t, customerpb.Address_NORMAL, pbAddress.Status)
		require.Equal(t, customerpb.Address_PRIMARY, pbAddress.IsPrimary)
	})

	t.Run("测试地址转换为PB-空地址", func(t *testing.T) {
		// 准备测试数据
		address := &customer.Address{}

		// 执行转换
		pbAddress := address.ToPB()

		// 验证转换结果
		require.Equal(t, int64(0), pbAddress.Id)
		require.Equal(t, int64(0), pbAddress.CustomerId)
		require.Empty(t, pbAddress.Address1)
		require.Empty(t, pbAddress.Address2)
		require.Empty(t, pbAddress.City)
		require.Empty(t, pbAddress.State)
		require.Empty(t, pbAddress.Zipcode)
		require.Empty(t, pbAddress.RegionCode)
		require.Equal(t, customerpb.Address_STATE_UNSPECIFIED, pbAddress.Status)
		require.Equal(t, customerpb.Address_IS_PRIMARY_UNSPECIFIED, pbAddress.GetIsPrimary())
	})

	t.Run("测试地址转换为PB-部分字段", func(t *testing.T) {
		// 准备测试数据
		address := &customer.Address{
			ID:         1,
			CustomerID: 100,
			Address1:   "测试地址1",
			Status:     customerpb.Address_NORMAL,
			IsPrimary:  pointer.Get(customerpb.Address_NO_PRIMARY),
		}

		// 执行转换
		pbAddress := address.ToPB()

		// 验证转换结果
		require.Equal(t, int64(1), pbAddress.Id)
		require.Equal(t, int64(100), pbAddress.CustomerId)
		require.Equal(t, "测试地址1", pbAddress.Address1)
		require.Empty(t, pbAddress.Address2)
		require.Empty(t, pbAddress.City)
		require.Empty(t, pbAddress.State)
		require.Empty(t, pbAddress.Zipcode)
		require.Empty(t, pbAddress.RegionCode)
		require.Equal(t, customerpb.Address_NORMAL, pbAddress.Status)
		require.Equal(t, customerpb.Address_NO_PRIMARY, pbAddress.IsPrimary)
	})
}

func TestConvertCustomer(t *testing.T) {
	t.Run("测试将潜在客户转换为正式客户-成功", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 准备测试数据
		customerID := int64(1)

		// 设置mock期望
		lead := &customerrepo.BusinessCustomer{
			ID:         customerID,
			BusinessID: 100,
			CompanyID:  200,
			Type:       customerpb.Customer_LEAD,
			Status:     int8(customerpb.Customer_NORMAL),
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(lead, nil)
		customerRepo.EXPECT().UpdateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, customer *customerrepo.BusinessCustomer) error {
			// 验证客户类型已更新
			require.Equal(t, customerpb.Customer_CUSTOMER, customer.Type)
			// 验证默认值已设置
			require.Equal(t, "#000000", customer.ClientColor)
			require.Equal(t, int8(1), customer.SendAutoMessage)
			require.Equal(t, int8(1), customer.SendAppAutoMessage)
			require.Equal(t, int8(-127), customer.UnconfirmedReminderBy)
			require.Equal(t, 28, customer.PreferredFrequencyDay)
			require.Equal(t, int8(1), customer.PreferredFrequencyType)
			require.Equal(t, "[0,1,2,3,4,5,6]", customer.PreferredDay)
			require.Equal(t, "[0,1435]", customer.PreferredTime)
			return nil
		})

		// 执行测试
		err := logic.ConvertCustomer(context.Background(), customerID)
		require.NoError(t, err)
	})

	t.Run("测试将潜在客户转换为正式客户-无效的客户ID", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 执行测试
		err := logic.ConvertCustomer(context.Background(), 0)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("测试将潜在客户转换为正式客户-客户不存在", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 设置mock期望
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound)

		// 执行测试
		err := logic.ConvertCustomer(context.Background(), 1)
		require.Error(t, err)
	})

	t.Run("测试将潜在客户转换为正式客户-无效的客户数据", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := dbmock.NewMockTransactionManager(ctrl)

		logic := customer.NewByParams(customerRepo, txManager)

		// 设置mock期望
		lead := &customerrepo.BusinessCustomer{
			ID:     1,
			Type:   customerpb.Customer_CUSTOMER, // 不是潜在客户
			Status: int8(customerpb.Customer_NORMAL),
		}
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(lead, nil)

		// 执行测试
		err := logic.ConvertCustomer(context.Background(), 1)
		require.Error(t, err)
		require.Equal(t, codes.InvalidArgument, status.Code(err))
	})
}

func TestCoverageImprovements(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()
	customerRepo := customermock.NewMockRepository(ctrl)
	txManager := dbmock.NewMockTransactionManager(ctrl)
	logic := customer.NewByParams(customerRepo, txManager)

	t.Run("Create with PreferredDay and PreferredTime", func(t *testing.T) {
		customerRepo.EXPECT().GetContacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*customerrepo.Contact{}, nil)
		customerRepo.EXPECT().CreateCustomer(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, customer *customerrepo.BusinessCustomer) (*customerrepo.BusinessCustomer, error) {
				require.Equal(t, "[1,2,3]", customer.PreferredDay)
				require.Equal(t, "[9,10,11]", customer.PreferredTime)
				customer.ID = 1
				return customer, nil
			},
		)
		customerRepo.EXPECT().GetCustomer(gomock.Any(), gomock.Any()).Return(&customerrepo.BusinessCustomer{ID: 1}, nil).AnyTimes()

		preferredDay := []int64{1, 2, 3}
		preferredTime := []int64{9, 10, 11}
		_, err := logic.Create(ctx, &customer.Customer{
			CompanyID:     1,
			GivenName:     "Test",
			FamilyName:    "User",
			PhoneNumber:   "1234567890",
			PreferredDay:  preferredDay,
			PreferredTime: preferredTime,
		})
		require.NoError(t, err)
	})

	t.Run("List customers error", func(t *testing.T) {
		customerRepo.EXPECT().CountCustomers(gomock.Any(), gomock.Any(), gomock.Any()).Return(int64(10), nil)
		customerRepo.EXPECT().ListCustomers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, status.Error(codes.Internal, "list error"))

		_, err := logic.List(ctx, &customer.ListCustomersParams{
			CompanyID: 1,
			PageNum:   1,
			PageSize:  10,
		})
		require.Error(t, err)
	})
}
