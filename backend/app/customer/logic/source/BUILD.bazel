load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "source",
    srcs = [
        "entity.go",
        "source.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/source",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/source",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)
