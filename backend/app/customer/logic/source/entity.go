package source

// CreateSourceDatum 创建参数
type CreateSourceDatum struct {
	CompanyID  int64
	BusinessID int64
	Name       string
	Sort       int32
	StaffID    int64
}

// UpdateSourceDatum 更新参数
type UpdateSourceDatum struct {
	SourceID int64
	Name     *string
	Sort     *int32
	StaffID  int64
}

// ListSourcesDatum 查询参数
type ListSourcesDatum struct {
	CompanyID  *int64
	CustomerID *int64
	IDs        []int64
}

// DeleteSourceDatum 删除参数
type DeleteSourceDatum struct {
	SourceID int64
	StaffID  int64
}
