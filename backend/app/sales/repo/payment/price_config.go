package payment

import (
	"context"

	"github.com/shopspring/decimal"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type PriceLevel int32

const (
	PriceLevelGrowthGrooming   PriceLevel = 1101
	PriceLevelUltimateGrooming PriceLevel = 1201
	PriceLevelGrowthBD         PriceLevel = 1102
	PriceLevelUltimateBD       PriceLevel = 1202
	PriceLevelEnterpriseBD     PriceLevel = 1302
)

type BusinessType int32

const (
	BusinessTypeMobile BusinessType = 0
	BusinessTypeSalon  BusinessType = 1
)

func getPriceLevel(plan salespb.SubscriptionPlan) []PriceLevel {
	switch plan {
	case salespb.SubscriptionPlan_GROWTH_GROOMING:
		return []PriceLevel{PriceLevelGrowthGrooming}
	case salespb.SubscriptionPlan_ULTIMATE_GROOMING:
		return []PriceLevel{PriceLevelUltimateGrooming}
	case salespb.SubscriptionPlan_GROWTH_BOARDING_DAYCARE:
		return []PriceLevel{PriceLevelGrowthBD, PriceLevelGrowthGrooming}
	case salespb.SubscriptionPlan_ULTIMATE_BOARDING_DAYCARE:
		return []PriceLevel{PriceLevelUltimateBD, PriceLevelUltimateGrooming}
	case salespb.SubscriptionPlan_ENTERPRISE_BOARDING_DAYCARE:
		return []PriceLevel{PriceLevelEnterpriseBD, PriceLevelUltimateGrooming}
	}
	return []PriceLevel{}
}

type Price struct {
	GroomingLocation decimal.Decimal
	BDLocation       decimal.Decimal
	Van              decimal.Decimal
}

type PriceConfig struct {
	ID           int64        `json:"id"`
	StripePlanID string       `json:"stripePlanId"`
	PlanName     string       `json:"planName"`
	Price        float64      `json:"price"`
	Title        string       `json:"title"`
	Description  string       `json:"description"`
	BusinessType BusinessType `json:"businessType"`
	Level        PriceLevel   `json:"level"`
}

type ListPriceConfigParams struct {
	IsAll bool       `json:"isAll"`
	Level PriceLevel `json:"level"`
}

type PriceConfigClient interface {
	GetPrice(ctx context.Context, plan salespb.SubscriptionPlan) (*Price, error)
}

type priceConfigClientImpl struct {
	client http.Client
}

func (i *priceConfigClientImpl) GetPrice(ctx context.Context, plan salespb.SubscriptionPlan) (*Price, error) {
	levels := getPriceLevel(plan)
	var configs []*PriceConfig
	for _, level := range levels {
		params := ListPriceConfigParams{
			Level: level,
		}
		var res []*PriceConfig
		err := i.client.Post(ctx, "/service/payment/subscription/plans", params, &res)
		if err != nil {
			return nil, err
		}

		configs = append(configs, res...)
	}

	groomingLocationPrice := decimal.Zero
	bdLocationPrice := decimal.Zero
	vanPrice := decimal.Zero

	for _, c := range configs {
		price := decimal.NewFromFloat(c.Price)
		switch c.BusinessType {
		case BusinessTypeSalon:
			switch c.Level {
			case PriceLevelGrowthGrooming, PriceLevelUltimateGrooming:
				groomingLocationPrice = price
			case PriceLevelGrowthBD, PriceLevelUltimateBD, PriceLevelEnterpriseBD:
				bdLocationPrice = price
			}
		case BusinessTypeMobile:
			vanPrice = price
		}
	}

	return &Price{
		GroomingLocation: groomingLocationPrice,
		BDLocation:       bdLocationPrice,
		Van:              vanPrice,
	}, nil
}

func NewPriceConfigClient() PriceConfigClient {
	return &priceConfigClientImpl{
		client: GetClient(),
	}
}
