package sales

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/sales/config"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/sales"
	"github.com/MoeGolibrary/moego/backend/app/sales/repo/salesforce"
	"github.com/MoeGolibrary/moego/backend/app/sales/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	salespb "github.com/MoeGolibrary/moego/backend/proto/sales/v1"
)

type Logic struct {
	opportunity sales.OpportunityReadWriter
	lineItem    sales.OpportunityLineItemReadWriter

	sf salesforce.Client
}

func NewLogic() *Logic {
	return &Logic{
		opportunity: sales.NewOpportunityRW(),
		lineItem:    sales.NewOpportunityLineItemRW(),
		sf:          salesforce.New(config.GetCfg().Salesforce),
	}
}

func NewByParams(
	opportunity sales.OpportunityReadWriter,
	lineItem sales.OpportunityLineItemReadWriter,
	sf salesforce.Client,
) *Logic {
	return &Logic{
		opportunity: opportunity,
		lineItem:    lineItem,
		sf:          sf,
	}
}

func (l *Logic) SyncOpportunity(ctx context.Context, params *OpportunitySyncParams) error {
	// 先检查这个 opportunity 是否在 salesforce 上存在
	sfop, err := l.sf.GetOpportunity(ctx, params.ID)
	if err != nil {
		return err
	}

	// 查询  opportunity, 如果数据库中不存在则创建一个
	op, err := l.opportunity.Get(ctx, params.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.InfoContextf(ctx, "First sync of opportunity %s", params.ID)
		op = &sales.Opportunity{
			ID: params.ID,
		}
	} else if err != nil {
		return err
	}
	params.ApplyTo(op)
	// account id 从 sfop 取
	op.SalesforceAccountID = sfop.AccountID

	// 先保存到数据库
	if err := l.opportunity.Save(ctx, op); err != nil {
		return err
	}

	// 更新到 salesforce
	return l.sf.UpdateOpportunity(ctx, op.ToSalesforceEntity())
}

func (l *Logic) SyncSalesSubscription(ctx context.Context, req *salespb.SyncSalesSubscriptionRequest) error {
	items := l.buildSubscriptionItems(ctx, req)
	if len(items) == 0 {
		return nil
	}
	return l.syncOpportunityLineItems(ctx, items)
}

func (l *Logic) SyncSalesHardware(ctx context.Context, req *salespb.SyncSalesHardwareRequest) error {
	items := l.buildHardwareLineItems(ctx, req)
	if len(items) == 0 {
		return nil
	}
	return l.syncOpportunityLineItems(ctx, items)
}

func (l *Logic) syncOpportunityLineItems(ctx context.Context, items []*sales.OpportunityLineItem) error {
	// 理论上这组 item 是同一个  opportunity 的，因此只需要取第一个 item 的 opportunityID
	opportunityID := items[0].OpportunityID
	productIDs := make([]string, 0, len(items))
	for _, item := range items {
		productIDs = append(productIDs, item.ProductID)
	}

	// 保存到数据库, 如果有相同的 productID, 则先删除再插入
	deleteItems, err := l.lineItem.DeleteItems(ctx, opportunityID, productIDs...)
	if err != nil {
		return err
	}
	if len(deleteItems) > 0 {
		log.WarnContextf(ctx, "delete items: %s", utils.ToJSON(deleteItems))
	}

	if err := l.lineItem.BatchCreate(ctx, items); err != nil {
		return err
	}

	// 保存到 salesforce, 同样如果有相同的 productID, 则先删除再插入
	sfOldItems, err := l.sf.ListOpportunityLineItems(ctx, opportunityID, productIDs...)
	if err != nil {
		return err
	}
	if len(sfOldItems) > 0 {
		itemIDs := make([]string, len(sfOldItems))
		for i := range sfOldItems {
			itemIDs[i] = *sfOldItems[i].ID
		}
		if err := l.sf.DeleteOpportunityLineItems(ctx, itemIDs); err != nil {
			log.ErrorContextf(ctx, "Failed to delete opportunity line items, opportunity ID: %s, err: %v",
				opportunityID, err)
			return err
		}
	}

	var sfItems []*salesforce.OpportunityLineItem
	for _, item := range items {
		sfItems = append(sfItems, item.ToSalesforceEntity())
	}
	return l.sf.CreateOpportunityLineItems(ctx, sfItems)
}

type OpportunitySyncParams struct {
	ID                    string
	Email                 *string
	Tier                  *string
	TerminalPercentage    *string
	TerminalFixed         *string
	NonTerminalPercentage *string
	NonTerminalFixed      *string
	MinVolume             *string
	SPIF                  *string
}

func (p *OpportunitySyncParams) ApplyTo(opportunity *sales.Opportunity) {
	setters := []struct {
		cond func() bool
		set  func(*sales.Opportunity)
	}{
		{
			cond: func() bool { return p.Email != nil },
			set:  func(o *sales.Opportunity) { o.Email = p.Email },
		},
		{
			cond: func() bool { return p.Tier != nil },
			set:  func(o *sales.Opportunity) { o.Tier = p.Tier },
		},
		{
			cond: func() bool { return p.TerminalPercentage != nil },
			set:  func(o *sales.Opportunity) { o.TerminalPercentage = p.TerminalPercentage },
		},
		{
			cond: func() bool { return p.TerminalFixed != nil },
			set:  func(o *sales.Opportunity) { o.TerminalFixed = p.TerminalFixed },
		},
		{
			cond: func() bool { return p.NonTerminalPercentage != nil },
			set:  func(o *sales.Opportunity) { o.NonTerminalPercentage = p.NonTerminalPercentage },
		},
		{
			cond: func() bool { return p.NonTerminalFixed != nil },
			set:  func(o *sales.Opportunity) { o.NonTerminalFixed = p.NonTerminalFixed },
		},
		{
			cond: func() bool { return p.MinVolume != nil },
			set:  func(o *sales.Opportunity) { o.MinVolume = p.MinVolume },
		},
		{
			cond: func() bool { return p.SPIF != nil },
			set:  func(o *sales.Opportunity) { o.SPIF = p.SPIF },
		},
	}

	// set if not nil, 用这种写法降低圈复杂度
	for _, s := range setters {
		if s.cond() {
			s.set(opportunity)
		}
	}
}

func (l *Logic) buildSubscriptionItems(ctx context.Context,
	req *salespb.SyncSalesSubscriptionRequest) []*sales.OpportunityLineItem {
	var items []*sales.OpportunityLineItem
	var salonProduct, mobileProduct *salesforce.Product
	switch req.SubscriptionPlan {
	case salespb.SubscriptionPlan_GROWTH_GROOMING:
		{
			if req.Salon != nil {
				salonProduct = l.sf.GetProduct(ctx, "Grooming Salon - Growth")
			}
			if req.Van != nil {
				mobileProduct = l.sf.GetProduct(ctx, "Grooming Mobile - Growth")
			}
		}
	case salespb.SubscriptionPlan_ULTIMATE_GROOMING:
		{
			if req.Salon != nil {
				salonProduct = l.sf.GetProduct(ctx, "Grooming Salon - Ultimate")
			}
			if req.Van != nil {
				mobileProduct = l.sf.GetProduct(ctx, "Grooming Mobile - Ultimate")
			}
		}
	case salespb.SubscriptionPlan_GROWTH_BOARDING_DAYCARE:
		{
			salonProduct = l.sf.GetProduct(ctx, "B&D - Pro")
		}
	case salespb.SubscriptionPlan_ULTIMATE_BOARDING_DAYCARE:
		{
			salonProduct = l.sf.GetProduct(ctx, "B&D - Ultimate")
		}
	case salespb.SubscriptionPlan_ENTERPRISE_BOARDING_DAYCARE:
		{
			salonProduct = l.sf.GetProduct(ctx, "Enterprise Plan")
		}
	}

	if salonProduct != nil {
		items = append(items, &sales.OpportunityLineItem{
			OpportunityID:      req.OpportunityId,
			ProductID:          salonProduct.ID,
			UnitPrice:          utils.MoneyToFloat(req.Salon.UnitPrice),
			Quantity:           req.Salon.Quantity,
			DiscountPercentage: utils.DecimalToFloat(req.Salon.DiscountPercentage),
			ContractLink:       req.ContractLink,
			ContractType:       utils.GetContractTerm(req.SubscriptionTerm),
			ContractSigned:     req.ContractSigned,
		})
	}
	if mobileProduct != nil {
		items = append(items, &sales.OpportunityLineItem{
			OpportunityID:      req.OpportunityId,
			ProductID:          mobileProduct.ID,
			UnitPrice:          utils.MoneyToFloat(req.Van.UnitPrice),
			Quantity:           req.Van.Quantity,
			DiscountPercentage: utils.DecimalToFloat(req.Van.DiscountPercentage),
			ContractLink:       req.ContractLink,
			ContractType:       utils.GetContractTerm(req.SubscriptionTerm),
			ContractSigned:     req.ContractSigned,
		})
	}

	return items
}

func (l *Logic) buildHardwareLineItems(ctx context.Context,
	req *salespb.SyncSalesHardwareRequest) []*sales.OpportunityLineItem {
	var items []*sales.OpportunityLineItem
	if req.ReaderM2 != nil {
		if product := l.sf.GetProduct(ctx, "Reader M2"); product != nil {
			items = append(items, &sales.OpportunityLineItem{
				OpportunityID:      req.OpportunityId,
				ProductID:          product.ID,
				UnitPrice:          utils.MoneyToFloat(req.ReaderM2.UnitPrice),
				Quantity:           req.ReaderM2.Quantity,
				DiscountPercentage: utils.DecimalToFloat(req.ReaderM2.DiscountPercentage),
			})
		}
	}

	if req.Bbpos != nil {
		if product := l.sf.GetProduct(ctx, "BBPOS WisePOS E"); product != nil {
			items = append(items, &sales.OpportunityLineItem{
				OpportunityID:      req.OpportunityId,
				ProductID:          product.ID,
				UnitPrice:          utils.MoneyToFloat(req.Bbpos.UnitPrice),
				Quantity:           req.Bbpos.Quantity,
				DiscountPercentage: utils.DecimalToFloat(req.Bbpos.DiscountPercentage),
			})
		}
	}

	return items
}
