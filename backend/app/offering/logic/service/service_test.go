package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/service/mocks"
	attributemocks "github.com/MoeGolibrary/moego/backend/app/offering/repo/serviceattribute/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestLogic_CreateService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	e := &Service{
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      "Test Description",
		ColorCode:        "#FF0000",
		Sort:             1,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		Attributes:       make(map[offeringpb.AttributeKey]*structpb.Value),
	}

	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.Service) error {
		m.ID = 1
		now := time.Now()
		m.CreateTime = &now
		m.UpdateTime = &now
		return nil
	})

	result, err := logic.CreateService(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Test Service Template", result.Name)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(123), result.OrganizationID)
	assert.Equal(t, int64(1), result.CareTypeID)
	assert.Equal(t, int64(2), result.CategoryID)
	assert.Equal(t, "Test Description", result.Description)
	assert.Equal(t, "#FF0000", result.ColorCode)
	assert.Equal(t, int64(1), result.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, result.Source)
	assert.True(t, result.IsActive)
}

func TestLogic_CreateService_WithAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	// Create attributes map
	attributes := make(map[offeringpb.AttributeKey]*structpb.Value)
	attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION] = structpb.NewNumberValue(60)
	attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED] = structpb.NewBoolValue(true)

	e := &Service{
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      "Test Description",
		ColorCode:        "#FF0000",
		Sort:             1,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		Attributes:       attributes,
	}

	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.Service) error {
		m.ID = 1
		now := time.Now()
		m.CreateTime = &now
		m.UpdateTime = &now
		return nil
	})

	// Mock attribute creation calls
	mockAttributeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	result, err := logic.CreateService(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
}

func TestLogic_CreateService_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	e := &Service{Name: "Test Service Template"}

	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("database error"))

	result, err := logic.CreateService(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "database error")
}

func TestLogic_GetService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	now := time.Now()
	mockModel := &model.Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        stringPtr("#FF0000"),
		Sort:             1,
		Images:           stringPtr(`["image1.jpg","image2.jpg"]`),
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(mockModel, nil)
	mockRepo.EXPECT().Get(gomock.Any(), int64(2)).Return(nil, errors.New("not found"))

	// Mock attribute list call
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)

	result, err := logic.GetService(context.Background(), 1)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Test Service Template", result.Name)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, result.OrganizationType)
	assert.Equal(t, int64(123), result.OrganizationID)
	assert.Equal(t, int64(1), result.CareTypeID)
	assert.Equal(t, int64(2), result.CategoryID)
	assert.Equal(t, "Test Description", result.Description)
	assert.Equal(t, "#FF0000", result.ColorCode)
	assert.Equal(t, int64(1), result.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, result.Source)
	assert.True(t, result.IsActive)

	_, err = logic.GetService(context.Background(), 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestLogic_GetService_WithAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	now := time.Now()
	mockModel := &model.Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		ColorCode:        stringPtr("#FF0000"),
		Sort:             1,
		Images:           stringPtr(`["image1.jpg","image2.jpg"]`),
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	// Mock attribute values
	mockAttributeValues := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
		},
		{
			ID:             2,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			AttributeValue: "true",
		},
	}

	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(mockModel, nil)
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return(mockAttributeValues, nil)

	result, err := logic.GetService(context.Background(), 1)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.NotNil(t, result.Attributes)
	assert.Len(t, result.Attributes, 2)
}

func TestLogic_UpdateService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	e := &Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Updated Service Template",
		Description:      "Updated Description",
		ColorCode:        "#00FF00",
		Sort:             2,
		Images:           []string{"updated1.jpg", "updated2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_ENTERPRISE,
		IsActive:         false,
		Attributes:       make(map[offeringpb.AttributeKey]*structpb.Value),
	}

	now := time.Now()
	updatedModel := &model.Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Updated Service Template",
		Description:      stringPtr("Updated Description"),
		ColorCode:        stringPtr("#00FF00"),
		Sort:             2,
		Images:           stringPtr(`["updated1.jpg","updated2.jpg"]`),
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_ENTERPRISE,
		IsActive:         false,
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().Get(gomock.Any(), e.ID).Return(updatedModel, nil)

	// Mock attribute deletion and creation
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)

	result, err := logic.UpdateService(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Updated Service Template", result.Name)
	assert.Equal(t, "Updated Description", result.Description)
	assert.Equal(t, "#00FF00", result.ColorCode)
	assert.Equal(t, int64(2), result.Sort)
	assert.Equal(t, []string{"updated1.jpg", "updated2.jpg"}, result.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_ENTERPRISE, result.Source)
	assert.False(t, result.IsActive)
}

func TestLogic_UpdateService_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	e := &Service{ID: 1, Name: "Updated Service Template"}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

	result, err := logic.UpdateService(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "update error")
}

//func TestLogic_UpdateService_GetError(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//
//	mockRepo := mock.NewMockRepository(ctrl)
//	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
//	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
//	logic := &Logic{
//		repo:                mockRepo,
//		attributeLogic: mockAttributeLogic,
//	}
//
//	e := &Service{ID: 1, Name: "Updated Service Template"}
//
//	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
//	mockRepo.EXPECT().Get(gomock.Any(), e.ID).Return(nil, errors.New("get error"))
//
//	result, err := logic.UpdateService(context.Background(), e)
//
//	assert.Error(t, err)
//	assert.Nil(t, result)
//	assert.Contains(t, err.Error(), "get error")
//}

func TestLogic_DeleteService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	// Mock attribute deletion
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return([]*model.ServiceAttribute{}, nil)
	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)

	err := logic.DeleteService(context.Background(), 1)
	assert.NoError(t, err)
}

//func TestLogic_DeleteService_Error(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//
//	mockRepo := mock.NewMockRepository(ctrl)
//	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
//	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
//	logic := &Logic{
//		repo:                mockRepo,
//		attributeLogic: mockAttributeLogic,
//	}
//
//	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(errors.New("delete error"))
//
//	err := logic.DeleteService(context.Background(), 1)
//	assert.Error(t, err)
//	assert.Contains(t, err.Error(), "delete error")
//}

// TestLogic_CreateService_AttributeError 测试创建服务模板时属性值处理错误的情况
func TestLogic_CreateService_AttributeError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	// 创建属性
	attributes := make(map[offeringpb.AttributeKey]*structpb.Value)
	attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION] = structpb.NewNumberValue(60)

	e := &Service{
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      "Test Description",
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		Attributes:       attributes,
	}

	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.Service) error {
		m.ID = 1
		now := time.Now()
		m.CreateTime = &now
		m.UpdateTime = &now
		return nil
	})

	// 模拟属性创建失败
	mockAttributeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("attribute creation error"))

	result, err := logic.CreateService(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "attribute creation error")
}

// TestLogic_GetService_AttributeError 测试获取服务模板时属性值处理错误的情况
func TestLogic_GetService_AttributeError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	now := time.Now()
	mockModel := &model.Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(mockModel, nil)

	// 模拟属性列表获取失败
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return(nil, errors.New("attribute list error"))

	result, err := logic.GetService(context.Background(), 1)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "attribute list error")
}

// TestLogic_GetService_InvalidAttributeValue 测试处理无效属性值的情况
func TestLogic_GetService_InvalidAttributeValue(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	now := time.Now()
	mockModel := &model.Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Test Service Template",
		Description:      stringPtr("Test Description"),
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	// 模拟属性值，包含一个无效的JSON值
	mockAttributeValues := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "invalid json", // 这不是一个有效的数字
		},
	}

	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(mockModel, nil)
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return(mockAttributeValues, nil)

	// 即使有无效值，GetService 也应该成功，只是跳过无效属性
	result, err := logic.GetService(context.Background(), 1)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.NotNil(t, result.Attributes)
}

// TestLogic_UpdateService_WithAttributes 测试更新带属性的服务模板
func TestLogic_UpdateService_WithAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	// 创建属性
	attributes := make(map[offeringpb.AttributeKey]*structpb.Value)
	attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION] = structpb.NewNumberValue(90)
	attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED] = structpb.NewBoolValue(false)

	e := &Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Updated Service Template",
		Description:      "Updated Description",
		ColorCode:        "#00FF00",
		Sort:             2,
		Images:           []string{"updated1.jpg", "updated2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_ENTERPRISE,
		IsActive:         false,
		Attributes:       attributes,
	}

	now := time.Now()
	updatedModel := &model.Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       1,
		CategoryID:       2,
		Name:             "Updated Service Template",
		Description:      stringPtr("Updated Description"),
		ColorCode:        stringPtr("#00FF00"),
		Sort:             2,
		Images:           stringPtr(`["updated1.jpg","updated2.jpg"]`),
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_ENTERPRISE,
		IsActive:         false,
		CreateTime:       &now,
		UpdateTime:       &now,
	}

	// 模拟现有属性
	existingAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
		},
	}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().Get(gomock.Any(), e.ID).Return(updatedModel, nil)

	// 模拟属性处理
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return(existingAttributes, nil)
	mockAttributeRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)
	mockAttributeRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	result, err := logic.UpdateService(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Updated Service Template", result.Name)
}

// TestLogic_UpdateService_AttributeDeletionError 测试更新服务模板时属性删除失败的情况
func TestLogic_UpdateService_AttributeDeletionError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	e := &Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		Name:             "Updated Service Template",
		Attributes:       make(map[offeringpb.AttributeKey]*structpb.Value),
	}

	// 模拟现有属性
	existingAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
		},
	}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	// 模拟属性列表获取成功但删除失败
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return(existingAttributes, nil)
	mockAttributeRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(errors.New("attribute deletion error"))

	result, err := logic.UpdateService(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "attribute deletion error")
}

// TestLogic_DeleteService_AttributeListError 测试删除服务模板时属性列表获取失败的情况
func TestLogic_DeleteService_AttributeListError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	// 模拟属性列表获取失败
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return(nil, errors.New("attribute list error"))

	err := logic.DeleteService(context.Background(), 1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "attribute list error")
}

// TestLogic_DeleteService_WithAttributes 测试删除带属性的服务模板
func TestLogic_DeleteService_WithAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	// 模拟现有属性
	existingAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
		},
		{
			ID:             2,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			AttributeValue: "true",
		},
	}

	// 模拟属性列表获取成功
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return(existingAttributes, nil)

	// 模拟属性删除
	mockAttributeRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)
	mockAttributeRepo.EXPECT().Delete(gomock.Any(), int64(2)).Return(nil)

	// 模拟服务模板删除
	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)

	err := logic.DeleteService(context.Background(), 1)
	assert.NoError(t, err)
}

// TestLogic_DeleteService_AttributeDeletionError 测试删除服务模板时属性删除失败的情况
func TestLogic_DeleteService_AttributeDeletionError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	mockAttributeRepo := attributemocks.NewMockRepository(ctrl)
	mockAttributeLogic := attribute.NewWithRepository(mockAttributeRepo)
	logic := &Logic{
		repo:           mockRepo,
		attributeLogic: mockAttributeLogic,
	}

	// 模拟现有属性
	existingAttributes := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
		},
	}

	// 模拟属性列表获取成功但删除失败
	mockAttributeRepo.EXPECT().List(gomock.Any(), int64(1)).Return(existingAttributes, nil)
	mockAttributeRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(errors.New("attribute deletion error"))

	err := logic.DeleteService(context.Background(), 1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "attribute deletion error")
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
