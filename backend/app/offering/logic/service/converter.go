package service

import (
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const (
	emtpyArray = "[]"
)

// ProtoToEntity converts a protobuf Service to a domain entity.
func ProtoToEntity(pbService *offeringpb.Service) *Service {
	if pbService == nil {
		return nil
	}

	// Convert string key map to AttributeKey map
	attributes := make(map[offeringpb.AttributeKey]*structpb.Value)
	for keyStr, value := range pbService.Attributes {
		attributeKey, err := parseAttributeKey(keyStr)
		if err != nil {
			// Skip invalid keys
			continue
		}
		attributes[attributeKey] = value
	}

	return &Service{
		ID:               pbService.Id,
		OrganizationType: pbService.OrganizationType,
		OrganizationID:   pbService.OrganizationId,
		CareTypeID:       pbService.CareTypeId,
		CategoryID:       pbService.CategoryId,
		Name:             pbService.Name,
		Description:      pbService.Description,
		ColorCode:        pbService.ColorCode,
		Sort:             pbService.Sort,
		Images:           pbService.Images,
		Source:           pbService.Source,
		IsActive:         pbService.IsActive,
		Attributes:       attributes,
	}
}

// EntityToProto converts a domain entity Service to a protobuf message.
func EntityToProto(e *Service) *offeringpb.Service {
	if e == nil {
		return nil
	}

	// Convert AttributeKey map to string key map
	attributes := make(map[string]*structpb.Value)
	for key, value := range e.Attributes {
		attributes[key.String()] = value
	}

	pbService := &offeringpb.Service{
		Id:               e.ID,
		OrganizationType: e.OrganizationType,
		OrganizationId:   e.OrganizationID,
		CareTypeId:       e.CareTypeID,
		CategoryId:       e.CategoryID,
		Name:             e.Name,
		Description:      e.Description,
		ColorCode:        e.ColorCode,
		Sort:             e.Sort,
		Images:           e.Images,
		Source:           e.Source,
		IsActive:         e.IsActive,
		CreateTime:       timestamppb.New(e.CreateTime),
		UpdateTime:       timestamppb.New(e.UpdateTime),
		Attributes:       attributes,
	}
	if e.DeleteTime != nil {
		pbService.DeleteTime = timestamppb.New(*e.DeleteTime)
	}
	return pbService
}

// parseAttributeKey converts string to AttributeKey enum
func parseAttributeKey(key string) (offeringpb.AttributeKey, error) {
	switch key {
	case "ATTRIBUTE_KEY_DURATION":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION, nil
	case "ATTRIBUTE_KEY_MAX_DURATION":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_MAX_DURATION, nil
	case "ATTRIBUTE_KEY_IS_LODGING_REQUIRED":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED, nil
	case "ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED, nil
	case "ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED, nil
	case "ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID, nil
	case "ATTRIBUTE_KEY_SESSION_COUNT":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_SESSION_COUNT, nil
	case "ATTRIBUTE_KEY_SESSION_DURATION_MIN":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_SESSION_DURATION_MIN, nil
	case "ATTRIBUTE_KEY_CLASS_CAPACITY":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_CLASS_CAPACITY, nil
	case "ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED, nil
	case "ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS, nil
	case "ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN, nil
	case "ATTRIBUTE_KEY_IS_RESETTABLE":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_RESETTABLE, nil
	case "ATTRIBUTE_KEY_RESET_INTERVAL_DAYS":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_RESET_INTERVAL_DAYS, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER, nil
	default:
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED, fmt.Errorf("unknown attribute key: %s", key)
	}
}

// EntityToModel converts a domain entity Service to a database model.
func EntityToModel(e *Service) *model.Service {
	if e == nil {
		return nil
	}

	imagesJSON := emtpyArray
	if len(e.Images) > 0 {
		if jsonBytes, err := json.Marshal(e.Images); err == nil {
			imagesJSON = string(jsonBytes)
		}
	}

	m := &model.Service{
		ID:               e.ID,
		OrganizationType: e.OrganizationType,
		OrganizationID:   e.OrganizationID,
		CareTypeID:       e.CareTypeID,
		CategoryID:       e.CategoryID,
		Name:             e.Name,
		Sort:             e.Sort,
		Source:           e.Source,
		IsActive:         e.IsActive,
	}

	if e.Description != "" {
		m.Description = &e.Description
	}
	if e.ColorCode != "" {
		m.ColorCode = &e.ColorCode
	}
	if imagesJSON != emtpyArray {
		m.Images = &imagesJSON
	}

	return m
}

// ModelToEntity converts a database model Service to a domain entity.
func ModelToEntity(m *model.Service) *Service {
	if m == nil {
		return nil
	}

	e := &Service{
		ID:               m.ID,
		OrganizationType: m.OrganizationType,
		OrganizationID:   m.OrganizationID,
		CareTypeID:       m.CareTypeID,
		CategoryID:       m.CategoryID,
		Name:             m.Name,
		Sort:             m.Sort,
		Source:           m.Source,
		IsActive:         m.IsActive,
	}

	if m.Description != nil {
		e.Description = *m.Description
	}
	if m.ColorCode != nil {
		e.ColorCode = *m.ColorCode
	}
	if m.Images != nil {
		var images []string
		if err := json.Unmarshal([]byte(*m.Images), &images); err == nil {
			e.Images = images
		}
	}
	if m.CreateTime != nil {
		e.CreateTime = *m.CreateTime
	}
	if m.UpdateTime != nil {
		e.UpdateTime = *m.UpdateTime
	}
	if m.DeleteTime != nil {
		e.DeleteTime = m.DeleteTime
	}

	return e
}

// ModelToEntityList converts a list of database models to a list of domain entities.
func ModelToEntityList(ms []*model.Service) []*Service {
	var es []*Service
	for _, m := range ms {
		es = append(es, ModelToEntity(m))
	}
	return es
}

// EntityToProtoList converts a list of domain entities to a list of protobuf messages.
func EntityToProtoList(es []*Service) []*offeringpb.Service {
	var pbs []*offeringpb.Service
	for _, e := range es {
		pbs = append(pbs, EntityToProto(e))
	}
	return pbs
}
