package service

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestProtoToEntity(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	pbTimestamp := timestamppb.New(now)

	// 创建属性
	attributes := make(map[string]*structpb.Value)
	attributes["ATTRIBUTE_KEY_DURATION"] = structpb.NewNumberValue(60)
	attributes["ATTRIBUTE_KEY_IS_LODGING_REQUIRED"] = structpb.NewBoolValue(true)
	attributes["INVALID_KEY"] = structpb.NewStringValue("should be skipped")

	pbService := &offeringpb.Service{
		Id:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationId:   123,
		CareTypeId:       456,
		CategoryId:       789,
		Name:             "Test Service",
		Description:      "Test Description",
		ColorCode:        "#FF0000",
		Sort:             10,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       pbTimestamp,
		UpdateTime:       pbTimestamp,
		Attributes:       attributes,
	}

	entity := ProtoToEntity(pbService)

	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, entity.OrganizationType)
	assert.Equal(t, int64(123), entity.OrganizationID)
	assert.Equal(t, int64(456), entity.CareTypeID)
	assert.Equal(t, int64(789), entity.CategoryID)
	assert.Equal(t, "Test Service", entity.Name)
	assert.Equal(t, "Test Description", entity.Description)
	assert.Equal(t, "#FF0000", entity.ColorCode)
	assert.Equal(t, int64(10), entity.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, entity.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, entity.Source)
	assert.True(t, entity.IsActive)

	// 验证属性转换
	assert.Len(t, entity.Attributes, 2) // 无效键应被跳过
	assert.Contains(t, entity.Attributes, offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION)
	assert.Contains(t, entity.Attributes, offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED)
	assert.Equal(t, float64(60), entity.Attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION].GetNumberValue())
	assert.True(t, entity.Attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED].GetBoolValue())

	// 测试 nil 输入
	entity = ProtoToEntity(nil)
	assert.Nil(t, entity)
}

func TestEntityToProto(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	deleteTime := now.Add(time.Hour)

	// 创建属性
	attributes := make(map[offeringpb.AttributeKey]*structpb.Value)
	attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION] = structpb.NewNumberValue(60)
	attributes[offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED] = structpb.NewBoolValue(true)

	entity := &Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      "Test Description",
		ColorCode:        "#FF0000",
		Sort:             10,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       now,
		UpdateTime:       now,
		DeleteTime:       &deleteTime,
		Attributes:       attributes,
	}

	pbService := EntityToProto(entity)

	assert.NotNil(t, pbService)
	assert.Equal(t, int64(1), pbService.Id)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, pbService.OrganizationType)
	assert.Equal(t, int64(123), pbService.OrganizationId)
	assert.Equal(t, int64(456), pbService.CareTypeId)
	assert.Equal(t, int64(789), pbService.CategoryId)
	assert.Equal(t, "Test Service", pbService.Name)
	assert.Equal(t, "Test Description", pbService.Description)
	assert.Equal(t, "#FF0000", pbService.ColorCode)
	assert.Equal(t, int64(10), pbService.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, pbService.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, pbService.Source)
	assert.True(t, pbService.IsActive)
	assert.NotNil(t, pbService.CreateTime)
	assert.NotNil(t, pbService.UpdateTime)
	assert.NotNil(t, pbService.DeleteTime)

	// 验证属性转换
	assert.Len(t, pbService.Attributes, 2)
	assert.Contains(t, pbService.Attributes, "ATTRIBUTE_KEY_DURATION")
	assert.Contains(t, pbService.Attributes, "ATTRIBUTE_KEY_IS_LODGING_REQUIRED")
	assert.Equal(t, float64(60), pbService.Attributes["ATTRIBUTE_KEY_DURATION"].GetNumberValue())
	assert.True(t, pbService.Attributes["ATTRIBUTE_KEY_IS_LODGING_REQUIRED"].GetBoolValue())

	// 测试 nil 输入
	pbService = EntityToProto(nil)
	assert.Nil(t, pbService)

	// 测试没有DeleteTime
	entity.DeleteTime = nil
	pbService = EntityToProto(entity)
	assert.NotNil(t, pbService)
	assert.Nil(t, pbService.DeleteTime)
}

func TestParseAttributeKey(t *testing.T) {
	testCases := []struct {
		name          string
		key           string
		expectedKey   offeringpb.AttributeKey
		expectedError bool
	}{
		{
			name:          "Valid key - DURATION",
			key:           "ATTRIBUTE_KEY_DURATION",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			expectedError: false,
		},
		{
			name:          "Valid key - IS_LODGING_REQUIRED",
			key:           "ATTRIBUTE_KEY_IS_LODGING_REQUIRED",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			expectedError: false,
		},
		{
			name:          "Valid key - SESSION_COUNT",
			key:           "ATTRIBUTE_KEY_SESSION_COUNT",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_SESSION_COUNT,
			expectedError: false,
		},
		{
			name:          "Invalid key",
			key:           "INVALID_KEY",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED,
			expectedError: true,
		},
		{
			name:          "Empty key",
			key:           "",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED,
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			key, err := parseAttributeKey(tc.key)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tc.expectedKey, key)
		})
	}
}

func TestEntityToModel(t *testing.T) {
	// 测试正常转换
	entity := &Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      "Test Description",
		ColorCode:        "#FF0000",
		Sort:             10,
		Images:           []string{"image1.jpg", "image2.jpg"},
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
	}

	m := EntityToModel(entity)

	assert.NotNil(t, m)
	assert.Equal(t, int64(1), m.ID)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, m.OrganizationType)
	assert.Equal(t, int64(123), m.OrganizationID)
	assert.Equal(t, int64(456), m.CareTypeID)
	assert.Equal(t, int64(789), m.CategoryID)
	assert.Equal(t, "Test Service", m.Name)
	assert.Equal(t, "Test Description", *m.Description)
	assert.Equal(t, "#FF0000", *m.ColorCode)
	assert.Equal(t, int64(10), m.Sort)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, m.Source)
	assert.True(t, m.IsActive)

	// 验证Images JSON转换
	var images []string
	err := json.Unmarshal([]byte(*m.Images), &images)
	assert.NoError(t, err)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, images)

	// 测试空字段
	entity = &Service{
		ID:   1,
		Name: "Test Service",
	}
	m = EntityToModel(entity)
	assert.NotNil(t, m)
	assert.Equal(t, int64(1), m.ID)
	assert.Equal(t, "Test Service", m.Name)
	assert.Nil(t, m.Description)
	assert.Nil(t, m.ColorCode)
	assert.Nil(t, m.Images)

	// 测试空Images
	entity = &Service{
		ID:     1,
		Name:   "Test Service",
		Images: []string{},
	}
	m = EntityToModel(entity)
	assert.NotNil(t, m)
	assert.Nil(t, m.Images)

	// 测试 nil 输入
	m = EntityToModel(nil)
	assert.Nil(t, m)
}

func TestModelToEntity(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	deleteTime := now.Add(time.Hour)
	imagesJSON := `["image1.jpg","image2.jpg"]`

	m := &model.Service{
		ID:               1,
		OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
		OrganizationID:   123,
		CareTypeID:       456,
		CategoryID:       789,
		Name:             "Test Service",
		Description:      lo.ToPtr("Test Description"),
		ColorCode:        lo.ToPtr("#FF0000"),
		Sort:             10,
		Images:           &imagesJSON,
		Source:           offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM,
		IsActive:         true,
		CreateTime:       &now,
		UpdateTime:       &now,
		DeleteTime:       &deleteTime,
	}

	entity := ModelToEntity(m)

	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY, entity.OrganizationType)
	assert.Equal(t, int64(123), entity.OrganizationID)
	assert.Equal(t, int64(456), entity.CareTypeID)
	assert.Equal(t, int64(789), entity.CategoryID)
	assert.Equal(t, "Test Service", entity.Name)
	assert.Equal(t, "Test Description", entity.Description)
	assert.Equal(t, "#FF0000", entity.ColorCode)
	assert.Equal(t, int64(10), entity.Sort)
	assert.Equal(t, []string{"image1.jpg", "image2.jpg"}, entity.Images)
	assert.Equal(t, offeringpb.ServiceSource_SERVICE_SOURCE_PLATFORM, entity.Source)
	assert.True(t, entity.IsActive)
	assert.Equal(t, now, entity.CreateTime)
	assert.Equal(t, now, entity.UpdateTime)
	assert.Equal(t, &deleteTime, entity.DeleteTime)

	// 测试空字段
	m = &model.Service{
		ID:   1,
		Name: "Test Service",
	}
	entity = ModelToEntity(m)
	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, "Test Service", entity.Name)
	assert.Empty(t, entity.Description)
	assert.Empty(t, entity.ColorCode)
	assert.Empty(t, entity.Images)
	assert.Equal(t, time.Time{}, entity.CreateTime)
	assert.Equal(t, time.Time{}, entity.UpdateTime)
	assert.Nil(t, entity.DeleteTime)

	// 测试无效的Images JSON
	invalidJSON := `invalid json`
	m = &model.Service{
		ID:     1,
		Name:   "Test Service",
		Images: &invalidJSON,
	}
	entity = ModelToEntity(m)
	assert.NotNil(t, entity)
	assert.Empty(t, entity.Images)

	// 测试 nil 输入
	entity = ModelToEntity(nil)
	assert.Nil(t, entity)
}

func TestModelToEntityList(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	imagesJSON := `["image1.jpg"]`

	models := []*model.Service{
		{
			ID:               1,
			OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
			OrganizationID:   123,
			Name:             "Service 1",
			Images:           &imagesJSON,
			CreateTime:       &now,
			UpdateTime:       &now,
		},
		{
			ID:               2,
			OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
			OrganizationID:   456,
			Name:             "Service 2",
			CreateTime:       &now,
			UpdateTime:       &now,
		},
	}

	entities := ModelToEntityList(models)

	assert.NotNil(t, entities)
	assert.Len(t, entities, 2)
	assert.Equal(t, int64(1), entities[0].ID)
	assert.Equal(t, "Service 1", entities[0].Name)
	assert.Equal(t, []string{"image1.jpg"}, entities[0].Images)
	assert.Equal(t, int64(2), entities[1].ID)
	assert.Equal(t, "Service 2", entities[1].Name)
	assert.Empty(t, entities[1].Images)

	// 测试空列表
	entities = ModelToEntityList([]*model.Service{})
	assert.Empty(t, entities)

	// 测试 nil 列表
	entities = ModelToEntityList(nil)
	assert.Empty(t, entities)
}

func TestEntityToProtoList(t *testing.T) {
	// 测试正常转换
	now := time.Now()

	entities := []*Service{
		{
			ID:               1,
			OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
			OrganizationID:   123,
			Name:             "Service 1",
			Images:           []string{"image1.jpg"},
			CreateTime:       now,
			UpdateTime:       now,
		},
		{
			ID:               2,
			OrganizationType: offeringpb.OrganizationType_ORGANIZATION_TYPE_COMPANY,
			OrganizationID:   456,
			Name:             "Service 2",
			CreateTime:       now,
			UpdateTime:       now,
		},
	}

	protos := EntityToProtoList(entities)

	assert.NotNil(t, protos)
	assert.Len(t, protos, 2)
	assert.Equal(t, int64(1), protos[0].Id)
	assert.Equal(t, "Service 1", protos[0].Name)
	assert.Equal(t, []string{"image1.jpg"}, protos[0].Images)
	assert.Equal(t, int64(2), protos[1].Id)
	assert.Equal(t, "Service 2", protos[1].Name)
	assert.Empty(t, protos[1].Images)

	// 测试空列表
	protos = EntityToProtoList([]*Service{})
	assert.Empty(t, protos)

	// 测试 nil 列表
	protos = EntityToProtoList(nil)
	assert.Empty(t, protos)
}
