package service

import (
	"context"
	"strconv"

	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func New() *Logic {
	return &Logic{
		repo:           service.NewRepository(),
		attributeLogic: attribute.New(),
	}
}

type Logic struct {
	repo           service.Repository
	attributeLogic *attribute.Logic
}

// CreateService creates a new service.
func (l *Logic) CreateService(ctx context.Context, e *Service) (*Service, error) {
	m := EntityToModel(e)
	err := l.repo.Create(ctx, m)
	if err != nil {
		return nil, err
	}

	// Create attribute values if they exist
	if len(e.Attributes) > 0 {
		// Convert AttributeKey map to string map for CreateServiceAttributeValues
		attributesStr := make(map[string]*structpb.Value)
		for key, value := range e.Attributes {
			attributesStr[key.String()] = value
		}
		err = l.attributeLogic.CreateAttributes(ctx, m.ID, attributesStr)
		if err != nil {
			return nil, err
		}
	}

	// The model `m` is updated with the ID and timestamps after creation.
	return ModelToEntity(m), nil
}

// GetService gets a service by ID.
func (l *Logic) GetService(ctx context.Context, id int64) (*Service, error) {
	m, err := l.repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	entity := ModelToEntity(m)

	// Load attribute values
	attributeValues, err := l.attributeLogic.ListAttributes(ctx, id)
	if err != nil {
		return nil, err
	}

	// Convert attribute values to map
	attributes := make(map[offeringpb.AttributeKey]*structpb.Value)
	for _, attrValue := range attributeValues {
		value, err := stringToValue(attrValue.AttributeValue)
		if err != nil {
			return nil, err
		}
		attributes[attrValue.AttributeKey] = value
	}

	entity.Attributes = attributes
	return entity, nil
}

// UpdateService updates a service.
func (l *Logic) UpdateService(ctx context.Context, e *Service) (*Service, error) {
	m := EntityToModel(e)
	err := l.repo.Update(ctx, m)
	if err != nil {
		return nil, err
	}

	// Delete existing attribute values
	err = l.attributeLogic.DeleteAttributesByServiceID(ctx, m.ID)
	if err != nil {
		return nil, err
	}

	// Create new attribute values if they exist
	if len(e.Attributes) > 0 {
		// Convert AttributeKey map to string map for CreateServiceAttributeValues
		attributesStr := make(map[string]*structpb.Value)
		for key, value := range e.Attributes {
			attributesStr[key.String()] = value
		}
		err = l.attributeLogic.CreateAttributes(ctx, m.ID, attributesStr)
		if err != nil {
			return nil, err
		}
	}

	// After update, we should get the latest version from the DB.
	updatedModel, err := l.repo.Get(ctx, m.ID)
	if err != nil {
		return nil, err
	}
	return ModelToEntity(updatedModel), nil
}

// DeleteService deletes a service.
func (l *Logic) DeleteService(ctx context.Context, id int64) error {
	// Delete attribute values first
	err := l.attributeLogic.DeleteAttributesByServiceID(ctx, id)
	if err != nil {
		return err
	}

	// Then delete the service
	return l.repo.Delete(ctx, id)
}

// stringToValue converts string back to structpb.Value
func stringToValue(valueStr string) (*structpb.Value, error) {
	if valueStr == "" {
		return structpb.NewNullValue(), nil
	}

	// Try to parse as number first
	if num, err := strconv.ParseFloat(valueStr, 64); err == nil {
		return structpb.NewNumberValue(num), nil
	}

	// Try to parse as boolean
	if b, err := strconv.ParseBool(valueStr); err == nil {
		return structpb.NewBoolValue(b), nil
	}

	// Default to string
	return structpb.NewStringValue(valueStr), nil
}
