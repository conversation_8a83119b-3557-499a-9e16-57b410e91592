package service

import (
	"time"

	"google.golang.org/protobuf/types/known/structpb"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// Service represents a service entity
type Service struct {
	ID               int64
	OrganizationType offeringpb.OrganizationType
	OrganizationID   int64
	CareTypeID       int64
	CategoryID       int64
	Name             string
	Description      string
	ColorCode        string
	Sort             int64
	Images           []string
	Source           offeringpb.ServiceSource
	IsActive         bool
	CreateTime       time.Time
	UpdateTime       time.Time
	DeleteTime       *time.Time
	Attributes       map[offeringpb.AttributeKey]*structpb.Value
}
