load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = [
        "converter.go",
        "entity.go",
        "service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/logic/service/attribute",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/service",
        "//backend/proto/offering/v1:offering",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "service_test",
    srcs = [
        "converter_test.go",
        "service_test.go",
    ],
    embed = [":service"],
    deps = [
        "//backend/app/offering/logic/service/attribute",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/service/mocks",
        "//backend/app/offering/repo/serviceattribute/mocks",
        "//backend/proto/offering/v1:offering",
        "@com_github_samber_lo//:lo",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
