package attribute

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/structpb"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestParseAttributeKey(t *testing.T) {
	testCases := []struct {
		name          string
		key           string
		expectedKey   offeringpb.AttributeKey
		expectedError bool
	}{
		{
			name:          "Valid key - DURATION",
			key:           "ATTRIBUTE_KEY_DURATION",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			expectedError: false,
		},
		{
			name:          "Valid key - IS_LODGING_REQUIRED",
			key:           "ATTRIBUTE_KEY_IS_LODGING_REQUIRED",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			expectedError: false,
		},
		{
			name:          "Valid key - SESSION_COUNT",
			key:           "ATTRIBUTE_KEY_SESSION_COUNT",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_SESSION_COUNT,
			expectedError: false,
		},
		{
			name:          "Invalid key",
			key:           "INVALID_KEY",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED,
			expectedError: true,
		},
		{
			name:          "Empty key",
			key:           "",
			expectedKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED,
			expectedError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			key, err := parseAttributeKey(tc.key)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tc.expectedKey, key)
		})
	}
}

func TestValueToString(t *testing.T) {
	testCases := []struct {
		name           string
		value          *structpb.Value
		expectedString string
		expectedError  bool
	}{
		{
			name:           "Null value",
			value:          structpb.NewNullValue(),
			expectedString: "",
			expectedError:  false,
		},
		{
			name:           "Number value - integer",
			value:          structpb.NewNumberValue(42),
			expectedString: "42",
			expectedError:  false,
		},
		{
			name:           "Number value - float",
			value:          structpb.NewNumberValue(3.14),
			expectedString: "3.14",
			expectedError:  false,
		},
		{
			name:           "String value",
			value:          structpb.NewStringValue("test string"),
			expectedString: "test string",
			expectedError:  false,
		},
		{
			name:           "Boolean value - true",
			value:          structpb.NewBoolValue(true),
			expectedString: "true",
			expectedError:  false,
		},
		{
			name:           "Boolean value - false",
			value:          structpb.NewBoolValue(false),
			expectedString: "false",
			expectedError:  false,
		},
		{
			name:           "Nil value",
			value:          nil,
			expectedString: "",
			expectedError:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			str, err := valueToString(tc.value)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tc.expectedString, str)
		})
	}
}

func TestValueToString_ComplexTypes(t *testing.T) {
	// 测试结构体值
	structValue, err := structpb.NewStruct(map[string]interface{}{
		"name": "John",
		"age":  30,
	})
	assert.NoError(t, err)

	structString, err := valueToString(structpb.NewStructValue(structValue))
	assert.NoError(t, err)
	assert.Contains(t, structString, "name")
	assert.Contains(t, structString, "John")
	assert.Contains(t, structString, "age")
	assert.Contains(t, structString, "30")

	// 测试列表值
	listValue, err := structpb.NewList([]interface{}{1, "two", true})
	assert.NoError(t, err)

	listString, err := valueToString(structpb.NewListValue(listValue))
	assert.NoError(t, err)
	assert.Contains(t, listString, "1")
	assert.Contains(t, listString, "two")
	assert.Contains(t, listString, "true")
}
