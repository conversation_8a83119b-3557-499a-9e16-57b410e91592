package attribute

import (
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
)

// EntityToModel converts a domain entity ServiceAttribute to a database model.
func EntityToModel(e *ServiceAttribute) *model.ServiceAttribute {
	if e == nil {
		return nil
	}

	m := &model.ServiceAttribute{
		ID:             e.ID,
		ServiceID:      e.ServiceID,
		AttributeKey:   e.AttributeKey,
		AttributeValue: e.AttributeValue,
	}

	return m
}

// ModelToEntity converts a database model ServiceAttribute to a domain entity.
func ModelToEntity(m *model.ServiceAttribute) *ServiceAttribute {
	if m == nil {
		return nil
	}

	e := &ServiceAttribute{
		ID:             m.ID,
		ServiceID:      m.ServiceID,
		AttributeKey:   m.AttributeKey,
		AttributeValue: m.AttributeValue,
	}

	if m.CreateTime != nil {
		e.CreateTime = *m.CreateTime
	}
	if m.UpdateTime != nil {
		e.UpdateTime = *m.UpdateTime
	}
	if m.DeleteTime != nil {
		e.DeleteTime = m.DeleteTime
	}

	return e
}

// ModelToEntityList converts a list of database models to a list of domain entities.
func ModelToEntityList(ms []*model.ServiceAttribute) []*ServiceAttribute {
	var es []*ServiceAttribute
	for _, m := range ms {
		es = append(es, ModelToEntity(m))
	}
	return es
}
