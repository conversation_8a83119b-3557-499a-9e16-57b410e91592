package attribute

import (
	"context"
	"fmt"
	"strconv"

	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/serviceattribute"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func New() *Logic {
	return &Logic{
		repo: serviceattribute.NewRepository(),
	}
}

// NewWithRepository creates a new Logic with a custom repository (useful for testing)
func NewWithRepository(repo serviceattribute.Repository) *Logic {
	return &Logic{
		repo: repo,
	}
}

type Logic struct {
	repo serviceattribute.Repository
}

// CreateAttribute creates a new service attribute value.
func (l *Logic) CreateAttribute(
	ctx context.Context, e *ServiceAttribute) (*ServiceAttribute, error) {
	m := EntityToModel(e)
	err := l.repo.Create(ctx, m)
	if err != nil {
		return nil, err
	}
	// The model `m` is updated with the ID and timestamps after creation.
	return ModelToEntity(m), nil
}

// CreateAttributes creates multiple service attribute values.
func (l *Logic) CreateAttributes(
	ctx context.Context, serviceID int64, attributes map[string]*structpb.Value) error {
	if len(attributes) == 0 {
		return nil
	}

	var models []*model.ServiceAttribute
	for key, value := range attributes {
		// Convert AttributeKey string to enum
		attributeKey, err := parseAttributeKey(key)
		if err != nil {
			return err
		}

		// Convert structpb.Value to string
		attributeValue, err := valueToString(value)
		if err != nil {
			return err
		}

		m := &model.ServiceAttribute{
			ServiceID:      serviceID,
			AttributeKey:   attributeKey,
			AttributeValue: attributeValue,
		}
		models = append(models, m)
	}

	// Create each attribute value individually since CreateInBatches is not available
	for _, m := range models {
		err := l.repo.Create(ctx, m)
		if err != nil {
			return err
		}
	}
	return nil
}

// parseAttributeKey converts string to AttributeKey enum
func parseAttributeKey(key string) (offeringpb.AttributeKey, error) {
	switch key {
	case "ATTRIBUTE_KEY_DURATION":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION, nil
	case "ATTRIBUTE_KEY_MAX_DURATION":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_MAX_DURATION, nil
	case "ATTRIBUTE_KEY_IS_LODGING_REQUIRED":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED, nil
	case "ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_EVALUATION_REQUIRED, nil
	case "ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_EVALUATION_FOR_OB_REQUIRED, nil
	case "ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_REQUIRED_EVALUATION_ID, nil
	case "ATTRIBUTE_KEY_SESSION_COUNT":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_SESSION_COUNT, nil
	case "ATTRIBUTE_KEY_SESSION_DURATION_MIN":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_SESSION_DURATION_MIN, nil
	case "ATTRIBUTE_KEY_CLASS_CAPACITY":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_CLASS_CAPACITY, nil
	case "ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_PREREQUISITE_REQUIRED, nil
	case "ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_PREREQUISITE_SERVICE_IDS, nil
	case "ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_ALLOW_STAFF_AUTO_ASSIGN, nil
	case "ATTRIBUTE_KEY_IS_RESETTABLE":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_RESETTABLE, nil
	case "ATTRIBUTE_KEY_RESET_INTERVAL_DAYS":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_RESET_INTERVAL_DAYS, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_TYPE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_COAT_TYPE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_SIZE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_PET_CODE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_STAFF_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_LODGING_TYPE_BINDING, nil
	case "ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER":
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_SUPPORT_AUTO_ROLLOVER, nil
	default:
		return offeringpb.AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED, fmt.Errorf("unknown attribute key: %s", key)
	}
}

// valueToString converts structpb.Value to string
func valueToString(value *structpb.Value) (string, error) {
	if value == nil {
		return "", nil
	}

	switch value.Kind.(type) {
	case *structpb.Value_NullValue:
		return "", nil
	case *structpb.Value_NumberValue:
		return strconv.FormatFloat(value.GetNumberValue(), 'f', -1, 64), nil
	case *structpb.Value_StringValue:
		return value.GetStringValue(), nil
	case *structpb.Value_BoolValue:
		return strconv.FormatBool(value.GetBoolValue()), nil
	case *structpb.Value_StructValue:
		// For complex objects, we can serialize to JSON
		return value.GetStructValue().String(), nil
	case *structpb.Value_ListValue:
		// For arrays, we can serialize to JSON
		return value.GetListValue().String(), nil
	default:
		return "", fmt.Errorf("unsupported value type")
	}
}

// GetAttribute gets a service attribute value by ID.
func (l *Logic) GetAttribute(
	ctx context.Context, id int64) (*ServiceAttribute, error) {
	m, err := l.repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return ModelToEntity(m), nil
}

// ListAttributes lists service attribute values by service template ID.
func (l *Logic) ListAttributes(
	ctx context.Context, serviceID int64) ([]*ServiceAttribute, error) {
	ms, err := l.repo.List(ctx, serviceID)
	if err != nil {
		return nil, err
	}
	return ModelToEntityList(ms), nil
}

// UpdateServiceAttribute updates a service attribute value.
func (l *Logic) UpdateServiceAttribute(
	ctx context.Context, e *ServiceAttribute) (*ServiceAttribute, error) {
	m := EntityToModel(e)
	err := l.repo.Update(ctx, m)
	if err != nil {
		return nil, err
	}
	// After update, we should get the latest version from the DB.
	updatedModel, err := l.repo.Get(ctx, m.ID)
	if err != nil {
		return nil, err
	}
	return ModelToEntity(updatedModel), nil
}

// DeleteAttribute deletes a service attribute value.
func (l *Logic) DeleteAttribute(ctx context.Context, id int64) error {
	return l.repo.Delete(ctx, id)
}

// DeleteAttributesByServiceID deletes all service attribute values for a service template.
func (l *Logic) DeleteAttributesByServiceID(ctx context.Context, serviceID int64) error {
	// First get all attribute values for this template
	attributeValues, err := l.ListAttributes(ctx, serviceID)
	if err != nil {
		return err
	}

	// Delete each attribute value
	for _, attrValue := range attributeValues {
		err := l.DeleteAttribute(ctx, attrValue.ID)
		if err != nil {
			return err
		}
	}

	return nil
}
