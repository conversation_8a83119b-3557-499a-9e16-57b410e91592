package attribute

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestEntityToModel(t *testing.T) {
	// 测试正常转换
	entity := &ServiceAttribute{
		ID:             1,
		ServiceID:      100,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "60",
	}

	model := EntityToModel(entity)

	assert.NotNil(t, model)
	assert.Equal(t, int64(1), model.ID)
	assert.Equal(t, int64(100), model.ServiceID)
	assert.Equal(t, offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION, model.AttributeKey)
	assert.Equal(t, "60", model.AttributeValue)

	// 测试 nil 输入
	model = EntityToModel(nil)
	assert.Nil(t, model)
}

func TestModelToEntity(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	deleteTime := now.Add(time.Hour)
	m := &model.ServiceAttribute{
		ID:             1,
		ServiceID:      100,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "60",
		CreateTime:     &now,
		UpdateTime:     &now,
		DeleteTime:     &deleteTime,
	}

	entity := ModelToEntity(m)

	assert.NotNil(t, entity)
	assert.Equal(t, int64(1), entity.ID)
	assert.Equal(t, int64(100), entity.ServiceID)
	assert.Equal(t, offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION, entity.AttributeKey)
	assert.Equal(t, "60", entity.AttributeValue)
	assert.Equal(t, now, entity.CreateTime)
	assert.Equal(t, now, entity.UpdateTime)
	assert.Equal(t, &deleteTime, entity.DeleteTime)

	// 测试空字段
	m = &model.ServiceAttribute{
		ID:             1,
		ServiceID:      100,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "60",
	}
	entity = ModelToEntity(m)
	assert.NotNil(t, entity)
	assert.Equal(t, time.Time{}, entity.CreateTime)
	assert.Equal(t, time.Time{}, entity.UpdateTime)
	assert.Nil(t, entity.DeleteTime)

	// 测试 nil 输入
	entity = ModelToEntity(nil)
	assert.Nil(t, entity)
}

func TestModelToEntityList(t *testing.T) {
	// 测试正常转换
	now := time.Now()
	models := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      100,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
			CreateTime:     &now,
			UpdateTime:     &now,
		},
		{
			ID:             2,
			ServiceID:      100,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			AttributeValue: "true",
			CreateTime:     &now,
			UpdateTime:     &now,
		},
	}

	entities := ModelToEntityList(models)

	assert.NotNil(t, entities)
	assert.Len(t, entities, 2)
	assert.Equal(t, int64(1), entities[0].ID)
	assert.Equal(t, offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION, entities[0].AttributeKey)
	assert.Equal(t, "60", entities[0].AttributeValue)
	assert.Equal(t, int64(2), entities[1].ID)
	assert.Equal(t, offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED, entities[1].AttributeKey)
	assert.Equal(t, "true", entities[1].AttributeValue)

	// 测试空列表
	entities = ModelToEntityList([]*model.ServiceAttribute{})
	assert.Empty(t, entities)

	// 测试 nil 列表
	entities = ModelToEntityList(nil)
	assert.Empty(t, entities)
}
