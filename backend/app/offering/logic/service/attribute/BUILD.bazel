load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "attribute",
    srcs = [
        "attribute.go",
        "converter.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/service/attribute",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/serviceattribute",
        "//backend/proto/offering/v1:offering",
        "@org_golang_google_protobuf//types/known/structpb",
    ],
)

go_test(
    name = "attribute_test",
    srcs = [
        "attribute_test.go",
        "converter_test.go",
        "helper_test.go",
    ],
    embed = [":attribute"],
    deps = [
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/serviceattribute/mocks",
        "//backend/proto/offering/v1:offering",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_mock//gomock",
    ],
)
