package attribute

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// ServiceAttribute represents a service attribute value entity
type ServiceAttribute struct {
	ID             int64
	ServiceID      int64
	FieldName      string
	AttributeKey   offeringpb.AttributeKey
	AttributeValue string
	CreateTime     time.Time
	UpdateTime     time.Time
	DeleteTime     *time.Time
}
