package attribute

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/serviceattribute/mocks"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestLogic_CreateServiceAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	now := time.Now()
	e := &ServiceAttribute{
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "60",
	}

	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.ServiceAttribute) error {
		m.ID = 1
		m.CreateTime = &now
		m.UpdateTime = &now
		return nil
	})

	result, err := logic.CreateAttribute(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, int64(1), result.ServiceID)
	assert.Equal(t, offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION, result.AttributeKey)
	assert.Equal(t, "60", result.AttributeValue)
}

func TestLogic_CreateServiceAttribute_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	e := &ServiceAttribute{
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "60",
	}

	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("create error"))

	result, err := logic.CreateAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "create error")
}

func TestLogic_GetServiceAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	now := time.Now()
	mockModel := &model.ServiceAttribute{
		ID:             1,
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "60",
		CreateTime:     &now,
		UpdateTime:     &now,
	}

	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(mockModel, nil)

	result, err := logic.GetAttribute(context.Background(), 1)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, int64(1), result.ServiceID)
	assert.Equal(t, offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION, result.AttributeKey)
	assert.Equal(t, "60", result.AttributeValue)
}

func TestLogic_GetServiceAttribute_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(nil, errors.New("get error"))

	result, err := logic.GetAttribute(context.Background(), 1)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "get error")
}

func TestLogic_UpdateServiceAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	now := time.Now()
	e := &ServiceAttribute{
		ID:             1,
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "90", // Updated value
	}

	updatedModel := &model.ServiceAttribute{
		ID:             1,
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "90",
		CreateTime:     &now,
		UpdateTime:     &now,
	}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(updatedModel, nil)

	result, err := logic.UpdateServiceAttribute(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "90", result.AttributeValue)
}

func TestLogic_UpdateServiceAttribute_UpdateError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	e := &ServiceAttribute{
		ID:             1,
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "90",
	}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

	result, err := logic.UpdateServiceAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "update error")
}

func TestLogic_UpdateServiceAttribute_GetError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	e := &ServiceAttribute{
		ID:             1,
		ServiceID:      1,
		AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		AttributeValue: "90",
	}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(nil, errors.New("get error"))

	result, err := logic.UpdateServiceAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "get error")
}

func TestLogic_DeleteServiceAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)

	err := logic.DeleteAttribute(context.Background(), 1)

	assert.NoError(t, err)
}

func TestLogic_DeleteServiceAttribute_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(errors.New("delete error"))

	err := logic.DeleteAttribute(context.Background(), 1)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "delete error")
}

func TestLogic_ListServiceAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	now := time.Now()
	mockModels := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
			CreateTime:     &now,
			UpdateTime:     &now,
		},
		{
			ID:             2,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			AttributeValue: "true",
			CreateTime:     &now,
			UpdateTime:     &now,
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), int64(1)).Return(mockModels, nil)

	results, err := logic.ListAttributes(context.Background(), 1)

	assert.NoError(t, err)
	assert.NotNil(t, results)
	assert.Len(t, results, 2)
	assert.Equal(t, int64(1), results[0].ID)
	assert.Equal(t, offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION, results[0].AttributeKey)
	assert.Equal(t, "60", results[0].AttributeValue)
	assert.Equal(t, int64(2), results[1].ID)
	assert.Equal(t, offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED, results[1].AttributeKey)
	assert.Equal(t, "true", results[1].AttributeValue)
}

func TestLogic_ListServiceAttributes_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	mockRepo.EXPECT().List(gomock.Any(), int64(1)).Return(nil, errors.New("list error"))

	results, err := logic.ListAttributes(context.Background(), 1)

	assert.Error(t, err)
	assert.Nil(t, results)
	assert.Contains(t, err.Error(), "list error")
}

func TestLogic_CreateServiceAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	attributes := make(map[string]*structpb.Value)
	attributes["ATTRIBUTE_KEY_DURATION"] = structpb.NewNumberValue(60)
	attributes["ATTRIBUTE_KEY_IS_LODGING_REQUIRED"] = structpb.NewBoolValue(true)

	// 每个属性都会调用一次Create
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	err := logic.CreateAttributes(context.Background(), 1, attributes)

	assert.NoError(t, err)
}

func TestLogic_CreateServiceAttributes_InvalidKey(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	attributes := make(map[string]*structpb.Value)
	attributes["INVALID_KEY"] = structpb.NewNumberValue(60)

	// 由于键无效，不应调用Create
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(0)

	err := logic.CreateAttributes(context.Background(), 1, attributes)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unknown attribute key")
}

func TestLogic_DeleteServiceAttributesByTemplateID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	now := time.Now()
	mockModels := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
			CreateTime:     &now,
			UpdateTime:     &now,
		},
		{
			ID:             2,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			AttributeValue: "true",
			CreateTime:     &now,
			UpdateTime:     &now,
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), int64(1)).Return(mockModels, nil)
	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)
	mockRepo.EXPECT().Delete(gomock.Any(), int64(2)).Return(nil)

	err := logic.DeleteAttributesByServiceID(context.Background(), 1)

	assert.NoError(t, err)
}

func TestLogic_DeleteServiceAttributesByTemplateID_ListError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	mockRepo.EXPECT().List(gomock.Any(), int64(1)).Return(nil, errors.New("list error"))

	err := logic.DeleteAttributesByServiceID(context.Background(), 1)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "list error")
}

func TestLogic_DeleteServiceAttributesByTemplateID_DeleteError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := NewWithRepository(mockRepo)

	now := time.Now()
	mockModels := []*model.ServiceAttribute{
		{
			ID:             1,
			ServiceID:      1,
			AttributeKey:   offeringpb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			AttributeValue: "60",
			CreateTime:     &now,
			UpdateTime:     &now,
		},
	}

	mockRepo.EXPECT().List(gomock.Any(), int64(1)).Return(mockModels, nil)
	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(errors.New("delete error"))

	err := logic.DeleteAttributesByServiceID(context.Background(), 1)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "delete error")
}
