package attribute

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/structpb"

	mock "github.com/MoeGolibrary/moego/backend/app/offering/repo/caretypeattribute/mocks"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	pb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func TestLogic_CreateCareTypeAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	e := &CareTypeAttribute{
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		Label:        "Test Label",
		ValueType:    pb.ValueType_VALUE_TYPE_NUMBER,
		Description:  "Test Description",
		IsRequired:   true,
	}
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.CareTypeAttribute) error {
		m.ID = 1
		return nil
	})

	result, err := logic.CreateCareTypeAttribute(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Test Label", result.Label)
	assert.Equal(t, pb.AttributeKey_ATTRIBUTE_KEY_DURATION, result.AttributeKey)
	assert.Equal(t, pb.ValueType_VALUE_TYPE_NUMBER, result.ValueType)
	assert.Equal(t, "Test Description", result.Description)
	assert.Equal(t, true, result.IsRequired)
}

func TestLogic_CreateCareTypeAttribute_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	e := &CareTypeAttribute{Label: "Test Label"}
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("create error"))

	result, err := logic.CreateCareTypeAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "create error")
}

func TestLogic_GetCareTypeAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label := "Test Label"
	description := "Test Description"
	mockRepo.EXPECT().
		Get(gomock.Any(), int64(1)).
		Return(&model.CareTypeAttribute{
			ID:           1,
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			Label:        &label,
			ValueType:    pb.ValueType_VALUE_TYPE_NUMBER,
			Description:  &description,
			IsRequired:   true,
		}, nil)

	result, err := logic.GetCareTypeAttribute(context.Background(), 1)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, int64(100), result.CareTypeID)
	assert.Equal(t, "Test Label", result.Label)
	assert.Equal(t, pb.AttributeKey_ATTRIBUTE_KEY_DURATION, result.AttributeKey)
	assert.Equal(t, pb.ValueType_VALUE_TYPE_NUMBER, result.ValueType)
	assert.Equal(t, "Test Description", result.Description)
	assert.Equal(t, true, result.IsRequired)
}

func TestLogic_GetCareTypeAttribute_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	mockRepo.EXPECT().
		Get(gomock.Any(), int64(1)).
		Return(nil, errors.New("get error"))

	result, err := logic.GetCareTypeAttribute(context.Background(), 1)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "get error")
}

func TestLogic_UpdateCareTypeAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	e := &CareTypeAttribute{
		ID:           1,
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		Label:        "Updated Label",
		ValueType:    pb.ValueType_VALUE_TYPE_NUMBER,
		Description:  "Updated Description",
		IsRequired:   true,
	}

	label := "Updated Label"
	description := "Updated Description"
	updatedModel := &model.CareTypeAttribute{
		ID:           1,
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		Label:        &label,
		ValueType:    pb.ValueType_VALUE_TYPE_NUMBER,
		Description:  &description,
		IsRequired:   true,
	}

	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(updatedModel, nil)

	result, err := logic.UpdateCareTypeAttribute(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Updated Label", result.Label)
	assert.Equal(t, "Updated Description", result.Description)
}

func TestLogic_UpdateCareTypeAttribute_UpdateError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	e := &CareTypeAttribute{ID: 1, Label: "Updated Label"}
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(errors.New("update error"))

	result, err := logic.UpdateCareTypeAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "update error")
}

func TestLogic_UpdateCareTypeAttribute_GetError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	e := &CareTypeAttribute{ID: 1, Label: "Updated Label"}
	mockRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().Get(gomock.Any(), int64(1)).Return(nil, errors.New("get error"))

	result, err := logic.UpdateCareTypeAttribute(context.Background(), e)

	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "get error")
}

func TestLogic_ListCareTypeAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	label1 := "Test 1"
	label2 := "Test 2"
	mockRepo.EXPECT().List(gomock.Any(), gomock.Any()).Return([]*model.CareTypeAttribute{
		{ID: 1, CareTypeID: 100, Label: &label1, AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_DURATION},
		{ID: 2, CareTypeID: 100, Label: &label2, AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED},
	}, nil)

	req := &pb.ListCareTypeAttributesRequest{CareTypeId: 100}
	results, err := logic.ListCareTypeAttributes(context.Background(), req)

	assert.NoError(t, err)
	assert.Len(t, results, 2)
	assert.Equal(t, int64(1), results[0].ID)
	assert.Equal(t, "Test 1", results[0].Label)
	assert.Equal(t, pb.AttributeKey_ATTRIBUTE_KEY_DURATION, results[0].AttributeKey)
	assert.Equal(t, int64(2), results[1].ID)
	assert.Equal(t, "Test 2", results[1].Label)
	assert.Equal(t, pb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED, results[1].AttributeKey)
}

func TestLogic_ListCareTypeAttributes_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	mockRepo.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, errors.New("list error"))

	req := &pb.ListCareTypeAttributesRequest{CareTypeId: 100}
	results, err := logic.ListCareTypeAttributes(context.Background(), req)

	assert.Error(t, err)
	assert.Nil(t, results)
	assert.Contains(t, err.Error(), "list error")
}

func TestLogic_DeleteCareTypeAttribute(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	mockRepo.EXPECT().Delete(gomock.Any(), int64(1)).Return(nil)
	mockRepo.EXPECT().Delete(gomock.Any(), int64(2)).Return(errors.New("not found"))

	err := logic.DeleteCareTypeAttribute(context.Background(), 1)
	assert.NoError(t, err)

	err = logic.DeleteCareTypeAttribute(context.Background(), 2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestLogic_CreateCareTypeAttributeWithOptions(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	// 创建带选项的属性
	options, _ := structpb.NewStruct(map[string]interface{}{
		"option1": "value1",
		"option2": "value2",
	})
	defaultValue, _ := structpb.NewValue(42.0)

	e := &CareTypeAttribute{
		CareTypeID:   100,
		AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_DURATION,
		Label:        "Duration",
		ValueType:    pb.ValueType_VALUE_TYPE_NUMBER,
		Options:      options,
		DefaultValue: defaultValue,
		IsRequired:   true,
	}

	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.CareTypeAttribute) error {
		m.ID = 1
		return nil
	})

	result, err := logic.CreateCareTypeAttribute(context.Background(), e)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "Duration", result.Label)
	assert.Equal(t, options, result.Options)
	assert.Equal(t, defaultValue, result.DefaultValue)
}

func TestLogic_BatchCreateCareTypeAttributes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	// 创建多个属性
	attributes := []*CareTypeAttribute{
		{
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			Label:        "Duration",
			ValueType:    pb.ValueType_VALUE_TYPE_NUMBER,
			IsRequired:   true,
		},
		{
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			Label:        "Lodging Required",
			ValueType:    pb.ValueType_VALUE_TYPE_BOOLEAN,
			IsRequired:   false,
		},
	}

	// 模拟创建成功
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.CareTypeAttribute) error {
		m.ID = 1
		return nil
	})
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.CareTypeAttribute) error {
		m.ID = 2
		return nil
	})

	results, err := logic.BatchCreateCareTypeAttributes(context.Background(), attributes)

	assert.NoError(t, err)
	assert.NotNil(t, results)
	assert.Len(t, results, 2)
	assert.Equal(t, int64(1), results[0].ID)
	assert.Equal(t, "Duration", results[0].Label)
	assert.Equal(t, int64(2), results[1].ID)
	assert.Equal(t, "Lodging Required", results[1].Label)
}

func TestLogic_BatchCreateCareTypeAttributes_PartialError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock.NewMockRepository(ctrl)
	logic := &Logic{repo: mockRepo}

	// 创建多个属性
	attributes := []*CareTypeAttribute{
		{
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_DURATION,
			Label:        "Duration",
			ValueType:    pb.ValueType_VALUE_TYPE_NUMBER,
			IsRequired:   true,
		},
		{
			CareTypeID:   100,
			AttributeKey: pb.AttributeKey_ATTRIBUTE_KEY_IS_LODGING_REQUIRED,
			Label:        "Lodging Required",
			ValueType:    pb.ValueType_VALUE_TYPE_BOOLEAN,
			IsRequired:   false,
		},
	}

	// 模拟第一个创建成功，第二个失败
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m *model.CareTypeAttribute) error {
		m.ID = 1
		return nil
	})
	mockRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("create error"))

	results, err := logic.BatchCreateCareTypeAttributes(context.Background(), attributes)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "create error")
	assert.Nil(t, results)
}
