load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "attribute",
    srcs = [
        "attribute.go",
        "converter.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/caretype/attribute",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/caretypeattribute",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/utils",
        "//backend/proto/offering/v1:offering",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "attribute_test",
    srcs = [
        "attribute_test.go",
        "converter_test.go",
    ],
    embed = [":attribute"],
    deps = [
        "//backend/app/offering/repo/caretypeattribute/mocks",
        "//backend/app/offering/repo/model",
        "//backend/proto/offering/v1:offering",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_mock//gomock",
    ],
)
