package utils

import (
	"database/sql/driver"
	"fmt"
	"reflect"
)

// EnumValuer 将任意枚举类型转换为实现 driver.Valuer 接口的包装器
type EnumValuer struct {
	value interface{}
}

// Value 实现 driver.Valuer 接口
func (e EnumValuer) Value() (driver.Value, error) {
	if e.value == nil {
		return nil, nil
	}

	// 使用反射获取底层值
	val := reflect.ValueOf(e.value)

	// 处理指针类型
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return nil, nil
		}
		val = val.Elem()
	}

	// 根据枚举的底层类型返回适当的 driver.Value
	switch val.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return val.Int(), nil
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return val.Uint(), nil
	case reflect.String:
		return val.String(), nil
	default:
		return nil, fmt.Errorf("unsupported enum type: %T", e.value)
	}
}

// ToValuer 将任意枚举值转换为 driver.Valuer
func ToValuer(enum interface{}) driver.Valuer {
	return EnumValuer{value: enum}
}

// ToSliceValuer 将枚举切片转换为 driver.Valuer 切片
func ToSliceValuer(enums interface{}) []driver.Valuer {
	val := reflect.ValueOf(enums)

	// 处理指针类型
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return nil
		}
		val = val.Elem()
	}

	// 确保是切片类型
	if val.Kind() != reflect.Slice {
		panic(fmt.Sprintf("expected slice, got %T", enums))
	}

	result := make([]driver.Valuer, val.Len())
	for i := 0; i < val.Len(); i++ {
		result[i] = ToValuer(val.Index(i).Interface())
	}

	return result
}
