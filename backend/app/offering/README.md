# moego-offering

offering 主要用于管理 MoeGo 系统中的各类服务，包括但不限于寄养（boarding）、日托（daycare）、美容（grooming）等标准服务类型，以及支持自定义服务类型。该领域的数据结构和业务逻辑设计如下：
## 1. 标准与自定义服务类型
+ 标准服务类型：如 boarding、daycare、grooming 等，系统内置，字段结构固定。
+ 自定义服务类型：商家可根据实际业务需求自定义服务类型及其属性。
  + 自定义服务的字段名称和类型信息通过 service_type_attribute 表进行配置。
  + 每个自定义服务的具体属性值通过 service_attribute_values 表进行存储，关联到具体的 service_factory。

## 2. 服务属性（Attributes）
+ service_type_attribute

记录自定义服务类型的属性定义，包括字段名称、类型（如文本、数字、布尔等）。

+ service_attribute_value

记录某个具体服务（service_factory）的属性值，实现灵活的服务扩展。

## 3. 服务可用性（Availability）
+ 每个服务包含详细的可用性信息，用于筛选哪些宠物可以使用该服务。
+ 可用性筛选条件包括但不限于：
    + 宠物体重（weight）
    + 毛发类型（coat type）
    + 宠物大小（pet size）
    + 宠物类型与品种（pet type, breed）

通过这些条件，商家可灵活配置服务的适用范围。

## 4. 服务选项（Options / Add-ons）
+ 每个服务可关联多个选项（option），即 add-on 服务。
+ 选项可为主服务提供附加价值，如加急、美容套餐等。

## 5. C 端 Online Booking 可用性配置
+ 针对 C 端用户（宠物主人）的在线预约，支持单独配置服务的可预约性。
+ 可设置服务的价格展示形式、配置哪些 staff 可做、针对哪些 care type 可一并开放。

## Development
project structure
```
.
├── CODEOWNERS
├── README.md
├── config
│   └── config
│       ├── production
│       │   └── config.yaml
│       ├── staging
│       │   └── config.yaml
│       └── testing
│           └── config.yaml
├── e2e
│   ├── e2e.go
│   └── todo
│       └── todo_test.go
├── entity
│   └── entity.go
├── logic
│   └── greeter
│       └── greeter_logic.go
├── main.go
├── repo
│   ├── gorm
│   │   └── gorm.go
│   ├── repo.go
│   └── stripe
│       └── client.go
├── service
│   └── greeter_service.go
└── utils
    └── utils.go

```

其中:
- `config` 配置文件目录，按环境分为子目录，每个子目录包含对应的 config.yaml 文件
- `e2e` 存放测试代码
- `entity` 存放服务内部的实体定义
- `logic` 存放服务的业务逻辑，该层以 entity 定义进行操作，会调用 repo 获取外部资源
- `repo` 提供外部依赖接口定义及实现，包括 rpc、db、cache 等
- `service` GRPC 接口的实现，wire.go 用于依赖注入，该层会将对外 proto 转化为 entity 并调用 logic 层
- `utils` 常用的工具函数
- `main.go` 服务启动的入口
- `.gitignore` gitignore 文件
- `CODEOWNERS` 包含对应目录的 Code Review necessary reviewers
- `README.md` 项目说明
