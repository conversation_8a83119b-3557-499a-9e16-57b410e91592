load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "gorm_lib",
    srcs = ["gorm_gen.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/cmd/gorm",
    visibility = ["//visibility:private"],
    deps = [
        "@io_gorm_driver_postgres//:postgres",
        "@io_gorm_gen//:gen",
        "@io_gorm_gorm//:gorm",
    ],
)

go_binary(
    name = "gorm",
    embed = [":gorm_lib"],
    visibility = ["//visibility:public"],
)
