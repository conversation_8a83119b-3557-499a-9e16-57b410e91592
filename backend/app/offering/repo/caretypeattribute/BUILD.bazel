load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "caretypeattribute",
    srcs = [
        "care_type_attribute.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/caretypeattribute",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/query",
    ],
)
