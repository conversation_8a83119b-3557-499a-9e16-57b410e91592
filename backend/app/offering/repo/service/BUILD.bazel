load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "entity.go",
        "service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/query",
        "//backend/app/offering/utils",
        "//backend/proto/offering/v1:offering",
    ],
)
