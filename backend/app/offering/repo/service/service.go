package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/query"
	"github.com/MoeGolibrary/moego/backend/app/offering/utils"
)

//go:generate mockgen -package=mock -destination=mocks/mock_service_repo.go . Repository
type Repository interface {
	Create(ctx context.Context, service *model.Service) error
	Get(ctx context.Context, id int64) (*model.Service, error)
	Update(ctx context.Context, service *model.Service) error
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, filter *ListServiceFilter) ([]*model.Service, error)
}

// repository implements the data access logic for Service.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// Create creates a new service template.
func (r *repository) Create(ctx context.Context, service *model.Service) error {
	return r.query.Service.WithContext(ctx).Create(service)
}

// Get gets a service template by ID.
func (r *repository) Get(ctx context.Context, id int64) (*model.Service, error) {
	return r.query.Service.WithContext(ctx).Where(r.query.Service.ID.Eq(id)).First()
}

// Update updates a service template.
func (r *repository) Update(ctx context.Context, service *model.Service) error {
	_, err := r.query.Service.WithContext(ctx).
		Where(r.query.Service.ID.Eq(service.ID)).Updates(service)
	if err != nil {
		return err
	}
	return nil
}

// Delete deletes a service template by ID.
func (r *repository) Delete(ctx context.Context, id int64) error {
	result, err := r.query.Service.WithContext(ctx).Where(r.query.Service.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}
	return result.Error
}

// List lists service templates.
func (r *repository) List(ctx context.Context, filter *ListServiceFilter) ([]*model.Service, error) {
	if filter == nil {
		return []*model.Service{}, nil
	}

	q := r.query.Service.WithContext(ctx).
		Where(r.query.Service.OrganizationType.Eq(utils.ToValuer(filter.OrganizationType))).
		Where(r.query.Service.OrganizationID.Eq(filter.OrganizationID))

	if len(filter.CareTypeIDs) > 0 {
		q = q.Where(r.query.Service.CareTypeID.In(filter.CareTypeIDs...))
	}
	if len(filter.CategoriesIDs) > 0 {
		q = q.Where(r.query.Service.CategoryID.In(filter.CategoriesIDs...))
	}
	if filter.IsActive != nil {
		q = q.Where(r.query.Service.IsActive.Is(*filter.IsActive))
	}
	if filter.Source != nil {
		q = q.Where(r.query.Service.Source.Eq(utils.ToValuer(*filter.Source)))
	}

	return q.Order(r.query.Service.CreateTime.Asc()).Find()
}
