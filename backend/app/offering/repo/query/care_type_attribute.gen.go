// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
)

func newCareTypeAttribute(db *gorm.DB, opts ...gen.DOOption) careTypeAttribute {
	_careTypeAttribute := careTypeAttribute{}

	_careTypeAttribute.careTypeAttributeDo.UseDB(db, opts...)
	_careTypeAttribute.careTypeAttributeDo.UseModel(&model.CareTypeAttribute{})

	tableName := _careTypeAttribute.careTypeAttributeDo.TableName()
	_careTypeAttribute.ALL = field.NewAsterisk(tableName)
	_careTypeAttribute.ID = field.NewInt64(tableName, "id")
	_careTypeAttribute.CareTypeID = field.NewInt64(tableName, "care_type_id")
	_careTypeAttribute.AttributeKey = field.NewField(tableName, "attribute_key")
	_careTypeAttribute.Label = field.NewString(tableName, "label")
	_careTypeAttribute.ValueType = field.NewField(tableName, "value_type")
	_careTypeAttribute.Options = field.NewString(tableName, "options")
	_careTypeAttribute.Description = field.NewString(tableName, "description")
	_careTypeAttribute.IsRequired = field.NewBool(tableName, "is_required")
	_careTypeAttribute.DefaultValue = field.NewString(tableName, "default_value")
	_careTypeAttribute.CreateTime = field.NewTime(tableName, "create_time")
	_careTypeAttribute.UpdateTime = field.NewTime(tableName, "update_time")
	_careTypeAttribute.DeleteTime = field.NewTime(tableName, "delete_time")

	_careTypeAttribute.fillFieldMap()

	return _careTypeAttribute
}

type careTypeAttribute struct {
	careTypeAttributeDo careTypeAttributeDo

	ALL          field.Asterisk
	ID           field.Int64  // Primary key ID of the care type attribute entry
	CareTypeID   field.Int64  // Care type to which this attribute is assigned
	AttributeKey field.Field  // Unique name (key) of the attribute within the care type (e.g., size, duration)
	Label        field.String // Display label for frontend UI (e.g., Pet Size)
	ValueType    field.Field  // Value type of the attribute (1STRING, 2NUMBER, 3BOOLEAN, 4ENUM)
	Options      field.String // JSON list of options (used when value_type is ENUM or BOOLEAN)
	Description  field.String // Optional explanation or usage hint
	IsRequired   field.Bool   // Whether this attribute is required for the care type
	DefaultValue field.String // Default value assigned to this attribute
	CreateTime   field.Time
	UpdateTime   field.Time
	DeleteTime   field.Time

	fieldMap map[string]field.Expr
}

func (c careTypeAttribute) Table(newTableName string) *careTypeAttribute {
	c.careTypeAttributeDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c careTypeAttribute) As(alias string) *careTypeAttribute {
	c.careTypeAttributeDo.DO = *(c.careTypeAttributeDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *careTypeAttribute) updateTableName(table string) *careTypeAttribute {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.CareTypeID = field.NewInt64(table, "care_type_id")
	c.AttributeKey = field.NewField(table, "attribute_key")
	c.Label = field.NewString(table, "label")
	c.ValueType = field.NewField(table, "value_type")
	c.Options = field.NewString(table, "options")
	c.Description = field.NewString(table, "description")
	c.IsRequired = field.NewBool(table, "is_required")
	c.DefaultValue = field.NewString(table, "default_value")
	c.CreateTime = field.NewTime(table, "create_time")
	c.UpdateTime = field.NewTime(table, "update_time")
	c.DeleteTime = field.NewTime(table, "delete_time")

	c.fillFieldMap()

	return c
}

func (c *careTypeAttribute) WithContext(ctx context.Context) *careTypeAttributeDo {
	return c.careTypeAttributeDo.WithContext(ctx)
}

func (c careTypeAttribute) TableName() string { return c.careTypeAttributeDo.TableName() }

func (c careTypeAttribute) Alias() string { return c.careTypeAttributeDo.Alias() }

func (c careTypeAttribute) Columns(cols ...field.Expr) gen.Columns {
	return c.careTypeAttributeDo.Columns(cols...)
}

func (c *careTypeAttribute) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *careTypeAttribute) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 12)
	c.fieldMap["id"] = c.ID
	c.fieldMap["care_type_id"] = c.CareTypeID
	c.fieldMap["attribute_key"] = c.AttributeKey
	c.fieldMap["label"] = c.Label
	c.fieldMap["value_type"] = c.ValueType
	c.fieldMap["options"] = c.Options
	c.fieldMap["description"] = c.Description
	c.fieldMap["is_required"] = c.IsRequired
	c.fieldMap["default_value"] = c.DefaultValue
	c.fieldMap["create_time"] = c.CreateTime
	c.fieldMap["update_time"] = c.UpdateTime
	c.fieldMap["delete_time"] = c.DeleteTime
}

func (c careTypeAttribute) clone(db *gorm.DB) careTypeAttribute {
	c.careTypeAttributeDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c careTypeAttribute) replaceDB(db *gorm.DB) careTypeAttribute {
	c.careTypeAttributeDo.ReplaceDB(db)
	return c
}

type careTypeAttributeDo struct{ gen.DO }

func (c careTypeAttributeDo) Debug() *careTypeAttributeDo {
	return c.withDO(c.DO.Debug())
}

func (c careTypeAttributeDo) WithContext(ctx context.Context) *careTypeAttributeDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c careTypeAttributeDo) ReadDB() *careTypeAttributeDo {
	return c.Clauses(dbresolver.Read)
}

func (c careTypeAttributeDo) WriteDB() *careTypeAttributeDo {
	return c.Clauses(dbresolver.Write)
}

func (c careTypeAttributeDo) Session(config *gorm.Session) *careTypeAttributeDo {
	return c.withDO(c.DO.Session(config))
}

func (c careTypeAttributeDo) Clauses(conds ...clause.Expression) *careTypeAttributeDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c careTypeAttributeDo) Returning(value interface{}, columns ...string) *careTypeAttributeDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c careTypeAttributeDo) Not(conds ...gen.Condition) *careTypeAttributeDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c careTypeAttributeDo) Or(conds ...gen.Condition) *careTypeAttributeDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c careTypeAttributeDo) Select(conds ...field.Expr) *careTypeAttributeDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c careTypeAttributeDo) Where(conds ...gen.Condition) *careTypeAttributeDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c careTypeAttributeDo) Order(conds ...field.Expr) *careTypeAttributeDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c careTypeAttributeDo) Distinct(cols ...field.Expr) *careTypeAttributeDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c careTypeAttributeDo) Omit(cols ...field.Expr) *careTypeAttributeDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c careTypeAttributeDo) Join(table schema.Tabler, on ...field.Expr) *careTypeAttributeDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c careTypeAttributeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *careTypeAttributeDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c careTypeAttributeDo) RightJoin(table schema.Tabler, on ...field.Expr) *careTypeAttributeDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c careTypeAttributeDo) Group(cols ...field.Expr) *careTypeAttributeDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c careTypeAttributeDo) Having(conds ...gen.Condition) *careTypeAttributeDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c careTypeAttributeDo) Limit(limit int) *careTypeAttributeDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c careTypeAttributeDo) Offset(offset int) *careTypeAttributeDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c careTypeAttributeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *careTypeAttributeDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c careTypeAttributeDo) Unscoped() *careTypeAttributeDo {
	return c.withDO(c.DO.Unscoped())
}

func (c careTypeAttributeDo) Create(values ...*model.CareTypeAttribute) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c careTypeAttributeDo) CreateInBatches(values []*model.CareTypeAttribute, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c careTypeAttributeDo) Save(values ...*model.CareTypeAttribute) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c careTypeAttributeDo) First() (*model.CareTypeAttribute, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareTypeAttribute), nil
	}
}

func (c careTypeAttributeDo) Take() (*model.CareTypeAttribute, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareTypeAttribute), nil
	}
}

func (c careTypeAttributeDo) Last() (*model.CareTypeAttribute, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareTypeAttribute), nil
	}
}

func (c careTypeAttributeDo) Find() ([]*model.CareTypeAttribute, error) {
	result, err := c.DO.Find()
	return result.([]*model.CareTypeAttribute), err
}

func (c careTypeAttributeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CareTypeAttribute, err error) {
	buf := make([]*model.CareTypeAttribute, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c careTypeAttributeDo) FindInBatches(result *[]*model.CareTypeAttribute, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c careTypeAttributeDo) Attrs(attrs ...field.AssignExpr) *careTypeAttributeDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c careTypeAttributeDo) Assign(attrs ...field.AssignExpr) *careTypeAttributeDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c careTypeAttributeDo) Joins(fields ...field.RelationField) *careTypeAttributeDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c careTypeAttributeDo) Preload(fields ...field.RelationField) *careTypeAttributeDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c careTypeAttributeDo) FirstOrInit() (*model.CareTypeAttribute, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareTypeAttribute), nil
	}
}

func (c careTypeAttributeDo) FirstOrCreate() (*model.CareTypeAttribute, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CareTypeAttribute), nil
	}
}

func (c careTypeAttributeDo) FindByPage(offset int, limit int) (result []*model.CareTypeAttribute, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c careTypeAttributeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c careTypeAttributeDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c careTypeAttributeDo) Delete(models ...*model.CareTypeAttribute) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *careTypeAttributeDo) withDO(do gen.Dao) *careTypeAttributeDo {
	c.DO = *do.(*gen.DO)
	return c
}
