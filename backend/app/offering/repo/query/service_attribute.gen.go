// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/model"
)

func newServiceAttribute(db *gorm.DB, opts ...gen.DOOption) serviceAttribute {
	_serviceAttribute := serviceAttribute{}

	_serviceAttribute.serviceAttributeDo.UseDB(db, opts...)
	_serviceAttribute.serviceAttributeDo.UseModel(&model.ServiceAttribute{})

	tableName := _serviceAttribute.serviceAttributeDo.TableName()
	_serviceAttribute.ALL = field.NewAsterisk(tableName)
	_serviceAttribute.ID = field.NewInt64(tableName, "id")
	_serviceAttribute.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceAttribute.FieldName = field.NewString(tableName, "field_name")
	_serviceAttribute.AttributeKey = field.NewField(tableName, "attribute_key")
	_serviceAttribute.AttributeValue = field.NewString(tableName, "attribute_value")
	_serviceAttribute.CreateTime = field.NewTime(tableName, "create_time")
	_serviceAttribute.UpdateTime = field.NewTime(tableName, "update_time")
	_serviceAttribute.DeleteTime = field.NewTime(tableName, "delete_time")

	_serviceAttribute.fillFieldMap()

	return _serviceAttribute
}

type serviceAttribute struct {
	serviceAttributeDo serviceAttributeDo

	ALL            field.Asterisk
	ID             field.Int64  // Primary key ID of the service attribute value
	ServiceID      field.Int64  // Reference to the service
	FieldName      field.String // The field name used in frontend JSON payloads for this attribute (e.g., petType, petSize)
	AttributeKey   field.Field  // Unique name (key) of the attribute within the care type (e.g., size, duration)
	AttributeValue field.String // Concrete value assigned to the attribute for this service-care_type pair
	CreateTime     field.Time
	UpdateTime     field.Time
	DeleteTime     field.Time

	fieldMap map[string]field.Expr
}

func (s serviceAttribute) Table(newTableName string) *serviceAttribute {
	s.serviceAttributeDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceAttribute) As(alias string) *serviceAttribute {
	s.serviceAttributeDo.DO = *(s.serviceAttributeDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceAttribute) updateTableName(table string) *serviceAttribute {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.FieldName = field.NewString(table, "field_name")
	s.AttributeKey = field.NewField(table, "attribute_key")
	s.AttributeValue = field.NewString(table, "attribute_value")
	s.CreateTime = field.NewTime(table, "create_time")
	s.UpdateTime = field.NewTime(table, "update_time")
	s.DeleteTime = field.NewTime(table, "delete_time")

	s.fillFieldMap()

	return s
}

func (s *serviceAttribute) WithContext(ctx context.Context) *serviceAttributeDo {
	return s.serviceAttributeDo.WithContext(ctx)
}

func (s serviceAttribute) TableName() string { return s.serviceAttributeDo.TableName() }

func (s serviceAttribute) Alias() string { return s.serviceAttributeDo.Alias() }

func (s serviceAttribute) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceAttributeDo.Columns(cols...)
}

func (s *serviceAttribute) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceAttribute) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["field_name"] = s.FieldName
	s.fieldMap["attribute_key"] = s.AttributeKey
	s.fieldMap["attribute_value"] = s.AttributeValue
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["update_time"] = s.UpdateTime
	s.fieldMap["delete_time"] = s.DeleteTime
}

func (s serviceAttribute) clone(db *gorm.DB) serviceAttribute {
	s.serviceAttributeDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceAttribute) replaceDB(db *gorm.DB) serviceAttribute {
	s.serviceAttributeDo.ReplaceDB(db)
	return s
}

type serviceAttributeDo struct{ gen.DO }

func (s serviceAttributeDo) Debug() *serviceAttributeDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceAttributeDo) WithContext(ctx context.Context) *serviceAttributeDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceAttributeDo) ReadDB() *serviceAttributeDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceAttributeDo) WriteDB() *serviceAttributeDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceAttributeDo) Session(config *gorm.Session) *serviceAttributeDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceAttributeDo) Clauses(conds ...clause.Expression) *serviceAttributeDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceAttributeDo) Returning(value interface{}, columns ...string) *serviceAttributeDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceAttributeDo) Not(conds ...gen.Condition) *serviceAttributeDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceAttributeDo) Or(conds ...gen.Condition) *serviceAttributeDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceAttributeDo) Select(conds ...field.Expr) *serviceAttributeDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceAttributeDo) Where(conds ...gen.Condition) *serviceAttributeDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceAttributeDo) Order(conds ...field.Expr) *serviceAttributeDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceAttributeDo) Distinct(cols ...field.Expr) *serviceAttributeDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceAttributeDo) Omit(cols ...field.Expr) *serviceAttributeDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceAttributeDo) Join(table schema.Tabler, on ...field.Expr) *serviceAttributeDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceAttributeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *serviceAttributeDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceAttributeDo) RightJoin(table schema.Tabler, on ...field.Expr) *serviceAttributeDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceAttributeDo) Group(cols ...field.Expr) *serviceAttributeDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceAttributeDo) Having(conds ...gen.Condition) *serviceAttributeDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceAttributeDo) Limit(limit int) *serviceAttributeDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceAttributeDo) Offset(offset int) *serviceAttributeDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceAttributeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *serviceAttributeDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceAttributeDo) Unscoped() *serviceAttributeDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceAttributeDo) Create(values ...*model.ServiceAttribute) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceAttributeDo) CreateInBatches(values []*model.ServiceAttribute, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceAttributeDo) Save(values ...*model.ServiceAttribute) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceAttributeDo) First() (*model.ServiceAttribute, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttribute), nil
	}
}

func (s serviceAttributeDo) Take() (*model.ServiceAttribute, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttribute), nil
	}
}

func (s serviceAttributeDo) Last() (*model.ServiceAttribute, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttribute), nil
	}
}

func (s serviceAttributeDo) Find() ([]*model.ServiceAttribute, error) {
	result, err := s.DO.Find()
	return result.([]*model.ServiceAttribute), err
}

func (s serviceAttributeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ServiceAttribute, err error) {
	buf := make([]*model.ServiceAttribute, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceAttributeDo) FindInBatches(result *[]*model.ServiceAttribute, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceAttributeDo) Attrs(attrs ...field.AssignExpr) *serviceAttributeDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceAttributeDo) Assign(attrs ...field.AssignExpr) *serviceAttributeDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceAttributeDo) Joins(fields ...field.RelationField) *serviceAttributeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceAttributeDo) Preload(fields ...field.RelationField) *serviceAttributeDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceAttributeDo) FirstOrInit() (*model.ServiceAttribute, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttribute), nil
	}
}

func (s serviceAttributeDo) FirstOrCreate() (*model.ServiceAttribute, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ServiceAttribute), nil
	}
}

func (s serviceAttributeDo) FindByPage(offset int, limit int) (result []*model.ServiceAttribute, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceAttributeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceAttributeDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceAttributeDo) Delete(models ...*model.ServiceAttribute) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceAttributeDo) withDO(do gen.Dao) *serviceAttributeDo {
	s.DO = *do.(*gen.DO)
	return s
}
