// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

const TableNameServiceAttribute = "service_attribute"

// ServiceAttribute mapped from table <service_attribute>
type ServiceAttribute struct {
	ID             int64                   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:Primary key ID of the service attribute value" json:"id"`                                                        // Primary key ID of the service attribute value
	ServiceID      int64                   `gorm:"column:service_id;type:bigint;not null;comment:Reference to the service" json:"service_id"`                                                                                  // Reference to the service
	FieldName      string                  `gorm:"column:field_name;type:character varying(255);not null;comment:The field name used in frontend JSON payloads for this attribute (e.g., petType, petSize)" json:"field_name"` // The field name used in frontend JSON payloads for this attribute (e.g., petType, petSize)
	AttributeKey   offeringpb.AttributeKey `gorm:"column:attribute_key;type:integer;not null;comment:Unique name (key) of the attribute within the care type (e.g., size, duration)" json:"attribute_key"`                     // Unique name (key) of the attribute within the care type (e.g., size, duration)
	AttributeValue string                  `gorm:"column:attribute_value;type:text;not null;comment:Concrete value assigned to the attribute for this service-care_type pair" json:"attribute_value"`                          // Concrete value assigned to the attribute for this service-care_type pair
	CreateTime     *time.Time              `gorm:"column:create_time;type:timestamp with time zone;not null;default:now()" json:"create_time"`
	UpdateTime     *time.Time              `gorm:"column:update_time;type:timestamp with time zone;not null;default:now()" json:"update_time"`
	DeleteTime     *time.Time              `gorm:"column:delete_time;type:timestamp with time zone" json:"delete_time"`
}

// TableName ServiceAttribute's table name
func (*ServiceAttribute) TableName() string {
	return TableNameServiceAttribute
}
