package caretype

import (
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

// ListCareTypeFilter filter for listing care types
type ListCareTypeFilter struct {
	// Required
	OrganizationType offeringpb.OrganizationType
	OrganizationID   int64

	// Optional
	CareCategories []offeringpb.CareCategory
	Pagination     *db.Pagination
}
