load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "caretype",
    srcs = [
        "care_type.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/caretype",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db",
        "//backend/app/offering/repo/model",
        "//backend/app/offering/repo/query",
        "//backend/app/offering/utils",
        "//backend/proto/offering/v1:offering",
    ],
)
