// Code generated by MockGen. DO NOT EDIT.
// Source: ./meta/meta.go
//
// Generated by this command:
//
//	mockgen -source=./meta/meta.go -destination=./meta/mock/meta_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	meta "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/meta"
	gomock "go.uber.org/mock/gomock"
	oauth2 "golang.org/x/oauth2"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// ExchangeCode mocks base method.
func (m *MockReadWriter) ExchangeCode(ctx context.Context, code string) (*oauth2.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeCode", ctx, code)
	ret0, _ := ret[0].(*oauth2.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeCode indicates an expected call of ExchangeCode.
func (mr *MockReadWriterMockRecorder) ExchangeCode(ctx, code any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeCode", reflect.TypeOf((*MockReadWriter)(nil).ExchangeCode), ctx, code)
}

// GetMetaAdsOAuth2Link mocks base method.
func (m *MockReadWriter) GetMetaAdsOAuth2Link(ctx context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMetaAdsOAuth2Link", ctx)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMetaAdsOAuth2Link indicates an expected call of GetMetaAdsOAuth2Link.
func (mr *MockReadWriterMockRecorder) GetMetaAdsOAuth2Link(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMetaAdsOAuth2Link", reflect.TypeOf((*MockReadWriter)(nil).GetMetaAdsOAuth2Link), ctx)
}

// GetUserInfo mocks base method.
func (m *MockReadWriter) GetUserInfo(ctx context.Context, token *oauth2.Token) (*meta.UserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfo", ctx, token)
	ret0, _ := ret[0].(*meta.UserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockReadWriterMockRecorder) GetUserInfo(ctx, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockReadWriter)(nil).GetUserInfo), ctx, token)
}

// ListAdsAccounts mocks base method.
func (m *MockReadWriter) ListAdsAccounts(ctx context.Context, token *oauth2.Token) ([]*meta.AdsAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAdsAccounts", ctx, token)
	ret0, _ := ret[0].([]*meta.AdsAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAdsAccounts indicates an expected call of ListAdsAccounts.
func (mr *MockReadWriterMockRecorder) ListAdsAccounts(ctx, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAdsAccounts", reflect.TypeOf((*MockReadWriter)(nil).ListAdsAccounts), ctx, token)
}
