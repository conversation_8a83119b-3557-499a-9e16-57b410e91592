load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "meta",
    srcs = ["meta.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/open_platform/repo/meta",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/open_platform/v1:open_platform",
        "@com_github_bytedance_sonic//:sonic",
        "@org_golang_x_oauth2//:oauth2",
    ],
)
