package appointment

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

const (
	defaultLimit  = 200
	defaultOffset = 0
)

type ReadWriter interface {
	List(ctx context.Context, param *BaseParam, filter *Filter) ([]*Appointment, error)
	Count(ctx context.Context, param *BaseParam, filter *Filter) (int64, error)
	BatchCreate(ctx context.Context, appointments []*Appointment) error
	Update(ctx context.Context, appointment *Appointment) error
	GetByID(ctx context.Context, id int) (*Appointment, error)
	GetByIDs(ctx context.Context, ids []int64) ([]*Appointment, error)
}

type impl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func New() ReadWriter {
	database := db.GetDB()
	return &impl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}
