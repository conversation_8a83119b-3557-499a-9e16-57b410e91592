package fulfillmentreport

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

type QuestionRepo interface {
	Create(ctx context.Context, question *Question) error
	Update(ctx context.Context, question *Question) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*Question, error)
	FindByTemplateID(ctx context.Context, templateID int64) ([]*Question, error)
	BatchCreate(ctx context.Context, questions []*Question) error
	BatchUpdate(ctx context.Context, questions []*Question) error
	BatchDelete(ctx context.Context, templateID int64) error
	BatchUpsert(ctx context.Context, questions []*Question) error
	FindByTemplateIDs(ctx context.Context, templateIDs []int64) ([]*Question, error)
	FindByTemplateIDAndTitles(ctx context.Context, templateID int64, titles []string) ([]*Question, error)
}

type questionImpl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func NewFulfillmentReportQuestionRepo() QuestionRepo {
	database := db.GetDB()
	return &questionImpl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

func (i *questionImpl) Create(ctx context.Context, question *Question) error {
	return i.db.WithContext(ctx).Create(question).Error
}

func (i *questionImpl) Update(ctx context.Context, question *Question) error {
	result := i.db.WithContext(ctx).Save(question)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

func (i *questionImpl) Delete(ctx context.Context, id int64) error {
	result := i.db.WithContext(ctx).Delete(&Question{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

func (i *questionImpl) FindByID(ctx context.Context, id int64) (*Question, error) {
	var question Question
	err := i.db.WithContext(ctx).First(&question, id).Error
	if err != nil {
		return nil, err
	}
	return &question, nil
}

func (i *questionImpl) FindByTemplateID(ctx context.Context, templateID int64) ([]*Question, error) {
	var questions []*Question
	err := i.db.WithContext(ctx).Where("template_id = ?", templateID).Order("sort").Find(&questions).Error
	if err != nil {
		return nil, err
	}
	return questions, nil
}

func (i *questionImpl) BatchCreate(ctx context.Context, questions []*Question) error {
	if len(questions) == 0 {
		return nil
	}
	return i.db.WithContext(ctx).CreateInBatches(questions, 100).Error
}

func (i *questionImpl) BatchUpdate(ctx context.Context, questions []*Question) error {
	if len(questions) == 0 {
		return nil
	}

	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, question := range questions {
			if err := tx.Save(question).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (i *questionImpl) FindByTemplateIDs(ctx context.Context, templateIDs []int64) ([]*Question, error) {
	if len(templateIDs) == 0 {
		return []*Question{}, nil
	}

	var questions []*Question
	err := i.db.WithContext(ctx).Where("template_id IN ?", templateIDs).Order("sort").Find(&questions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query questions by template IDs: %w", err)
	}

	return questions, nil
}

func (i *questionImpl) BatchDelete(ctx context.Context, templateID int64) error {
	result := i.db.WithContext(ctx).Where("template_id = ?", templateID).Delete(&Question{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (i *questionImpl) BatchUpsert(ctx context.Context, questions []*Question) error {
	if len(questions) == 0 {
		return nil
	}

	return i.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先删除该模板下的所有问题
		if len(questions) > 0 {
			templateID := questions[0].TemplateID
			if err := tx.Where("template_id = ?", templateID).Delete(&Question{}).Error; err != nil {
				return err
			}
		}

		// 批量创建新问题
		return tx.CreateInBatches(questions, 100).Error
	})
}

func (i *questionImpl) FindByTemplateIDAndTitles(
	ctx context.Context,
	templateID int64,
	titles []string,
) ([]*Question, error) {
	if len(titles) == 0 {
		return []*Question{}, nil
	}

	var questions []*Question
	err := i.db.WithContext(ctx).
		Where("template_id = ? AND title IN ?", templateID, titles).
		Order("sort").
		Find(&questions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query questions by template ID and titles: %w", err)
	}

	return questions, nil
}
