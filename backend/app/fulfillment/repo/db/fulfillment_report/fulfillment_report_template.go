package fulfillmentreport

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

type TemplateRepo interface {
	Create(ctx context.Context, template *Template) error
	Update(ctx context.Context, template *Template) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*Template, error)
	FindByUniqueKey(ctx context.Context, companyID, businessID int64, careType int32) (*Template, error)
	Upsert(ctx context.Context, template *Template) error
	FindByUniqueKeys(ctx context.Context, uniqueKeys []UniqueKey) ([]*Template, error)
}

// UniqueKey 唯一键结构
type UniqueKey struct {
	CompanyID  int64
	BusinessID int64
	CareType   int32
}

type impl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func NewFulfillmentReportTemplateRepo() TemplateRepo {
	database := db.GetDB()
	return &impl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

func (i *impl) Create(ctx context.Context, template *Template) error {
	return i.db.WithContext(ctx).Create(template).Error
}

func (i *impl) Update(ctx context.Context, template *Template) error {
	result := i.db.WithContext(ctx).Save(template)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

func (i *impl) Delete(ctx context.Context, id int64) error {
	result := i.db.WithContext(ctx).Delete(&Template{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (i *impl) FindByID(ctx context.Context, id int64) (*Template, error) {
	var template Template
	err := i.db.WithContext(ctx).First(&template, id).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (i *impl) FindByUniqueKey(ctx context.Context, companyID, businessID int64, careType int32) (*Template, error) {
	var template Template
	err := i.db.WithContext(ctx).Where("company_id = ? AND business_id = ? AND care_type = ?",
		companyID, businessID, careType).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (i *impl) Upsert(ctx context.Context, template *Template) error {
	// 尝试根据唯一键查找现有记录
	existing, err := i.FindByUniqueKey(ctx, template.CompanyID, template.BusinessID, template.CareType)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录不存在，创建新记录
			return i.Create(ctx, template)
		}
		return err
	}

	// 记录存在，更新现有记录
	template.ID = existing.ID
	template.CreateTime = existing.CreateTime // 保持原始创建时间
	return i.Update(ctx, template)
}

func (i *impl) FindByUniqueKeys(ctx context.Context, uniqueKeys []UniqueKey) ([]*Template, error) {
	if len(uniqueKeys) == 0 {
		return []*Template{}, nil
	}

	var templates []*Template
	query := i.db.WithContext(ctx)

	// 构建 OR 条件查询
	var conditions []string
	var args []interface{}

	for _, uniqueKey := range uniqueKeys {
		condition := "(company_id = ? AND business_id = ? AND care_type = ?)"
		args = append(args, uniqueKey.CompanyID, uniqueKey.BusinessID, uniqueKey.CareType)
		conditions = append(conditions, condition)
	}

	if len(conditions) > 0 {
		query = query.Where(strings.Join(conditions, " OR "), args...)
	}

	// 执行查询
	if err := query.Find(&templates).Error; err != nil {
		return nil, fmt.Errorf("failed to query templates by unique keys: %w", err)
	}

	return templates, nil
}
