package fulfillmentreport

import (
	"time"
)

//nolint:lll
type Template struct {
	ID                            int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID                     int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID                    int64     `gorm:"column:business_id;not null;uniqueIndex:udx_biz_care"`
	CareType                      int32     `gorm:"column:care_type;not null;default:1;uniqueIndex:udx_biz_care"`
	ThankYouMessage               string    `gorm:"column:thank_you_message;not null;default:'Thanks for your trust and support'"`
	ThemeColor                    string    `gorm:"column:theme_color;not null;default:'#F96B18'"`
	LightThemeColor               string    `gorm:"column:light_theme_color;not null;default:'#FEEFE6'"`
	ShowShowcase                  bool      `gorm:"column:show_showcase;not null;default:true"`
	ShowOverallFeedback           bool      `gorm:"column:show_overall_feedback;not null;default:true"`
	ShowPetCondition              bool      `gorm:"column:show_pet_condition;not null;default:true"`
	ShowServiceStaffName          bool      `gorm:"column:show_service_staff_name;not null;default:false"`
	ShowNextAppointment           bool      `gorm:"column:show_next_appointment;not null;default:true"`
	NextAppointmentDateFormatType int32     `gorm:"column:next_appointment_date_format_type;not null;default:2"`
	ShowReviewBooster             bool      `gorm:"column:show_review_booster;not null;default:true"`
	ShowYelpReview                bool      `gorm:"column:show_yelp_review;not null;default:false"`
	ShowGoogleReview              bool      `gorm:"column:show_google_review;not null;default:false"`
	ShowFacebookReview            bool      `gorm:"column:show_facebook_review;not null;default:false"`
	LastPublishTime               time.Time `gorm:"column:last_publish_time"`
	Title                         string    `gorm:"column:title;not null;default:'Service Report'"`
	ThemeCode                     string    `gorm:"column:theme_code"`
	UpdateBy                      int64     `gorm:"column:update_by;not null;default:0"`
	CreateTime                    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime                    time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (t *Template) TableName() string {
	return "fulfillment_report_template"
}

type Question struct {
	ID                int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID         int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID        int64     `gorm:"column:business_id;not null"`
	TemplateID        int64     `gorm:"column:template_id;not null;index:idx_question_template"`
	Category          int32     `gorm:"column:category;not null;default:1"`
	Type              string    `gorm:"column:type;not null"`
	Key               string    `gorm:"column:key"`
	Title             string    `gorm:"column:title;not null"`
	ExtraJSON         string    `gorm:"column:extra_json;type:jsonb"`
	IsDefault         bool      `gorm:"column:is_default;not null;default:false"`
	IsRequired        bool      `gorm:"column:is_required;not null;default:true"`
	IsTypeEditable    bool      `gorm:"column:is_type_editable;not null;default:true"`
	IsTitleEditable   bool      `gorm:"column:is_title_editable;not null;default:true"`
	IsOptionsEditable bool      `gorm:"column:is_options_editable;not null;default:true"`
	Sort              int32     `gorm:"column:sort;not null;default:0;index:idx_question_template"`
	CreateTime        time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime        time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (q *Question) TableName() string {
	return "fulfillment_report_question"
}

type FulfillmentReport struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CompanyID       int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID      int64     `gorm:"column:business_id;not null"`
	CustomerID      int64     `gorm:"column:customer_id;not null"`
	AppointmentID   int64     `gorm:"column:appointment_id;not null"`
	CareType        int32     `gorm:"column:care_type;not null;default:1"`
	PetID           int64     `gorm:"column:pet_id;not null"`
	UUID            string    `gorm:"column:uuid;unique"`
	TemplateVersion time.Time `gorm:"column:template_version"`
	TemplateJSON    string    `gorm:"column:template_json;type:jsonb;default:'{}'"`
	ContentJSON     string    `gorm:"column:content_json;type:jsonb;default:'{}'"`
	Status          string    `gorm:"column:status;not null;default:'draft'"`
	LinkOpenedCount int32     `gorm:"column:link_opened_count;not null;default:0"`
	ServiceDate     time.Time `gorm:"column:service_date;not null;type:date"`
	ThemeCode       string    `gorm:"column:theme_code"`
	UpdateBy        int64     `gorm:"column:update_by;not null;default:0"`
	CreateTime      time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime      time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (r *FulfillmentReport) TableName() string {
	return "fulfillment_report"
}

type SendRecord struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement"`
	ReportID      int64     `gorm:"column:report_id;not null"`
	CompanyID     int64     `gorm:"column:company_id;not null;default:0"`
	BusinessID    int64     `gorm:"column:business_id;not null"`
	AppointmentID int64     `gorm:"column:appointment_id;not null"`
	ContentJSON   string    `gorm:"column:content_json;type:jsonb;default:'{}'"`
	PetID         int64     `gorm:"column:pet_id;not null"`
	SendMethod    int32     `gorm:"column:send_method;not null;default:1"`
	SentTime      time.Time `gorm:"column:sent_time"`
	SentBy        int64     `gorm:"column:sent_by;not null;default:0"`
	ErrorMessage  string    `gorm:"column:error_message;not null;default:''"`
	IsSentSuccess bool      `gorm:"column:is_sent_success;not null;default:false"`
	CreateTime    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime    time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP"`
}

func (r *SendRecord) TableName() string {
	return "fulfillment_report_send_record"
}
