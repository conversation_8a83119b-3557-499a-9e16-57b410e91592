package fulfillmentreport

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/fulfillment/repo/db"
)

type ReportRepo interface {
	Create(ctx context.Context, report *FulfillmentReport) error
	Update(ctx context.Context, report *FulfillmentReport) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*FulfillmentReport, error)
	FindByUniqueKey(ctx context.Context,
		businessID, appointmentID, petID int64, careType int32, serviceDate string) (*FulfillmentReport, error)
	Upsert(ctx context.Context, report *FulfillmentReport) error
	FindByUniqueKeys(ctx context.Context, uniqueKeys []ReportUniqueKey) ([]*FulfillmentReport, error)
}

// ReportUniqueKey 报告唯一键结构
type ReportUniqueKey struct {
	BusinessID    int64
	AppointmentID int64
	PetID         int64
	CareType      int32
	ServiceDate   time.Time
}

type reportImpl struct {
	db                 *gorm.DB
	transactionManager db.TransactionManager
}

func NewFulfillmentReportRepo() ReportRepo {
	database := db.GetDB()
	return &reportImpl{
		db:                 database,
		transactionManager: db.NewTxManager(),
	}
}

func (i *reportImpl) Create(ctx context.Context, report *FulfillmentReport) error {
	return i.db.WithContext(ctx).Create(report).Error
}

func (i *reportImpl) Update(ctx context.Context, report *FulfillmentReport) error {
	result := i.db.WithContext(ctx).Save(report)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

func (i *reportImpl) Delete(ctx context.Context, id int64) error {
	result := i.db.WithContext(ctx).Delete(&FulfillmentReport{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (i *reportImpl) FindByID(ctx context.Context, id int64) (*FulfillmentReport, error) {
	var report FulfillmentReport
	err := i.db.WithContext(ctx).First(&report, id).Error
	if err != nil {
		return nil, err
	}
	return &report, nil
}

func (i *reportImpl) FindByUniqueKey(ctx context.Context, businessID, appointmentID, petID int64,
	careType int32, serviceDate string) (*FulfillmentReport, error) {
	var report FulfillmentReport
	err := i.db.WithContext(ctx).Where(
		"business_id = ? AND appointment_id = ? AND pet_id = ? AND care_type = ? AND service_date = ?",
		businessID, appointmentID, petID, careType, serviceDate).First(&report).Error
	if err != nil {
		return nil, err
	}
	return &report, nil
}

func (i *reportImpl) Upsert(ctx context.Context, report *FulfillmentReport) error {
	// 尝试根据唯一键查找现有记录
	existing, err := i.FindByUniqueKey(ctx, report.BusinessID,
		report.AppointmentID, report.PetID, report.CareType, report.ServiceDate.Format("2006-01-02"))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录不存在，创建新记录
			return i.Create(ctx, report)
		}
		return err
	}

	// 记录存在，更新现有记录
	report.ID = existing.ID
	report.CreateTime = existing.CreateTime // 保持原始创建时间
	return i.Update(ctx, report)
}

func (i *reportImpl) FindByUniqueKeys(ctx context.Context, uniqueKeys []ReportUniqueKey) ([]*FulfillmentReport, error) {
	if len(uniqueKeys) == 0 {
		return []*FulfillmentReport{}, nil
	}

	var reports []*FulfillmentReport
	query := i.db.WithContext(ctx)

	// 构建 OR 条件查询
	var conditions []string
	var args []interface{}

	for _, uniqueKey := range uniqueKeys {
		condition := "(business_id = ? AND appointment_id = ? AND pet_id = ? AND care_type = ? AND service_date = ?)"
		args = append(
			args,
			uniqueKey.BusinessID,
			uniqueKey.AppointmentID,
			uniqueKey.PetID,
			uniqueKey.CareType,
			uniqueKey.ServiceDate,
		)
		conditions = append(conditions, condition)
	}

	if len(conditions) > 0 {
		query = query.Where(strings.Join(conditions, " OR "), args...)
	}

	// 执行查询
	if err := query.Find(&reports).Error; err != nil {
		return nil, fmt.Errorf("failed to query reports by unique keys: %w", err)
	}

	return reports, nil
}
