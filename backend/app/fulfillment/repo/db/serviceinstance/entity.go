package serviceinstance

import (
	"time"
)

// 列名常量定义，避免手搓列名
const (
	ColumnBusinessID       = "business_id"
	ColumnCompanyID        = "company_id"
	ColumnCustomerID       = "customer_id"
	ColumnPetID            = "pet_id"
	ColumnCareType         = "care_type"
	ColumnDateType         = "date_type"
	ColumnRootID           = "root_id"
	ColumnParentID         = "parent_id"
	ColumnCreatedAt        = "created_at"
	ColumnUpdatedAt        = "updated_at"
	ColumnStartDate        = "start_date"
	ColumnEndDate          = "end_date"
	ColumnAppointmentID    = "appointment_id"
	ColumnServiceFactoryID = "service_factory_id"
	ColumnID               = "id"
)

type BaseParam struct {
	BusinessID     int32
	CompanyID      int32
	PaginationInfo *PaginationInfo
	StartTime      time.Time
	EndTime        time.Time
}

type Filter struct {
	PetIDs      []int64
	CustomerIDs []int64
	CareTypes   []int32
	DateTypes   []int32
	RootIDs     []int32
}

type PaginationInfo struct {
	Offset int32
	Limit  int32
}

type ServiceInstance struct {
	ID               int `gorm:"column:id;primaryKey;autoIncrement" json:"id" db:"id"`
	BusinessID       int `gorm:"column:business_id" json:"business_id" db:"business_id"`
	CustomerID       int `gorm:"column:customer_id" json:"customer_id" db:"customer_id"`
	CompanyID        int `gorm:"column:company_id" json:"company_id" db:"company_id"`
	AppointmentID    int `gorm:"column:appointment_id" json:"appointment_id" db:"appointment_id"`
	PetID            int `gorm:"column:pet_id" json:"pet_id" db:"pet_id"`
	CareType         int `gorm:"column:care_type" json:"care_type" db:"care_type"`
	DateType         int `gorm:"column:date_type" json:"date_type" db:"date_type"`
	ServiceFactoryID int `gorm:"column:service_factory_id" json:"service_factory_id" db:"service_factory_id"`

	// 树形结构
	ParentID     int `gorm:"column:parent_id" json:"parent_id" db:"parent_id"`
	RootParentID int `gorm:"column:root_parent_id" json:"root_parent_id" db:"root_parent_id"`

	StartDate time.Time `gorm:"column:start_date" json:"start_date" db:"start_date"`
	EndDate   time.Time `gorm:"column:end_date" json:"end_date" db:"end_date"`

	CreatedAt time.Time `gorm:"column:created_at" json:"created_at" db:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at" db:"updated_at"`
}

// TableName 设置表名
func (ServiceInstance) TableName() string {
	return "service_instance"
}
