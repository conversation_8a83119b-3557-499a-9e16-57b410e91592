package com.moego.common.utils;

import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.MonitorInfo;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @since  2021-01-25
 */
@Slf4j
public class ThreadDump {

    private static final String DUMP_FILE_PREFIX = "ThreadDump";
    private static final String DUMP_DIR = "/dump";

    private static String getDumpFileName(String name) {
        return String.format(
                "%s-%s-%s.txt", DUMP_FILE_PREFIX, new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date()), name);
    }

    public static void threadDump(String logPath, String name) {
        Path threadDumpFile = Paths.get(logPath + DUMP_DIR, getDumpFileName(name));
        StringBuilder sb = new StringBuilder(System.lineSeparator());
        ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
        /**
         * boolean lockedMonitors
         * boolean lockedSynchronizers: write locks appear in the "ownable synchronizers" list, read locks don't.
         */
        for (ThreadInfo threadInfo : threadMXBean.dumpAllThreads(true, true)) {
            sb.append(threadInfo.toString());
            StackTraceElement[] stackTrace = threadInfo.getStackTrace();
            // ThreadInfo default size when toString
            final int MAX_FRAMES = 8;
            for (int i = MAX_FRAMES; i < stackTrace.length && i >= MAX_FRAMES; i++) {
                StackTraceElement ste = stackTrace[i];
                sb.append("\tmoe at " + ste.toString());
                sb.append('\n');
                for (MonitorInfo mi : threadInfo.getLockedMonitors()) {
                    if (mi.getLockedStackDepth() == i) {
                        sb.append("\t-  locked " + mi);
                        sb.append('\n');
                    }
                }
            }
        }
        try {
            Path parent = threadDumpFile.getParent();
            Assert.notNull(parent, "thread dump file parent path is null");
            Files.createDirectories(parent);
            Files.write(threadDumpFile, sb.toString().getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("thread dump error", e);
        }
    }
}
