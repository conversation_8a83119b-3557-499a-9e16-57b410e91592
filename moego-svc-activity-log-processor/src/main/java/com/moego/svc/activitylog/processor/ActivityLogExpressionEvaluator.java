package com.moego.svc.activitylog.processor;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.expression.AnnotatedElementKey;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.context.expression.CachedExpressionEvaluator;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 */
public class ActivityLogExpressionEvaluator extends CachedExpressionEvaluator {

    private final Map<ExpressionKey, Expression> keyCache = new ConcurrentHashMap<>(64);

    /**
     * Create an {@link EvaluationContext} for the given method invocation.
     *
     * @param method      the method being invoked
     * @param args        the arguments to the method
     * @param target      the target object
     * @param targetClass the target class
     * @param result      the result of the method invocation
     * @param beanFactory the bean factory
     * @return {@link EvaluationContext}
     */
    public EvaluationContext createEvaluationContext(
            Method method,
            Object[] args,
            Object target,
            Class<?> targetClass,
            @Nullable Object result,
            @Nullable BeanFactory beanFactory) {
        RootObject rootObject = new RootObject(method, args, target, targetClass);
        MethodBasedEvaluationContext ctx =
                new MethodBasedEvaluationContext(rootObject, method, args, getParameterNameDiscoverer());
        ctx.setVariable("result", result);
        Optional.ofNullable(beanFactory).ifPresent(bf -> ctx.setBeanResolver(new BeanFactoryResolver(bf)));
        return ctx;
    }

    /**
     * Evaluate the given key expression.
     *
     * <p> e.g. {@code #params.id}, {@code #result.id}
     *
     * @param keyExpression key expression
     * @param methodKey     method key
     * @param evalContext   evaluation context
     * @return key
     */
    @Nullable
    public Object key(String keyExpression, AnnotatedElementKey methodKey, EvaluationContext evalContext) {
        return getExpression(this.keyCache, methodKey, keyExpression).getValue(evalContext);
    }
}
