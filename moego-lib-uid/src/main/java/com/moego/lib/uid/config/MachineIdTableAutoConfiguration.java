package com.moego.lib.uid.config;

import com.zaxxer.hikari.HikariDataSource;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.jdbc.JdbcMachineIdDistributor;
import me.ahoo.cosid.spring.boot.starter.machine.CosIdJdbcMachineIdDistributorAutoConfiguration;
import me.ahoo.cosid.spring.boot.starter.machine.CosIdMachineAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;

/**
 * 自动初始化 machine id 分配记录表
 */
@Slf4j
@AutoConfiguration(
        // 在成功装配 jdbcMachineIdDistributor 后执行
        after = CosIdJdbcMachineIdDistributorAutoConfiguration.class,
        // 在请求分配 machine id 之前执行
        before = CosIdMachineAutoConfiguration.class)
@ConditionalOnBean(JdbcMachineIdDistributor.class)
@ConditionalOnProperty(prefix = "cosid.jdbc", name = "auto-init", havingValue = "true")
public class MachineIdTableAutoConfiguration {

    public static final String MYSQL_SCHEMA = "sql/schema-mysql.sql";
    public static final String POSTGRESQL_SCHEMA = "sql/schema-postgresql.sql";
    public static final String H2_SCHEMA = "sql/schema-h2.sql";

    private static final Map<String, String> SCHEMA_PATHS = Map.of(
            "com.mysql.cj.jdbc.Driver", MYSQL_SCHEMA,
            "com.mysql.jdbc.Driver", MYSQL_SCHEMA,
            "org.postgresql.Driver", POSTGRESQL_SCHEMA,
            "org.h2.Driver", H2_SCHEMA);

    @SneakyThrows
    public MachineIdTableAutoConfiguration(HikariDataSource dataSource) {
        var driverClassName = dataSource.getDriverClassName();
        String schemaPath;

        if ("software.amazon.jdbc.Driver".equals(driverClassName)) {
            String jdbcUrl = dataSource.getJdbcUrl();
            String dbType = determineDatabaseType(jdbcUrl);
            schemaPath = switch (dbType) {
                case "mysql" -> MYSQL_SCHEMA;
                case "postgresql" -> POSTGRESQL_SCHEMA;
                case "h2" -> H2_SCHEMA;
                default -> throw new IllegalArgumentException("Unsupported database type: " + dbType);};
        } else {
            schemaPath = SCHEMA_PATHS.get(driverClassName);
            if (schemaPath == null) {
                throw new IllegalArgumentException("Unsupported driver class name: " + driverClassName);
            }
        }

        executeSchemaScript(dataSource, schemaPath);
        log.info("Initialize schema successfully, driver: %s, schema path: %s".formatted(driverClassName, schemaPath));
    }

    @SneakyThrows
    private void executeSchemaScript(HikariDataSource dataSource, String schemaPath) {
        ClassPathResource resource = new ClassPathResource(schemaPath);
        var sql = resource.getContentAsString(StandardCharsets.UTF_8);
        try (Connection connection = dataSource.getConnection();
                PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.execute();
        }
    }

    /**
     * 使用正则表达式解析 JDBC URL 中的数据库类型
     */
    public static String determineDatabaseType(String jdbcUrl) {
        Pattern pattern = Pattern.compile(":([a-zA-Z0-9]+):");
        Matcher matcher = pattern.matcher(jdbcUrl);
        if (matcher.find()) {
            String dbType = matcher.group(1).toLowerCase();
            if (dbType.equals("mysql") || dbType.equals("postgresql") || dbType.equals("h2")) {
                return dbType;
            }
        }
        throw new IllegalArgumentException("Unsupported or undetectable database type in JDBC URL: " + jdbcUrl);
    }
}
