// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/business_customer/v1/business_pet_fixed_service.proto

package businesscustomersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get pet fixed request
type GetPetFixedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *GetPetFixedRequest) Reset() {
	*x = GetPetFixedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetFixedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetFixedRequest) ProtoMessage() {}

func (x *GetPetFixedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetFixedRequest.ProtoReflect.Descriptor instead.
func (*GetPetFixedRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetPetFixedRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPetFixedRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetPetFixedRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// get pet fixed response
type GetPetFixedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed
	Fixed *v1.BusinessPetFixedModel `protobuf:"bytes,1,opt,name=fixed,proto3" json:"fixed,omitempty"`
}

func (x *GetPetFixedResponse) Reset() {
	*x = GetPetFixedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetFixedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetFixedResponse) ProtoMessage() {}

func (x *GetPetFixedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetFixedResponse.ProtoReflect.Descriptor instead.
func (*GetPetFixedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetPetFixedResponse) GetFixed() *v1.BusinessPetFixedModel {
	if x != nil {
		return x.Fixed
	}
	return nil
}

// list pet fixed request
type ListPetFixedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *ListPetFixedRequest) Reset() {
	*x = ListPetFixedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetFixedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetFixedRequest) ProtoMessage() {}

func (x *ListPetFixedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetFixedRequest.ProtoReflect.Descriptor instead.
func (*ListPetFixedRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListPetFixedRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListPetFixedRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// list pet fixed response
type ListPetFixedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed list
	Fixeds []*v1.BusinessPetFixedModel `protobuf:"bytes,1,rep,name=fixeds,proto3" json:"fixeds,omitempty"`
}

func (x *ListPetFixedResponse) Reset() {
	*x = ListPetFixedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetFixedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetFixedResponse) ProtoMessage() {}

func (x *ListPetFixedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetFixedResponse.ProtoReflect.Descriptor instead.
func (*ListPetFixedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListPetFixedResponse) GetFixeds() []*v1.BusinessPetFixedModel {
	if x != nil {
		return x.Fixeds
	}
	return nil
}

// create pet fixed request
type CreatePetFixedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pet fixed
	Fixed *v1.BusinessPetFixedCreateDef `protobuf:"bytes,3,opt,name=fixed,proto3" json:"fixed,omitempty"`
}

func (x *CreatePetFixedRequest) Reset() {
	*x = CreatePetFixedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetFixedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetFixedRequest) ProtoMessage() {}

func (x *CreatePetFixedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetFixedRequest.ProtoReflect.Descriptor instead.
func (*CreatePetFixedRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePetFixedRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreatePetFixedRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *CreatePetFixedRequest) GetFixed() *v1.BusinessPetFixedCreateDef {
	if x != nil {
		return x.Fixed
	}
	return nil
}

// create pet fixed response
type CreatePetFixedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed
	Fixed *v1.BusinessPetFixedModel `protobuf:"bytes,1,opt,name=fixed,proto3" json:"fixed,omitempty"`
}

func (x *CreatePetFixedResponse) Reset() {
	*x = CreatePetFixedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetFixedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetFixedResponse) ProtoMessage() {}

func (x *CreatePetFixedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetFixedResponse.ProtoReflect.Descriptor instead.
func (*CreatePetFixedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePetFixedResponse) GetFixed() *v1.BusinessPetFixedModel {
	if x != nil {
		return x.Fixed
	}
	return nil
}

// update pet fixed request
type UpdatePetFixedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pet fixed
	Fixed *v1.BusinessPetFixedUpdateDef `protobuf:"bytes,4,opt,name=fixed,proto3" json:"fixed,omitempty"`
}

func (x *UpdatePetFixedRequest) Reset() {
	*x = UpdatePetFixedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetFixedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetFixedRequest) ProtoMessage() {}

func (x *UpdatePetFixedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetFixedRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetFixedRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePetFixedRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetFixedRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdatePetFixedRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *UpdatePetFixedRequest) GetFixed() *v1.BusinessPetFixedUpdateDef {
	if x != nil {
		return x.Fixed
	}
	return nil
}

// update pet fixed response
type UpdatePetFixedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePetFixedResponse) Reset() {
	*x = UpdatePetFixedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetFixedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetFixedResponse) ProtoMessage() {}

func (x *UpdatePetFixedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetFixedResponse.ProtoReflect.Descriptor instead.
func (*UpdatePetFixedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{7}
}

// sort pet fixed request
type SortPetFixedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed id list, should contain all pet fixed ids for the company / business
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *SortPetFixedRequest) Reset() {
	*x = SortPetFixedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetFixedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetFixedRequest) ProtoMessage() {}

func (x *SortPetFixedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetFixedRequest.ProtoReflect.Descriptor instead.
func (*SortPetFixedRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{8}
}

func (x *SortPetFixedRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *SortPetFixedRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SortPetFixedRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// sort pet fixed response
type SortPetFixedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetFixedResponse) Reset() {
	*x = SortPetFixedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetFixedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetFixedResponse) ProtoMessage() {}

func (x *SortPetFixedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetFixedResponse.ProtoReflect.Descriptor instead.
func (*SortPetFixedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{9}
}

// delete pet fixed request
type DeletePetFixedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet fixed id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *DeletePetFixedRequest) Reset() {
	*x = DeletePetFixedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetFixedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetFixedRequest) ProtoMessage() {}

func (x *DeletePetFixedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetFixedRequest.ProtoReflect.Descriptor instead.
func (*DeletePetFixedRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{10}
}

func (x *DeletePetFixedRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeletePetFixedRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeletePetFixedRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// delete pet fixed response
type DeletePetFixedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetFixedResponse) Reset() {
	*x = DeletePetFixedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetFixedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetFixedResponse) ProtoMessage() {}

func (x *DeletePetFixedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetFixedResponse.ProtoReflect.Descriptor instead.
func (*DeletePetFixedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP(), []int{11}
}

var File_moego_service_business_customer_v1_business_pet_fixed_service_proto protoreflect.FileDescriptor

var file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x94, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x65,
	0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x65, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x22, 0x7c, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x22, 0x68, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x06, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x66, 0x69, 0x78, 0x65, 0x64, 0x73, 0x22, 0xdc, 0x01, 0x0a,
	0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a,
	0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x68, 0x0a, 0x16, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x22, 0xf5, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x5c, 0x0a, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x18, 0x0a,
	0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x13, 0x53, 0x6f, 0x72, 0x74,
	0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64,
	0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x53, 0x6f, 0x72, 0x74,
	0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x97, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x32, 0xbf, 0x06, 0x0a, 0x17, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x7e, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x81, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87,
	0x01, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x0c, 0x53, 0x6f, 0x72,
	0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a,
	0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x12,
	0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x9d, 0x01, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x6d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescOnce sync.Once
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescData = file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDesc
)

func file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescGZIP() []byte {
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescOnce.Do(func() {
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescData)
	})
	return file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDescData
}

var file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_service_business_customer_v1_business_pet_fixed_service_proto_goTypes = []interface{}{
	(*GetPetFixedRequest)(nil),           // 0: moego.service.business_customer.v1.GetPetFixedRequest
	(*GetPetFixedResponse)(nil),          // 1: moego.service.business_customer.v1.GetPetFixedResponse
	(*ListPetFixedRequest)(nil),          // 2: moego.service.business_customer.v1.ListPetFixedRequest
	(*ListPetFixedResponse)(nil),         // 3: moego.service.business_customer.v1.ListPetFixedResponse
	(*CreatePetFixedRequest)(nil),        // 4: moego.service.business_customer.v1.CreatePetFixedRequest
	(*CreatePetFixedResponse)(nil),       // 5: moego.service.business_customer.v1.CreatePetFixedResponse
	(*UpdatePetFixedRequest)(nil),        // 6: moego.service.business_customer.v1.UpdatePetFixedRequest
	(*UpdatePetFixedResponse)(nil),       // 7: moego.service.business_customer.v1.UpdatePetFixedResponse
	(*SortPetFixedRequest)(nil),          // 8: moego.service.business_customer.v1.SortPetFixedRequest
	(*SortPetFixedResponse)(nil),         // 9: moego.service.business_customer.v1.SortPetFixedResponse
	(*DeletePetFixedRequest)(nil),        // 10: moego.service.business_customer.v1.DeletePetFixedRequest
	(*DeletePetFixedResponse)(nil),       // 11: moego.service.business_customer.v1.DeletePetFixedResponse
	(*v1.BusinessPetFixedModel)(nil),     // 12: moego.models.business_customer.v1.BusinessPetFixedModel
	(*v1.BusinessPetFixedCreateDef)(nil), // 13: moego.models.business_customer.v1.BusinessPetFixedCreateDef
	(*v1.BusinessPetFixedUpdateDef)(nil), // 14: moego.models.business_customer.v1.BusinessPetFixedUpdateDef
}
var file_moego_service_business_customer_v1_business_pet_fixed_service_proto_depIdxs = []int32{
	12, // 0: moego.service.business_customer.v1.GetPetFixedResponse.fixed:type_name -> moego.models.business_customer.v1.BusinessPetFixedModel
	12, // 1: moego.service.business_customer.v1.ListPetFixedResponse.fixeds:type_name -> moego.models.business_customer.v1.BusinessPetFixedModel
	13, // 2: moego.service.business_customer.v1.CreatePetFixedRequest.fixed:type_name -> moego.models.business_customer.v1.BusinessPetFixedCreateDef
	12, // 3: moego.service.business_customer.v1.CreatePetFixedResponse.fixed:type_name -> moego.models.business_customer.v1.BusinessPetFixedModel
	14, // 4: moego.service.business_customer.v1.UpdatePetFixedRequest.fixed:type_name -> moego.models.business_customer.v1.BusinessPetFixedUpdateDef
	0,  // 5: moego.service.business_customer.v1.BusinessPetFixedService.GetPetFixed:input_type -> moego.service.business_customer.v1.GetPetFixedRequest
	2,  // 6: moego.service.business_customer.v1.BusinessPetFixedService.ListPetFixed:input_type -> moego.service.business_customer.v1.ListPetFixedRequest
	4,  // 7: moego.service.business_customer.v1.BusinessPetFixedService.CreatePetFixed:input_type -> moego.service.business_customer.v1.CreatePetFixedRequest
	6,  // 8: moego.service.business_customer.v1.BusinessPetFixedService.UpdatePetFixed:input_type -> moego.service.business_customer.v1.UpdatePetFixedRequest
	8,  // 9: moego.service.business_customer.v1.BusinessPetFixedService.SortPetFixed:input_type -> moego.service.business_customer.v1.SortPetFixedRequest
	10, // 10: moego.service.business_customer.v1.BusinessPetFixedService.DeletePetFixed:input_type -> moego.service.business_customer.v1.DeletePetFixedRequest
	1,  // 11: moego.service.business_customer.v1.BusinessPetFixedService.GetPetFixed:output_type -> moego.service.business_customer.v1.GetPetFixedResponse
	3,  // 12: moego.service.business_customer.v1.BusinessPetFixedService.ListPetFixed:output_type -> moego.service.business_customer.v1.ListPetFixedResponse
	5,  // 13: moego.service.business_customer.v1.BusinessPetFixedService.CreatePetFixed:output_type -> moego.service.business_customer.v1.CreatePetFixedResponse
	7,  // 14: moego.service.business_customer.v1.BusinessPetFixedService.UpdatePetFixed:output_type -> moego.service.business_customer.v1.UpdatePetFixedResponse
	9,  // 15: moego.service.business_customer.v1.BusinessPetFixedService.SortPetFixed:output_type -> moego.service.business_customer.v1.SortPetFixedResponse
	11, // 16: moego.service.business_customer.v1.BusinessPetFixedService.DeletePetFixed:output_type -> moego.service.business_customer.v1.DeletePetFixedResponse
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_service_business_customer_v1_business_pet_fixed_service_proto_init() }
func file_moego_service_business_customer_v1_business_pet_fixed_service_proto_init() {
	if File_moego_service_business_customer_v1_business_pet_fixed_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetFixedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetFixedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetFixedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetFixedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetFixedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetFixedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetFixedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetFixedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetFixedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetFixedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetFixedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetFixedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_business_customer_v1_business_pet_fixed_service_proto_goTypes,
		DependencyIndexes: file_moego_service_business_customer_v1_business_pet_fixed_service_proto_depIdxs,
		MessageInfos:      file_moego_service_business_customer_v1_business_pet_fixed_service_proto_msgTypes,
	}.Build()
	File_moego_service_business_customer_v1_business_pet_fixed_service_proto = out.File
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_rawDesc = nil
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_goTypes = nil
	file_moego_service_business_customer_v1_business_pet_fixed_service_proto_depIdxs = nil
}
