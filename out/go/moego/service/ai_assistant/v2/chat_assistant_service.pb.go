// @since 2024-07-11 14:21:59
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/ai_assistant/v2/chat_assistant_service.proto

package aiassistantsvcpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// process new message request
type ProcessMessageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// the customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// the last message id
	LastMessageId int64 `protobuf:"varint,4,opt,name=last_message_id,json=lastMessageId,proto3" json:"last_message_id,omitempty"`
}

func (x *ProcessMessageRequest) Reset() {
	*x = ProcessMessageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v2_chat_assistant_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessMessageRequest) ProtoMessage() {}

func (x *ProcessMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v2_chat_assistant_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessMessageRequest.ProtoReflect.Descriptor instead.
func (*ProcessMessageRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessMessageRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ProcessMessageRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ProcessMessageRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ProcessMessageRequest) GetLastMessageId() int64 {
	if x != nil {
		return x.LastMessageId
	}
	return 0
}

// process new message response
type ProcessMessageAsyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ProcessMessageAsyncResponse) Reset() {
	*x = ProcessMessageAsyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_ai_assistant_v2_chat_assistant_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessMessageAsyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessMessageAsyncResponse) ProtoMessage() {}

func (x *ProcessMessageAsyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_ai_assistant_v2_chat_assistant_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessMessageAsyncResponse.ProtoReflect.Descriptor instead.
func (*ProcessMessageAsyncResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescGZIP(), []int{1}
}

var File_moego_service_ai_assistant_v2_chat_assistant_service_proto protoreflect.FileDescriptor

var file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x22, 0xa0, 0x01, 0x0a, 0x15,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x1d,
	0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xa0, 0x01,
	0x0a, 0x14, 0x43, 0x68, 0x61, 0x74, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x8e, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73,
	0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x32,
	0x3b, 0x61, 0x69, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescOnce sync.Once
	file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescData = file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDesc
)

func file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescGZIP() []byte {
	file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescOnce.Do(func() {
		file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescData)
	})
	return file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDescData
}

var file_moego_service_ai_assistant_v2_chat_assistant_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_ai_assistant_v2_chat_assistant_service_proto_goTypes = []interface{}{
	(*ProcessMessageRequest)(nil),       // 0: moego.service.ai_assistant.v2.ProcessMessageRequest
	(*ProcessMessageAsyncResponse)(nil), // 1: moego.service.ai_assistant.v2.ProcessMessageAsyncResponse
}
var file_moego_service_ai_assistant_v2_chat_assistant_service_proto_depIdxs = []int32{
	0, // 0: moego.service.ai_assistant.v2.ChatAssistantService.ProcessMessageAsync:input_type -> moego.service.ai_assistant.v2.ProcessMessageRequest
	1, // 1: moego.service.ai_assistant.v2.ChatAssistantService.ProcessMessageAsync:output_type -> moego.service.ai_assistant.v2.ProcessMessageAsyncResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_service_ai_assistant_v2_chat_assistant_service_proto_init() }
func file_moego_service_ai_assistant_v2_chat_assistant_service_proto_init() {
	if File_moego_service_ai_assistant_v2_chat_assistant_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_ai_assistant_v2_chat_assistant_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessMessageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_ai_assistant_v2_chat_assistant_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessMessageAsyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_ai_assistant_v2_chat_assistant_service_proto_goTypes,
		DependencyIndexes: file_moego_service_ai_assistant_v2_chat_assistant_service_proto_depIdxs,
		MessageInfos:      file_moego_service_ai_assistant_v2_chat_assistant_service_proto_msgTypes,
	}.Build()
	File_moego_service_ai_assistant_v2_chat_assistant_service_proto = out.File
	file_moego_service_ai_assistant_v2_chat_assistant_service_proto_rawDesc = nil
	file_moego_service_ai_assistant_v2_chat_assistant_service_proto_goTypes = nil
	file_moego_service_ai_assistant_v2_chat_assistant_service_proto_depIdxs = nil
}
