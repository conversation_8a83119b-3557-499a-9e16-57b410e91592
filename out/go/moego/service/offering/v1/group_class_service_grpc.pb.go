// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/offering/v1/group_class_service.proto

package offeringsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GroupClassServiceClient is the client API for GroupClassService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroupClassServiceClient interface {
	// create sessions
	CreateInstanceAndSessions(ctx context.Context, in *CreateInstanceAndSessionsRequest, opts ...grpc.CallOption) (*CreateInstanceAndSessionsResponse, error)
	// count group class status
	CountInstancesGroupByStatus(ctx context.Context, in *CountInstancesGroupByStatusRequest, opts ...grpc.CallOption) (*CountInstancesGroupByStatusResponse, error)
	// count group class statuses by group class id
	CountInstancesGroupByClass(ctx context.Context, in *CountInstancesGroupByClassRequest, opts ...grpc.CallOption) (*CountInstancesGroupByClassResponse, error)
	// GetTrainingClassBatch
	GetInstance(ctx context.Context, in *GetInstanceRequest, opts ...grpc.CallOption) (*GetInstanceResponse, error)
	// ListTrainingClassBatch
	ListInstances(ctx context.Context, in *ListInstancesRequest, opts ...grpc.CallOption) (*ListInstancesResponse, error)
	// UpdateGroupClassInstance
	UpdateInstanceAndSessions(ctx context.Context, in *UpdateInstanceAndSessionsRequest, opts ...grpc.CallOption) (*UpdateInstanceAndSessionsResponse, error)
	// DeleteInstanceAndSessions
	DeleteInstanceAndSessions(ctx context.Context, in *DeleteInstanceAndSessionsRequest, opts ...grpc.CallOption) (*DeleteInstanceAndSessionsResponse, error)
	// edit session
	UpdateSession(ctx context.Context, in *UpdateSessionRequest, opts ...grpc.CallOption) (*UpdateSessionResponse, error)
	// list sessions
	ListSessions(ctx context.Context, in *ListSessionsRequest, opts ...grpc.CallOption) (*ListSessionsResponse, error)
	// refresh status
	TaskRefreshInstanceStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type groupClassServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroupClassServiceClient(cc grpc.ClientConnInterface) GroupClassServiceClient {
	return &groupClassServiceClient{cc}
}

func (c *groupClassServiceClient) CreateInstanceAndSessions(ctx context.Context, in *CreateInstanceAndSessionsRequest, opts ...grpc.CallOption) (*CreateInstanceAndSessionsResponse, error) {
	out := new(CreateInstanceAndSessionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/CreateInstanceAndSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) CountInstancesGroupByStatus(ctx context.Context, in *CountInstancesGroupByStatusRequest, opts ...grpc.CallOption) (*CountInstancesGroupByStatusResponse, error) {
	out := new(CountInstancesGroupByStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/CountInstancesGroupByStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) CountInstancesGroupByClass(ctx context.Context, in *CountInstancesGroupByClassRequest, opts ...grpc.CallOption) (*CountInstancesGroupByClassResponse, error) {
	out := new(CountInstancesGroupByClassResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/CountInstancesGroupByClass", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) GetInstance(ctx context.Context, in *GetInstanceRequest, opts ...grpc.CallOption) (*GetInstanceResponse, error) {
	out := new(GetInstanceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/GetInstance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) ListInstances(ctx context.Context, in *ListInstancesRequest, opts ...grpc.CallOption) (*ListInstancesResponse, error) {
	out := new(ListInstancesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/ListInstances", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) UpdateInstanceAndSessions(ctx context.Context, in *UpdateInstanceAndSessionsRequest, opts ...grpc.CallOption) (*UpdateInstanceAndSessionsResponse, error) {
	out := new(UpdateInstanceAndSessionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/UpdateInstanceAndSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) DeleteInstanceAndSessions(ctx context.Context, in *DeleteInstanceAndSessionsRequest, opts ...grpc.CallOption) (*DeleteInstanceAndSessionsResponse, error) {
	out := new(DeleteInstanceAndSessionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/DeleteInstanceAndSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) UpdateSession(ctx context.Context, in *UpdateSessionRequest, opts ...grpc.CallOption) (*UpdateSessionResponse, error) {
	out := new(UpdateSessionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/UpdateSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) ListSessions(ctx context.Context, in *ListSessionsRequest, opts ...grpc.CallOption) (*ListSessionsResponse, error) {
	out := new(ListSessionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/ListSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) TaskRefreshInstanceStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.GroupClassService/TaskRefreshInstanceStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupClassServiceServer is the server API for GroupClassService service.
// All implementations must embed UnimplementedGroupClassServiceServer
// for forward compatibility
type GroupClassServiceServer interface {
	// create sessions
	CreateInstanceAndSessions(context.Context, *CreateInstanceAndSessionsRequest) (*CreateInstanceAndSessionsResponse, error)
	// count group class status
	CountInstancesGroupByStatus(context.Context, *CountInstancesGroupByStatusRequest) (*CountInstancesGroupByStatusResponse, error)
	// count group class statuses by group class id
	CountInstancesGroupByClass(context.Context, *CountInstancesGroupByClassRequest) (*CountInstancesGroupByClassResponse, error)
	// GetTrainingClassBatch
	GetInstance(context.Context, *GetInstanceRequest) (*GetInstanceResponse, error)
	// ListTrainingClassBatch
	ListInstances(context.Context, *ListInstancesRequest) (*ListInstancesResponse, error)
	// UpdateGroupClassInstance
	UpdateInstanceAndSessions(context.Context, *UpdateInstanceAndSessionsRequest) (*UpdateInstanceAndSessionsResponse, error)
	// DeleteInstanceAndSessions
	DeleteInstanceAndSessions(context.Context, *DeleteInstanceAndSessionsRequest) (*DeleteInstanceAndSessionsResponse, error)
	// edit session
	UpdateSession(context.Context, *UpdateSessionRequest) (*UpdateSessionResponse, error)
	// list sessions
	ListSessions(context.Context, *ListSessionsRequest) (*ListSessionsResponse, error)
	// refresh status
	TaskRefreshInstanceStatus(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedGroupClassServiceServer()
}

// UnimplementedGroupClassServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGroupClassServiceServer struct {
}

func (UnimplementedGroupClassServiceServer) CreateInstanceAndSessions(context.Context, *CreateInstanceAndSessionsRequest) (*CreateInstanceAndSessionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInstanceAndSessions not implemented")
}
func (UnimplementedGroupClassServiceServer) CountInstancesGroupByStatus(context.Context, *CountInstancesGroupByStatusRequest) (*CountInstancesGroupByStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountInstancesGroupByStatus not implemented")
}
func (UnimplementedGroupClassServiceServer) CountInstancesGroupByClass(context.Context, *CountInstancesGroupByClassRequest) (*CountInstancesGroupByClassResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountInstancesGroupByClass not implemented")
}
func (UnimplementedGroupClassServiceServer) GetInstance(context.Context, *GetInstanceRequest) (*GetInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstance not implemented")
}
func (UnimplementedGroupClassServiceServer) ListInstances(context.Context, *ListInstancesRequest) (*ListInstancesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstances not implemented")
}
func (UnimplementedGroupClassServiceServer) UpdateInstanceAndSessions(context.Context, *UpdateInstanceAndSessionsRequest) (*UpdateInstanceAndSessionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstanceAndSessions not implemented")
}
func (UnimplementedGroupClassServiceServer) DeleteInstanceAndSessions(context.Context, *DeleteInstanceAndSessionsRequest) (*DeleteInstanceAndSessionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInstanceAndSessions not implemented")
}
func (UnimplementedGroupClassServiceServer) UpdateSession(context.Context, *UpdateSessionRequest) (*UpdateSessionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSession not implemented")
}
func (UnimplementedGroupClassServiceServer) ListSessions(context.Context, *ListSessionsRequest) (*ListSessionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSessions not implemented")
}
func (UnimplementedGroupClassServiceServer) TaskRefreshInstanceStatus(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskRefreshInstanceStatus not implemented")
}
func (UnimplementedGroupClassServiceServer) mustEmbedUnimplementedGroupClassServiceServer() {}

// UnsafeGroupClassServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroupClassServiceServer will
// result in compilation errors.
type UnsafeGroupClassServiceServer interface {
	mustEmbedUnimplementedGroupClassServiceServer()
}

func RegisterGroupClassServiceServer(s grpc.ServiceRegistrar, srv GroupClassServiceServer) {
	s.RegisterService(&GroupClassService_ServiceDesc, srv)
}

func _GroupClassService_CreateInstanceAndSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInstanceAndSessionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).CreateInstanceAndSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/CreateInstanceAndSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).CreateInstanceAndSessions(ctx, req.(*CreateInstanceAndSessionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_CountInstancesGroupByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountInstancesGroupByStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).CountInstancesGroupByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/CountInstancesGroupByStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).CountInstancesGroupByStatus(ctx, req.(*CountInstancesGroupByStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_CountInstancesGroupByClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountInstancesGroupByClassRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).CountInstancesGroupByClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/CountInstancesGroupByClass",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).CountInstancesGroupByClass(ctx, req.(*CountInstancesGroupByClassRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_GetInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).GetInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/GetInstance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).GetInstance(ctx, req.(*GetInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_ListInstances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstancesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).ListInstances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/ListInstances",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).ListInstances(ctx, req.(*ListInstancesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_UpdateInstanceAndSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceAndSessionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).UpdateInstanceAndSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/UpdateInstanceAndSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).UpdateInstanceAndSessions(ctx, req.(*UpdateInstanceAndSessionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_DeleteInstanceAndSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstanceAndSessionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).DeleteInstanceAndSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/DeleteInstanceAndSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).DeleteInstanceAndSessions(ctx, req.(*DeleteInstanceAndSessionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_UpdateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).UpdateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/UpdateSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).UpdateSession(ctx, req.(*UpdateSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_ListSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSessionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).ListSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/ListSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).ListSessions(ctx, req.(*ListSessionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_TaskRefreshInstanceStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).TaskRefreshInstanceStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.GroupClassService/TaskRefreshInstanceStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).TaskRefreshInstanceStatus(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// GroupClassService_ServiceDesc is the grpc.ServiceDesc for GroupClassService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroupClassService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.offering.v1.GroupClassService",
	HandlerType: (*GroupClassServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateInstanceAndSessions",
			Handler:    _GroupClassService_CreateInstanceAndSessions_Handler,
		},
		{
			MethodName: "CountInstancesGroupByStatus",
			Handler:    _GroupClassService_CountInstancesGroupByStatus_Handler,
		},
		{
			MethodName: "CountInstancesGroupByClass",
			Handler:    _GroupClassService_CountInstancesGroupByClass_Handler,
		},
		{
			MethodName: "GetInstance",
			Handler:    _GroupClassService_GetInstance_Handler,
		},
		{
			MethodName: "ListInstances",
			Handler:    _GroupClassService_ListInstances_Handler,
		},
		{
			MethodName: "UpdateInstanceAndSessions",
			Handler:    _GroupClassService_UpdateInstanceAndSessions_Handler,
		},
		{
			MethodName: "DeleteInstanceAndSessions",
			Handler:    _GroupClassService_DeleteInstanceAndSessions_Handler,
		},
		{
			MethodName: "UpdateSession",
			Handler:    _GroupClassService_UpdateSession_Handler,
		},
		{
			MethodName: "ListSessions",
			Handler:    _GroupClassService_ListSessions_Handler,
		},
		{
			MethodName: "TaskRefreshInstanceStatus",
			Handler:    _GroupClassService_TaskRefreshInstanceStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/offering/v1/group_class_service.proto",
}
