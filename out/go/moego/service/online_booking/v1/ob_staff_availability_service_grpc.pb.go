// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/ob_staff_availability_service.proto

package onlinebookingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OBStaffAvailabilityServiceClient is the client API for OBStaffAvailabilityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OBStaffAvailabilityServiceClient interface {
	// get staff available
	GetStaffAvailability(ctx context.Context, in *GetStaffAvailabilityRequest, opts ...grpc.CallOption) (*GetStaffAvailabilityResponse, error)
	// update staff available
	UpdateStaffAvailability(ctx context.Context, in *UpdateStaffAvailabilityRequest, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResponse, error)
	// get staff availability status
	GetStaffAvailabilityStatus(ctx context.Context, in *GetStaffAvailabilityStatusRequest, opts ...grpc.CallOption) (*GetStaffAvailabilityStatusResponse, error)
}

type oBStaffAvailabilityServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOBStaffAvailabilityServiceClient(cc grpc.ClientConnInterface) OBStaffAvailabilityServiceClient {
	return &oBStaffAvailabilityServiceClient{cc}
}

func (c *oBStaffAvailabilityServiceClient) GetStaffAvailability(ctx context.Context, in *GetStaffAvailabilityRequest, opts ...grpc.CallOption) (*GetStaffAvailabilityResponse, error) {
	out := new(GetStaffAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBStaffAvailabilityService/GetStaffAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBStaffAvailabilityServiceClient) UpdateStaffAvailability(ctx context.Context, in *UpdateStaffAvailabilityRequest, opts ...grpc.CallOption) (*UpdateStaffAvailabilityResponse, error) {
	out := new(UpdateStaffAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBStaffAvailabilityService/UpdateStaffAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBStaffAvailabilityServiceClient) GetStaffAvailabilityStatus(ctx context.Context, in *GetStaffAvailabilityStatusRequest, opts ...grpc.CallOption) (*GetStaffAvailabilityStatusResponse, error) {
	out := new(GetStaffAvailabilityStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBStaffAvailabilityService/GetStaffAvailabilityStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OBStaffAvailabilityServiceServer is the server API for OBStaffAvailabilityService service.
// All implementations must embed UnimplementedOBStaffAvailabilityServiceServer
// for forward compatibility
type OBStaffAvailabilityServiceServer interface {
	// get staff available
	GetStaffAvailability(context.Context, *GetStaffAvailabilityRequest) (*GetStaffAvailabilityResponse, error)
	// update staff available
	UpdateStaffAvailability(context.Context, *UpdateStaffAvailabilityRequest) (*UpdateStaffAvailabilityResponse, error)
	// get staff availability status
	GetStaffAvailabilityStatus(context.Context, *GetStaffAvailabilityStatusRequest) (*GetStaffAvailabilityStatusResponse, error)
	mustEmbedUnimplementedOBStaffAvailabilityServiceServer()
}

// UnimplementedOBStaffAvailabilityServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOBStaffAvailabilityServiceServer struct {
}

func (UnimplementedOBStaffAvailabilityServiceServer) GetStaffAvailability(context.Context, *GetStaffAvailabilityRequest) (*GetStaffAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffAvailability not implemented")
}
func (UnimplementedOBStaffAvailabilityServiceServer) UpdateStaffAvailability(context.Context, *UpdateStaffAvailabilityRequest) (*UpdateStaffAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStaffAvailability not implemented")
}
func (UnimplementedOBStaffAvailabilityServiceServer) GetStaffAvailabilityStatus(context.Context, *GetStaffAvailabilityStatusRequest) (*GetStaffAvailabilityStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffAvailabilityStatus not implemented")
}
func (UnimplementedOBStaffAvailabilityServiceServer) mustEmbedUnimplementedOBStaffAvailabilityServiceServer() {
}

// UnsafeOBStaffAvailabilityServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OBStaffAvailabilityServiceServer will
// result in compilation errors.
type UnsafeOBStaffAvailabilityServiceServer interface {
	mustEmbedUnimplementedOBStaffAvailabilityServiceServer()
}

func RegisterOBStaffAvailabilityServiceServer(s grpc.ServiceRegistrar, srv OBStaffAvailabilityServiceServer) {
	s.RegisterService(&OBStaffAvailabilityService_ServiceDesc, srv)
}

func _OBStaffAvailabilityService_GetStaffAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBStaffAvailabilityServiceServer).GetStaffAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBStaffAvailabilityService/GetStaffAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBStaffAvailabilityServiceServer).GetStaffAvailability(ctx, req.(*GetStaffAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBStaffAvailabilityService_UpdateStaffAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStaffAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBStaffAvailabilityServiceServer).UpdateStaffAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBStaffAvailabilityService/UpdateStaffAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBStaffAvailabilityServiceServer).UpdateStaffAvailability(ctx, req.(*UpdateStaffAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBStaffAvailabilityService_GetStaffAvailabilityStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffAvailabilityStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBStaffAvailabilityServiceServer).GetStaffAvailabilityStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBStaffAvailabilityService/GetStaffAvailabilityStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBStaffAvailabilityServiceServer).GetStaffAvailabilityStatus(ctx, req.(*GetStaffAvailabilityStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OBStaffAvailabilityService_ServiceDesc is the grpc.ServiceDesc for OBStaffAvailabilityService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OBStaffAvailabilityService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.OBStaffAvailabilityService",
	HandlerType: (*OBStaffAvailabilityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStaffAvailability",
			Handler:    _OBStaffAvailabilityService_GetStaffAvailability_Handler,
		},
		{
			MethodName: "UpdateStaffAvailability",
			Handler:    _OBStaffAvailabilityService_UpdateStaffAvailability_Handler,
		},
		{
			MethodName: "GetStaffAvailabilityStatus",
			Handler:    _OBStaffAvailabilityService_GetStaffAvailabilityStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/online_booking/v1/ob_staff_availability_service.proto",
}
