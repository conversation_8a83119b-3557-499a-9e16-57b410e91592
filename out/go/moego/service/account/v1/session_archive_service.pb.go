// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/account/v1/session_archive_service.proto

package accountsvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// session archive service request
type CreateSessionArchiveTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start id
	StartId int64 `protobuf:"varint,1,opt,name=start_id,json=startId,proto3" json:"start_id,omitempty"`
	// end id
	EndId int64 `protobuf:"varint,2,opt,name=end_id,json=endId,proto3" json:"end_id,omitempty"`
	// step
	Step int32 `protobuf:"varint,3,opt,name=step,proto3" json:"step,omitempty"`
	// max date
	MaxDate *date.Date `protobuf:"bytes,4,opt,name=max_date,json=maxDate,proto3" json:"max_date,omitempty"`
}

func (x *CreateSessionArchiveTaskRequest) Reset() {
	*x = CreateSessionArchiveTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_archive_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionArchiveTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionArchiveTaskRequest) ProtoMessage() {}

func (x *CreateSessionArchiveTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_archive_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionArchiveTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateSessionArchiveTaskRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_archive_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSessionArchiveTaskRequest) GetStartId() int64 {
	if x != nil {
		return x.StartId
	}
	return 0
}

func (x *CreateSessionArchiveTaskRequest) GetEndId() int64 {
	if x != nil {
		return x.EndId
	}
	return 0
}

func (x *CreateSessionArchiveTaskRequest) GetStep() int32 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (x *CreateSessionArchiveTaskRequest) GetMaxDate() *date.Date {
	if x != nil {
		return x.MaxDate
	}
	return nil
}

// session archive service response
type CreateSessionArchiveTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// task id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateSessionArchiveTaskResponse) Reset() {
	*x = CreateSessionArchiveTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_archive_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionArchiveTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionArchiveTaskResponse) ProtoMessage() {}

func (x *CreateSessionArchiveTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_archive_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionArchiveTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateSessionArchiveTaskResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_archive_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateSessionArchiveTaskResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// update session archive task status request
type UpdateSessionArchiveTaskStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// task id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateSessionArchiveTaskStatusRequest) Reset() {
	*x = UpdateSessionArchiveTaskStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_archive_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSessionArchiveTaskStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSessionArchiveTaskStatusRequest) ProtoMessage() {}

func (x *UpdateSessionArchiveTaskStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_archive_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSessionArchiveTaskStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateSessionArchiveTaskStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_archive_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateSessionArchiveTaskStatusRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSessionArchiveTaskStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// update session archive task status response
type UpdateSessionArchiveTaskStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateSessionArchiveTaskStatusResponse) Reset() {
	*x = UpdateSessionArchiveTaskStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_session_archive_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSessionArchiveTaskStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSessionArchiveTaskStatusResponse) ProtoMessage() {}

func (x *UpdateSessionArchiveTaskStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_session_archive_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSessionArchiveTaskStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateSessionArchiveTaskStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_session_archive_service_proto_rawDescGZIP(), []int{3}
}

var File_moego_service_account_v1_session_archive_service_proto protoreflect.FileDescriptor

var file_moego_service_account_v1_session_archive_service_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xba, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x65,
	0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x28, 0x00, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x73,
	0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x12, 0x36, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x44, 0x61, 0x74, 0x65,
	0x22, 0x32, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x4f, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x28, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32,
	0xd1, 0x02, 0x0a, 0x15, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69,
	0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x18, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69,
	0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41,
	0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa3, 0x01,
	0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41,
	0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_account_v1_session_archive_service_proto_rawDescOnce sync.Once
	file_moego_service_account_v1_session_archive_service_proto_rawDescData = file_moego_service_account_v1_session_archive_service_proto_rawDesc
)

func file_moego_service_account_v1_session_archive_service_proto_rawDescGZIP() []byte {
	file_moego_service_account_v1_session_archive_service_proto_rawDescOnce.Do(func() {
		file_moego_service_account_v1_session_archive_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_account_v1_session_archive_service_proto_rawDescData)
	})
	return file_moego_service_account_v1_session_archive_service_proto_rawDescData
}

var file_moego_service_account_v1_session_archive_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_account_v1_session_archive_service_proto_goTypes = []interface{}{
	(*CreateSessionArchiveTaskRequest)(nil),        // 0: moego.service.account.v1.CreateSessionArchiveTaskRequest
	(*CreateSessionArchiveTaskResponse)(nil),       // 1: moego.service.account.v1.CreateSessionArchiveTaskResponse
	(*UpdateSessionArchiveTaskStatusRequest)(nil),  // 2: moego.service.account.v1.UpdateSessionArchiveTaskStatusRequest
	(*UpdateSessionArchiveTaskStatusResponse)(nil), // 3: moego.service.account.v1.UpdateSessionArchiveTaskStatusResponse
	(*date.Date)(nil),                              // 4: google.type.Date
}
var file_moego_service_account_v1_session_archive_service_proto_depIdxs = []int32{
	4, // 0: moego.service.account.v1.CreateSessionArchiveTaskRequest.max_date:type_name -> google.type.Date
	0, // 1: moego.service.account.v1.SessionArchiveService.CreateSessionArchiveTask:input_type -> moego.service.account.v1.CreateSessionArchiveTaskRequest
	2, // 2: moego.service.account.v1.SessionArchiveService.UpdateSessionArchiveTaskStatus:input_type -> moego.service.account.v1.UpdateSessionArchiveTaskStatusRequest
	1, // 3: moego.service.account.v1.SessionArchiveService.CreateSessionArchiveTask:output_type -> moego.service.account.v1.CreateSessionArchiveTaskResponse
	3, // 4: moego.service.account.v1.SessionArchiveService.UpdateSessionArchiveTaskStatus:output_type -> moego.service.account.v1.UpdateSessionArchiveTaskStatusResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_account_v1_session_archive_service_proto_init() }
func file_moego_service_account_v1_session_archive_service_proto_init() {
	if File_moego_service_account_v1_session_archive_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_account_v1_session_archive_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionArchiveTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_archive_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionArchiveTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_archive_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSessionArchiveTaskStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_session_archive_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSessionArchiveTaskStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_account_v1_session_archive_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_account_v1_session_archive_service_proto_goTypes,
		DependencyIndexes: file_moego_service_account_v1_session_archive_service_proto_depIdxs,
		MessageInfos:      file_moego_service_account_v1_session_archive_service_proto_msgTypes,
	}.Build()
	File_moego_service_account_v1_session_archive_service_proto = out.File
	file_moego_service_account_v1_session_archive_service_proto_rawDesc = nil
	file_moego_service_account_v1_session_archive_service_proto_goTypes = nil
	file_moego_service_account_v1_session_archive_service_proto_depIdxs = nil
}
