// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/appointment_checker_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// check save appointment params
type CheckSaveAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// lodging unit ids
	LodgingUnitIds []int64 `protobuf:"varint,4,rep,packed,name=lodging_unit_ids,json=lodgingUnitIds,proto3" json:"lodging_unit_ids,omitempty"`
	// pet ids
	PetIds []int64 `protobuf:"varint,5,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// customer id
	CustomerId *int64 `protobuf:"varint,6,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// appointment id
	AppointmentId *int64 `protobuf:"varint,7,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
}

func (x *CheckSaveAppointmentParams) Reset() {
	*x = CheckSaveAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSaveAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSaveAppointmentParams) ProtoMessage() {}

func (x *CheckSaveAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSaveAppointmentParams.ProtoReflect.Descriptor instead.
func (*CheckSaveAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{0}
}

func (x *CheckSaveAppointmentParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CheckSaveAppointmentParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CheckSaveAppointmentParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *CheckSaveAppointmentParams) GetLodgingUnitIds() []int64 {
	if x != nil {
		return x.LodgingUnitIds
	}
	return nil
}

func (x *CheckSaveAppointmentParams) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *CheckSaveAppointmentParams) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *CheckSaveAppointmentParams) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

// check save appointment result
type CheckSaveAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging over capacity check result
	LodgingOverCapacityCheckResult *LodgingOverCapacityCheckResult `protobuf:"bytes,1,opt,name=lodging_over_capacity_check_result,json=lodgingOverCapacityCheckResult,proto3" json:"lodging_over_capacity_check_result,omitempty"`
	// appointment date conflict check result
	AppointmentDateConflictCheckResult *AppointmentDateConflictCheckResult `protobuf:"bytes,2,opt,name=appointment_date_conflict_check_result,json=appointmentDateConflictCheckResult,proto3" json:"appointment_date_conflict_check_result,omitempty"`
	// business closed date check result
	BusinessClosedDateCheckResult *BusinessClosedDateCheckResult `protobuf:"bytes,3,opt,name=business_closed_date_check_result,json=businessClosedDateCheckResult,proto3" json:"business_closed_date_check_result,omitempty"`
}

func (x *CheckSaveAppointmentResult) Reset() {
	*x = CheckSaveAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSaveAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSaveAppointmentResult) ProtoMessage() {}

func (x *CheckSaveAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSaveAppointmentResult.ProtoReflect.Descriptor instead.
func (*CheckSaveAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{1}
}

func (x *CheckSaveAppointmentResult) GetLodgingOverCapacityCheckResult() *LodgingOverCapacityCheckResult {
	if x != nil {
		return x.LodgingOverCapacityCheckResult
	}
	return nil
}

func (x *CheckSaveAppointmentResult) GetAppointmentDateConflictCheckResult() *AppointmentDateConflictCheckResult {
	if x != nil {
		return x.AppointmentDateConflictCheckResult
	}
	return nil
}

func (x *CheckSaveAppointmentResult) GetBusinessClosedDateCheckResult() *BusinessClosedDateCheckResult {
	if x != nil {
		return x.BusinessClosedDateCheckResult
	}
	return nil
}

// check lodging over capacity params
type CheckLodgingOverCapacityParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// lodging unit ids
	LodgingUnitIds []int64 `protobuf:"varint,4,rep,packed,name=lodging_unit_ids,json=lodgingUnitIds,proto3" json:"lodging_unit_ids,omitempty"`
	// appointment id
	AppointmentId *int64 `protobuf:"varint,5,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
	// lodging unit change
	LodgingUnitChange *LodgingUnitChange `protobuf:"bytes,6,opt,name=lodging_unit_change,json=lodgingUnitChange,proto3" json:"lodging_unit_change,omitempty"`
}

func (x *CheckLodgingOverCapacityParams) Reset() {
	*x = CheckLodgingOverCapacityParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckLodgingOverCapacityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckLodgingOverCapacityParams) ProtoMessage() {}

func (x *CheckLodgingOverCapacityParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckLodgingOverCapacityParams.ProtoReflect.Descriptor instead.
func (*CheckLodgingOverCapacityParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{2}
}

func (x *CheckLodgingOverCapacityParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CheckLodgingOverCapacityParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CheckLodgingOverCapacityParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *CheckLodgingOverCapacityParams) GetLodgingUnitIds() []int64 {
	if x != nil {
		return x.LodgingUnitIds
	}
	return nil
}

func (x *CheckLodgingOverCapacityParams) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

func (x *CheckLodgingOverCapacityParams) GetLodgingUnitChange() *LodgingUnitChange {
	if x != nil {
		return x.LodgingUnitChange
	}
	return nil
}

// lodging unit change
type LodgingUnitChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// old lodging unit id
	OldLodgingUnitId int64 `protobuf:"varint,1,opt,name=old_lodging_unit_id,json=oldLodgingUnitId,proto3" json:"old_lodging_unit_id,omitempty"`
	// new lodging unit id
	NewLodgingUnitId int64 `protobuf:"varint,2,opt,name=new_lodging_unit_id,json=newLodgingUnitId,proto3" json:"new_lodging_unit_id,omitempty"`
}

func (x *LodgingUnitChange) Reset() {
	*x = LodgingUnitChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingUnitChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUnitChange) ProtoMessage() {}

func (x *LodgingUnitChange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUnitChange.ProtoReflect.Descriptor instead.
func (*LodgingUnitChange) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{3}
}

func (x *LodgingUnitChange) GetOldLodgingUnitId() int64 {
	if x != nil {
		return x.OldLodgingUnitId
	}
	return 0
}

func (x *LodgingUnitChange) GetNewLodgingUnitId() int64 {
	if x != nil {
		return x.NewLodgingUnitId
	}
	return 0
}

// check lodging over capacity result
type CheckLodgingOverCapacityResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging over capacity check result
	LodgingOverCapacityCheckResult *LodgingOverCapacityCheckResult `protobuf:"bytes,1,opt,name=lodging_over_capacity_check_result,json=lodgingOverCapacityCheckResult,proto3" json:"lodging_over_capacity_check_result,omitempty"`
	// business closed date check result
	BusinessClosedDateCheckResult *BusinessClosedDateCheckResult `protobuf:"bytes,3,opt,name=business_closed_date_check_result,json=businessClosedDateCheckResult,proto3" json:"business_closed_date_check_result,omitempty"`
}

func (x *CheckLodgingOverCapacityResult) Reset() {
	*x = CheckLodgingOverCapacityResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckLodgingOverCapacityResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckLodgingOverCapacityResult) ProtoMessage() {}

func (x *CheckLodgingOverCapacityResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckLodgingOverCapacityResult.ProtoReflect.Descriptor instead.
func (*CheckLodgingOverCapacityResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{4}
}

func (x *CheckLodgingOverCapacityResult) GetLodgingOverCapacityCheckResult() *LodgingOverCapacityCheckResult {
	if x != nil {
		return x.LodgingOverCapacityCheckResult
	}
	return nil
}

func (x *CheckLodgingOverCapacityResult) GetBusinessClosedDateCheckResult() *BusinessClosedDateCheckResult {
	if x != nil {
		return x.BusinessClosedDateCheckResult
	}
	return nil
}

// lodging over capacity check result
type LodgingOverCapacityCheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging over capacity list
	LodgingUnits []*LodgingUnitOverview `protobuf:"bytes,1,rep,name=lodging_units,json=lodgingUnits,proto3" json:"lodging_units,omitempty"`
	// lodging type list
	LodgingTypes []*LodgingTypeOverview `protobuf:"bytes,2,rep,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
}

func (x *LodgingOverCapacityCheckResult) Reset() {
	*x = LodgingOverCapacityCheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingOverCapacityCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingOverCapacityCheckResult) ProtoMessage() {}

func (x *LodgingOverCapacityCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingOverCapacityCheckResult.ProtoReflect.Descriptor instead.
func (*LodgingOverCapacityCheckResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{5}
}

func (x *LodgingOverCapacityCheckResult) GetLodgingUnits() []*LodgingUnitOverview {
	if x != nil {
		return x.LodgingUnits
	}
	return nil
}

func (x *LodgingOverCapacityCheckResult) GetLodgingTypes() []*LodgingTypeOverview {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

// lodging unit over view
type LodgingUnitOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// lodging unit name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// lodging unit type id
	LodgingTypeId int64 `protobuf:"varint,3,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
}

func (x *LodgingUnitOverview) Reset() {
	*x = LodgingUnitOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingUnitOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUnitOverview) ProtoMessage() {}

func (x *LodgingUnitOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUnitOverview.ProtoReflect.Descriptor instead.
func (*LodgingUnitOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{6}
}

func (x *LodgingUnitOverview) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingUnitOverview) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingUnitOverview) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

// lodging type over view
type LodgingTypeOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// lodging type name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *LodgingTypeOverview) Reset() {
	*x = LodgingTypeOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingTypeOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingTypeOverview) ProtoMessage() {}

func (x *LodgingTypeOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingTypeOverview.ProtoReflect.Descriptor instead.
func (*LodgingTypeOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{7}
}

func (x *LodgingTypeOverview) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingTypeOverview) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// appointment date conflict check result
type AppointmentDateConflictCheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conflict appointments overview for pet
	ConflictAppointments []*PetAppointmentsOverview `protobuf:"bytes,1,rep,name=conflict_appointments,json=conflictAppointments,proto3" json:"conflict_appointments,omitempty"`
}

func (x *AppointmentDateConflictCheckResult) Reset() {
	*x = AppointmentDateConflictCheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentDateConflictCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentDateConflictCheckResult) ProtoMessage() {}

func (x *AppointmentDateConflictCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentDateConflictCheckResult.ProtoReflect.Descriptor instead.
func (*AppointmentDateConflictCheckResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{8}
}

func (x *AppointmentDateConflictCheckResult) GetConflictAppointments() []*PetAppointmentsOverview {
	if x != nil {
		return x.ConflictAppointments
	}
	return nil
}

// appointments overview for pet
type PetAppointmentsOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet
	Pet *PetOverview `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// appointments
	Appointments []*AppointmentOverview `protobuf:"bytes,2,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *PetAppointmentsOverview) Reset() {
	*x = PetAppointmentsOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetAppointmentsOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetAppointmentsOverview) ProtoMessage() {}

func (x *PetAppointmentsOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetAppointmentsOverview.ProtoReflect.Descriptor instead.
func (*PetAppointmentsOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{9}
}

func (x *PetAppointmentsOverview) GetPet() *PetOverview {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *PetAppointmentsOverview) GetAppointments() []*AppointmentOverview {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// business closed date check result
type BusinessClosedDateCheckResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// closed date list, in yyyy-MM-dd format
	ClosedDate []string `protobuf:"bytes,1,rep,name=closed_date,json=closedDate,proto3" json:"closed_date,omitempty"`
}

func (x *BusinessClosedDateCheckResult) Reset() {
	*x = BusinessClosedDateCheckResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessClosedDateCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessClosedDateCheckResult) ProtoMessage() {}

func (x *BusinessClosedDateCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessClosedDateCheckResult.ProtoReflect.Descriptor instead.
func (*BusinessClosedDateCheckResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{10}
}

func (x *BusinessClosedDateCheckResult) GetClosedDate() []string {
	if x != nil {
		return x.ClosedDate
	}
	return nil
}

// pet overview
type PetOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// weight
	Weight string `protobuf:"bytes,4,opt,name=weight,proto3" json:"weight,omitempty"`
	// coat type
	CoatType string `protobuf:"bytes,5,opt,name=coat_type,json=coatType,proto3" json:"coat_type,omitempty"`
	// breed
	Breed string `protobuf:"bytes,6,opt,name=breed,proto3" json:"breed,omitempty"`
	// pet type
	PetType v1.PetType `protobuf:"varint,7,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
}

func (x *PetOverview) Reset() {
	*x = PetOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetOverview) ProtoMessage() {}

func (x *PetOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetOverview.ProtoReflect.Descriptor instead.
func (*PetOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{11}
}

func (x *PetOverview) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetOverview) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *PetOverview) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *PetOverview) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *PetOverview) GetCoatType() string {
	if x != nil {
		return x.CoatType
	}
	return ""
}

func (x *PetOverview) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *PetOverview) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

// appointment overview
type AppointmentOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// appointment start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// appointment end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// services
	Services []*ServiceOverview `protobuf:"bytes,4,rep,name=services,proto3" json:"services,omitempty"`
	// appointment start time, the number of minutes of the day
	AppointmentStartTime int32 `protobuf:"varint,5,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time, the number of minutes of the day
	AppointmentEndTime int32 `protobuf:"varint,6,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
}

func (x *AppointmentOverview) Reset() {
	*x = AppointmentOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentOverview) ProtoMessage() {}

func (x *AppointmentOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentOverview.ProtoReflect.Descriptor instead.
func (*AppointmentOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{12}
}

func (x *AppointmentOverview) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *AppointmentOverview) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *AppointmentOverview) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *AppointmentOverview) GetServices() []*ServiceOverview {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *AppointmentOverview) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *AppointmentOverview) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

// service overview
type ServiceOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// service start date
	StartDate *date.Date `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date
	EndDate *date.Date `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// service item type, different from service type, it includes grooming, boarding, daycare or other services.
	ServiceItemType v11.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *ServiceOverview) Reset() {
	*x = ServiceOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOverview) ProtoMessage() {}

func (x *ServiceOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOverview.ProtoReflect.Descriptor instead.
func (*ServiceOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{13}
}

func (x *ServiceOverview) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceOverview) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceOverview) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ServiceOverview) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ServiceOverview) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

// get available dates params
type GetAvailableDatesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business_id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet 和选择的 service 信息
	PetServices []*GetAvailableDatesParams_PetServices `protobuf:"bytes,2,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *GetAvailableDatesParams) Reset() {
	*x = GetAvailableDatesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDatesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDatesParams) ProtoMessage() {}

func (x *GetAvailableDatesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDatesParams.ProtoReflect.Descriptor instead.
func (*GetAvailableDatesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetAvailableDatesParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAvailableDatesParams) GetPetServices() []*GetAvailableDatesParams_PetServices {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *GetAvailableDatesParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetAvailableDatesParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

// get available dates result
type GetAvailableDatesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available dates
	AvailableDates []*date.Date `protobuf:"bytes,1,rep,name=available_dates,json=availableDates,proto3" json:"available_dates,omitempty"`
	// unavailable dates
	UnavailableDates []*date.Date `protobuf:"bytes,2,rep,name=unavailable_dates,json=unavailableDates,proto3" json:"unavailable_dates,omitempty"`
}

func (x *GetAvailableDatesResult) Reset() {
	*x = GetAvailableDatesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDatesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDatesResult) ProtoMessage() {}

func (x *GetAvailableDatesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDatesResult.ProtoReflect.Descriptor instead.
func (*GetAvailableDatesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{15}
}

func (x *GetAvailableDatesResult) GetAvailableDates() []*date.Date {
	if x != nil {
		return x.AvailableDates
	}
	return nil
}

func (x *GetAvailableDatesResult) GetUnavailableDates() []*date.Date {
	if x != nil {
		return x.UnavailableDates
	}
	return nil
}

// get available time ranges params
type GetEvaluationAvailableTimeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business_id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet 和选择的 service 信息
	PetServices []*GetEvaluationAvailableTimeParams_PetServices `protobuf:"bytes,2,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// start date
	Date *date.Date `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *GetEvaluationAvailableTimeParams) Reset() {
	*x = GetEvaluationAvailableTimeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationAvailableTimeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationAvailableTimeParams) ProtoMessage() {}

func (x *GetEvaluationAvailableTimeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationAvailableTimeParams.ProtoReflect.Descriptor instead.
func (*GetEvaluationAvailableTimeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{16}
}

func (x *GetEvaluationAvailableTimeParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetEvaluationAvailableTimeParams) GetPetServices() []*GetEvaluationAvailableTimeParams_PetServices {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *GetEvaluationAvailableTimeParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

// get available time ranges result
type GetEvaluationAvailableTimeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available time ranges
	DayTimeRanges []*GetEvaluationAvailableTimeResult_DayAvailableTimeRanges `protobuf:"bytes,1,rep,name=day_time_ranges,json=dayTimeRanges,proto3" json:"day_time_ranges,omitempty"`
}

func (x *GetEvaluationAvailableTimeResult) Reset() {
	*x = GetEvaluationAvailableTimeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationAvailableTimeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationAvailableTimeResult) ProtoMessage() {}

func (x *GetEvaluationAvailableTimeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationAvailableTimeResult.ProtoReflect.Descriptor instead.
func (*GetEvaluationAvailableTimeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{17}
}

func (x *GetEvaluationAvailableTimeResult) GetDayTimeRanges() []*GetEvaluationAvailableTimeResult_DayAvailableTimeRanges {
	if x != nil {
		return x.DayTimeRanges
	}
	return nil
}

// pet 和选择的 service/evaluation 信息
type GetAvailableDatesParams_PetServices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet selected evaluations
	EvaluationIds []int64 `protobuf:"varint,2,rep,packed,name=evaluation_ids,json=evaluationIds,proto3" json:"evaluation_ids,omitempty"`
}

func (x *GetAvailableDatesParams_PetServices) Reset() {
	*x = GetAvailableDatesParams_PetServices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDatesParams_PetServices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDatesParams_PetServices) ProtoMessage() {}

func (x *GetAvailableDatesParams_PetServices) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDatesParams_PetServices.ProtoReflect.Descriptor instead.
func (*GetAvailableDatesParams_PetServices) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{14, 0}
}

func (x *GetAvailableDatesParams_PetServices) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GetAvailableDatesParams_PetServices) GetEvaluationIds() []int64 {
	if x != nil {
		return x.EvaluationIds
	}
	return nil
}

// pet 和选择的 service/evaluation 信息
type GetEvaluationAvailableTimeParams_PetServices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet selected evaluations
	EvaluationIds []int64 `protobuf:"varint,2,rep,packed,name=evaluation_ids,json=evaluationIds,proto3" json:"evaluation_ids,omitempty"`
}

func (x *GetEvaluationAvailableTimeParams_PetServices) Reset() {
	*x = GetEvaluationAvailableTimeParams_PetServices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationAvailableTimeParams_PetServices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationAvailableTimeParams_PetServices) ProtoMessage() {}

func (x *GetEvaluationAvailableTimeParams_PetServices) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationAvailableTimeParams_PetServices.ProtoReflect.Descriptor instead.
func (*GetEvaluationAvailableTimeParams_PetServices) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{16, 0}
}

func (x *GetEvaluationAvailableTimeParams_PetServices) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GetEvaluationAvailableTimeParams_PetServices) GetEvaluationIds() []int64 {
	if x != nil {
		return x.EvaluationIds
	}
	return nil
}

// available time ranges for a certain day
type GetEvaluationAvailableTimeResult_DayAvailableTimeRanges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date
	Date *date.Date `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// available time range list
	TimeRange []*v12.DayTimeRangeDef `protobuf:"bytes,2,rep,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetEvaluationAvailableTimeResult_DayAvailableTimeRanges) Reset() {
	*x = GetEvaluationAvailableTimeResult_DayAvailableTimeRanges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationAvailableTimeResult_DayAvailableTimeRanges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationAvailableTimeResult_DayAvailableTimeRanges) ProtoMessage() {}

func (x *GetEvaluationAvailableTimeResult_DayAvailableTimeRanges) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationAvailableTimeResult_DayAvailableTimeRanges.ProtoReflect.Descriptor instead.
func (*GetEvaluationAvailableTimeResult_DayAvailableTimeRanges) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP(), []int{17, 0}
}

func (x *GetEvaluationAvailableTimeResult_DayAvailableTimeRanges) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetEvaluationAvailableTimeResult_DayAvailableTimeRanges) GetTimeRange() []*v12.DayTimeRangeDef {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

var File_moego_api_appointment_v1_appointment_checker_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_appointment_checker_api_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf2, 0x02, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x3a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22, 0xba, 0x03, 0x0a, 0x1a,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x84, 0x01, 0x0a, 0x22, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x1e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x90, 0x01, 0x0a, 0x26, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x6c, 0x69, 0x63, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x22, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x81, 0x01, 0x0a, 0x21, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x1d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x84, 0x03, 0x0a, 0x1e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x43, 0x61, 0x70,
	0x61, 0x63, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x5b, 0x0a, 0x13, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55,
	0x6e, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22,
	0x71, 0x0a, 0x11, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x13, 0x6f, 0x6c, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x6f, 0x6c, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69,
	0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x6e, 0x65, 0x77, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x10, 0x6e, 0x65, 0x77, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x49, 0x64, 0x22, 0xab, 0x02, 0x0a, 0x1e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x84, 0x01, 0x0a, 0x22, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x1e, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x81, 0x01, 0x0a,
	0x21, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x1d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0xc8, 0x01, 0x0a, 0x1e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72,
	0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x52, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69,
	0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x61, 0x0a, 0x13, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x22, 0x39,
	0x0a, 0x13, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x22, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x6c, 0x69, 0x63, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x66, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x5f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xa5, 0x01, 0x0a, 0x17, 0x50, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x37, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x51, 0x0a,
	0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x40, 0x0a, 0x1d, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x22, 0xe2, 0x01, 0x0a, 0x0b, 0x50, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16,
	0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07,
	0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xcb, 0x02, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x34, 0x0a,
	0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x8a, 0x02, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xc9, 0x02, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x60, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x1a, 0x4b, 0x0a, 0x0b, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x95,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x11, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x10, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x22, 0xab, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x69, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x4b, 0x0a, 0x0b, 0x50, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x73, 0x22, 0xaf, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x79, 0x0a, 0x0f, 0x64, 0x61, 0x79,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x44,
	0x61, 0x79, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x0d, 0x64, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x1a, 0x8f, 0x01, 0x0a, 0x16, 0x44, 0x61, 0x79, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12,
	0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x32, 0xc3, 0x04, 0x0a, 0x19, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x61,
	0x76, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x61,
	0x76, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x79, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8e, 0x01, 0x0a, 0x18,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72,
	0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x4f, 0x76, 0x65, 0x72, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a,
	0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescData = file_moego_api_appointment_v1_appointment_checker_api_proto_rawDesc
)

func file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_appointment_checker_api_proto_rawDescData
}

var file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_moego_api_appointment_v1_appointment_checker_api_proto_goTypes = []interface{}{
	(*CheckSaveAppointmentParams)(nil),                              // 0: moego.api.appointment.v1.CheckSaveAppointmentParams
	(*CheckSaveAppointmentResult)(nil),                              // 1: moego.api.appointment.v1.CheckSaveAppointmentResult
	(*CheckLodgingOverCapacityParams)(nil),                          // 2: moego.api.appointment.v1.CheckLodgingOverCapacityParams
	(*LodgingUnitChange)(nil),                                       // 3: moego.api.appointment.v1.LodgingUnitChange
	(*CheckLodgingOverCapacityResult)(nil),                          // 4: moego.api.appointment.v1.CheckLodgingOverCapacityResult
	(*LodgingOverCapacityCheckResult)(nil),                          // 5: moego.api.appointment.v1.LodgingOverCapacityCheckResult
	(*LodgingUnitOverview)(nil),                                     // 6: moego.api.appointment.v1.LodgingUnitOverview
	(*LodgingTypeOverview)(nil),                                     // 7: moego.api.appointment.v1.LodgingTypeOverview
	(*AppointmentDateConflictCheckResult)(nil),                      // 8: moego.api.appointment.v1.AppointmentDateConflictCheckResult
	(*PetAppointmentsOverview)(nil),                                 // 9: moego.api.appointment.v1.PetAppointmentsOverview
	(*BusinessClosedDateCheckResult)(nil),                           // 10: moego.api.appointment.v1.BusinessClosedDateCheckResult
	(*PetOverview)(nil),                                             // 11: moego.api.appointment.v1.PetOverview
	(*AppointmentOverview)(nil),                                     // 12: moego.api.appointment.v1.AppointmentOverview
	(*ServiceOverview)(nil),                                         // 13: moego.api.appointment.v1.ServiceOverview
	(*GetAvailableDatesParams)(nil),                                 // 14: moego.api.appointment.v1.GetAvailableDatesParams
	(*GetAvailableDatesResult)(nil),                                 // 15: moego.api.appointment.v1.GetAvailableDatesResult
	(*GetEvaluationAvailableTimeParams)(nil),                        // 16: moego.api.appointment.v1.GetEvaluationAvailableTimeParams
	(*GetEvaluationAvailableTimeResult)(nil),                        // 17: moego.api.appointment.v1.GetEvaluationAvailableTimeResult
	(*GetAvailableDatesParams_PetServices)(nil),                     // 18: moego.api.appointment.v1.GetAvailableDatesParams.PetServices
	(*GetEvaluationAvailableTimeParams_PetServices)(nil),            // 19: moego.api.appointment.v1.GetEvaluationAvailableTimeParams.PetServices
	(*GetEvaluationAvailableTimeResult_DayAvailableTimeRanges)(nil), // 20: moego.api.appointment.v1.GetEvaluationAvailableTimeResult.DayAvailableTimeRanges
	(*date.Date)(nil),                                               // 21: google.type.Date
	(v1.PetType)(0),                                                 // 22: moego.models.customer.v1.PetType
	(v11.ServiceItemType)(0),                                        // 23: moego.models.offering.v1.ServiceItemType
	(*v12.DayTimeRangeDef)(nil),                                     // 24: moego.models.online_booking.v1.DayTimeRangeDef
}
var file_moego_api_appointment_v1_appointment_checker_api_proto_depIdxs = []int32{
	21, // 0: moego.api.appointment.v1.CheckSaveAppointmentParams.start_date:type_name -> google.type.Date
	21, // 1: moego.api.appointment.v1.CheckSaveAppointmentParams.end_date:type_name -> google.type.Date
	5,  // 2: moego.api.appointment.v1.CheckSaveAppointmentResult.lodging_over_capacity_check_result:type_name -> moego.api.appointment.v1.LodgingOverCapacityCheckResult
	8,  // 3: moego.api.appointment.v1.CheckSaveAppointmentResult.appointment_date_conflict_check_result:type_name -> moego.api.appointment.v1.AppointmentDateConflictCheckResult
	10, // 4: moego.api.appointment.v1.CheckSaveAppointmentResult.business_closed_date_check_result:type_name -> moego.api.appointment.v1.BusinessClosedDateCheckResult
	21, // 5: moego.api.appointment.v1.CheckLodgingOverCapacityParams.start_date:type_name -> google.type.Date
	21, // 6: moego.api.appointment.v1.CheckLodgingOverCapacityParams.end_date:type_name -> google.type.Date
	3,  // 7: moego.api.appointment.v1.CheckLodgingOverCapacityParams.lodging_unit_change:type_name -> moego.api.appointment.v1.LodgingUnitChange
	5,  // 8: moego.api.appointment.v1.CheckLodgingOverCapacityResult.lodging_over_capacity_check_result:type_name -> moego.api.appointment.v1.LodgingOverCapacityCheckResult
	10, // 9: moego.api.appointment.v1.CheckLodgingOverCapacityResult.business_closed_date_check_result:type_name -> moego.api.appointment.v1.BusinessClosedDateCheckResult
	6,  // 10: moego.api.appointment.v1.LodgingOverCapacityCheckResult.lodging_units:type_name -> moego.api.appointment.v1.LodgingUnitOverview
	7,  // 11: moego.api.appointment.v1.LodgingOverCapacityCheckResult.lodging_types:type_name -> moego.api.appointment.v1.LodgingTypeOverview
	9,  // 12: moego.api.appointment.v1.AppointmentDateConflictCheckResult.conflict_appointments:type_name -> moego.api.appointment.v1.PetAppointmentsOverview
	11, // 13: moego.api.appointment.v1.PetAppointmentsOverview.pet:type_name -> moego.api.appointment.v1.PetOverview
	12, // 14: moego.api.appointment.v1.PetAppointmentsOverview.appointments:type_name -> moego.api.appointment.v1.AppointmentOverview
	22, // 15: moego.api.appointment.v1.PetOverview.pet_type:type_name -> moego.models.customer.v1.PetType
	21, // 16: moego.api.appointment.v1.AppointmentOverview.start_date:type_name -> google.type.Date
	21, // 17: moego.api.appointment.v1.AppointmentOverview.end_date:type_name -> google.type.Date
	13, // 18: moego.api.appointment.v1.AppointmentOverview.services:type_name -> moego.api.appointment.v1.ServiceOverview
	21, // 19: moego.api.appointment.v1.ServiceOverview.start_date:type_name -> google.type.Date
	21, // 20: moego.api.appointment.v1.ServiceOverview.end_date:type_name -> google.type.Date
	23, // 21: moego.api.appointment.v1.ServiceOverview.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	18, // 22: moego.api.appointment.v1.GetAvailableDatesParams.pet_services:type_name -> moego.api.appointment.v1.GetAvailableDatesParams.PetServices
	21, // 23: moego.api.appointment.v1.GetAvailableDatesParams.start_date:type_name -> google.type.Date
	21, // 24: moego.api.appointment.v1.GetAvailableDatesParams.end_date:type_name -> google.type.Date
	21, // 25: moego.api.appointment.v1.GetAvailableDatesResult.available_dates:type_name -> google.type.Date
	21, // 26: moego.api.appointment.v1.GetAvailableDatesResult.unavailable_dates:type_name -> google.type.Date
	19, // 27: moego.api.appointment.v1.GetEvaluationAvailableTimeParams.pet_services:type_name -> moego.api.appointment.v1.GetEvaluationAvailableTimeParams.PetServices
	21, // 28: moego.api.appointment.v1.GetEvaluationAvailableTimeParams.date:type_name -> google.type.Date
	20, // 29: moego.api.appointment.v1.GetEvaluationAvailableTimeResult.day_time_ranges:type_name -> moego.api.appointment.v1.GetEvaluationAvailableTimeResult.DayAvailableTimeRanges
	21, // 30: moego.api.appointment.v1.GetEvaluationAvailableTimeResult.DayAvailableTimeRanges.date:type_name -> google.type.Date
	24, // 31: moego.api.appointment.v1.GetEvaluationAvailableTimeResult.DayAvailableTimeRanges.time_range:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	0,  // 32: moego.api.appointment.v1.AppointmentCheckerService.CheckSaveAppointment:input_type -> moego.api.appointment.v1.CheckSaveAppointmentParams
	14, // 33: moego.api.appointment.v1.AppointmentCheckerService.GetAvailableDates:input_type -> moego.api.appointment.v1.GetAvailableDatesParams
	16, // 34: moego.api.appointment.v1.AppointmentCheckerService.GetEvaluationAvailableTime:input_type -> moego.api.appointment.v1.GetEvaluationAvailableTimeParams
	2,  // 35: moego.api.appointment.v1.AppointmentCheckerService.CheckLodgingOverCapacity:input_type -> moego.api.appointment.v1.CheckLodgingOverCapacityParams
	1,  // 36: moego.api.appointment.v1.AppointmentCheckerService.CheckSaveAppointment:output_type -> moego.api.appointment.v1.CheckSaveAppointmentResult
	15, // 37: moego.api.appointment.v1.AppointmentCheckerService.GetAvailableDates:output_type -> moego.api.appointment.v1.GetAvailableDatesResult
	17, // 38: moego.api.appointment.v1.AppointmentCheckerService.GetEvaluationAvailableTime:output_type -> moego.api.appointment.v1.GetEvaluationAvailableTimeResult
	4,  // 39: moego.api.appointment.v1.AppointmentCheckerService.CheckLodgingOverCapacity:output_type -> moego.api.appointment.v1.CheckLodgingOverCapacityResult
	36, // [36:40] is the sub-list for method output_type
	32, // [32:36] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_appointment_checker_api_proto_init() }
func file_moego_api_appointment_v1_appointment_checker_api_proto_init() {
	if File_moego_api_appointment_v1_appointment_checker_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSaveAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSaveAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckLodgingOverCapacityParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingUnitChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckLodgingOverCapacityResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingOverCapacityCheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingUnitOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingTypeOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentDateConflictCheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetAppointmentsOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessClosedDateCheckResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDatesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDatesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationAvailableTimeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationAvailableTimeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDatesParams_PetServices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationAvailableTimeParams_PetServices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationAvailableTimeResult_DayAvailableTimeRanges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_appointment_checker_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_appointment_checker_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_appointment_checker_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_appointment_checker_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_appointment_checker_api_proto = out.File
	file_moego_api_appointment_v1_appointment_checker_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_appointment_checker_api_proto_goTypes = nil
	file_moego_api_appointment_v1_appointment_checker_api_proto_depIdxs = nil
}
