// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/offering/v2/pricing_rule_api.proto

package offeringapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PricingRuleServiceClient is the client API for PricingRuleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PricingRuleServiceClient interface {
	// upsert pricing rule
	UpsertPricingRule(ctx context.Context, in *UpsertPricingRuleParams, opts ...grpc.CallOption) (*UpsertPricingRuleResult, error)
	// get pricing rule
	GetPricingRule(ctx context.Context, in *GetPricingRuleParams, opts ...grpc.CallOption) (*GetPricingRuleResult, error)
	// list pricing rule
	ListPricingRules(ctx context.Context, in *ListPricingRulesParams, opts ...grpc.CallOption) (*ListPricingRulesResult, error)
	// calculate pricing rule
	CalculatePricingRule(ctx context.Context, in *CalculatePricingRuleParams, opts ...grpc.CallOption) (*CalculatePricingRuleResult, error)
	// preview pricing rule
	PreviewPricingRule(ctx context.Context, in *PreviewPricingRuleParams, opts ...grpc.CallOption) (*PreviewPricingRuleResult, error)
	// delete pricing rule
	DeletePricingRule(ctx context.Context, in *DeletePricingRuleParams, opts ...grpc.CallOption) (*DeletePricingRuleResult, error)
	// list associated services, used for pricing rule selecting services
	ListAssociatedServices(ctx context.Context, in *ListAssociatedServicesParams, opts ...grpc.CallOption) (*ListAssociatedServicesResult, error)
	// check configuration
	CheckConfiguration(ctx context.Context, in *CheckConfigurationParams, opts ...grpc.CallOption) (*CheckConfigurationResult, error)
	// get discount setting
	GetDiscountSetting(ctx context.Context, in *GetDiscountSettingParams, opts ...grpc.CallOption) (*GetDiscountSettingResult, error)
	// update discount setting
	UpdateDiscountSetting(ctx context.Context, in *UpdateDiscountSettingParams, opts ...grpc.CallOption) (*UpdateDiscountSettingResult, error)
	// get pricing rule overview
	GetPricingRuleOverview(ctx context.Context, in *GetPricingRuleOverviewParams, opts ...grpc.CallOption) (*GetPricingRuleOverviewResult, error)
}

type pricingRuleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingRuleServiceClient(cc grpc.ClientConnInterface) PricingRuleServiceClient {
	return &pricingRuleServiceClient{cc}
}

func (c *pricingRuleServiceClient) UpsertPricingRule(ctx context.Context, in *UpsertPricingRuleParams, opts ...grpc.CallOption) (*UpsertPricingRuleResult, error) {
	out := new(UpsertPricingRuleResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/UpsertPricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) GetPricingRule(ctx context.Context, in *GetPricingRuleParams, opts ...grpc.CallOption) (*GetPricingRuleResult, error) {
	out := new(GetPricingRuleResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/GetPricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) ListPricingRules(ctx context.Context, in *ListPricingRulesParams, opts ...grpc.CallOption) (*ListPricingRulesResult, error) {
	out := new(ListPricingRulesResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/ListPricingRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) CalculatePricingRule(ctx context.Context, in *CalculatePricingRuleParams, opts ...grpc.CallOption) (*CalculatePricingRuleResult, error) {
	out := new(CalculatePricingRuleResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/CalculatePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) PreviewPricingRule(ctx context.Context, in *PreviewPricingRuleParams, opts ...grpc.CallOption) (*PreviewPricingRuleResult, error) {
	out := new(PreviewPricingRuleResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/PreviewPricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) DeletePricingRule(ctx context.Context, in *DeletePricingRuleParams, opts ...grpc.CallOption) (*DeletePricingRuleResult, error) {
	out := new(DeletePricingRuleResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/DeletePricingRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) ListAssociatedServices(ctx context.Context, in *ListAssociatedServicesParams, opts ...grpc.CallOption) (*ListAssociatedServicesResult, error) {
	out := new(ListAssociatedServicesResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/ListAssociatedServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) CheckConfiguration(ctx context.Context, in *CheckConfigurationParams, opts ...grpc.CallOption) (*CheckConfigurationResult, error) {
	out := new(CheckConfigurationResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/CheckConfiguration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) GetDiscountSetting(ctx context.Context, in *GetDiscountSettingParams, opts ...grpc.CallOption) (*GetDiscountSettingResult, error) {
	out := new(GetDiscountSettingResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/GetDiscountSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) UpdateDiscountSetting(ctx context.Context, in *UpdateDiscountSettingParams, opts ...grpc.CallOption) (*UpdateDiscountSettingResult, error) {
	out := new(UpdateDiscountSettingResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/UpdateDiscountSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingRuleServiceClient) GetPricingRuleOverview(ctx context.Context, in *GetPricingRuleOverviewParams, opts ...grpc.CallOption) (*GetPricingRuleOverviewResult, error) {
	out := new(GetPricingRuleOverviewResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v2.PricingRuleService/GetPricingRuleOverview", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingRuleServiceServer is the server API for PricingRuleService service.
// All implementations must embed UnimplementedPricingRuleServiceServer
// for forward compatibility
type PricingRuleServiceServer interface {
	// upsert pricing rule
	UpsertPricingRule(context.Context, *UpsertPricingRuleParams) (*UpsertPricingRuleResult, error)
	// get pricing rule
	GetPricingRule(context.Context, *GetPricingRuleParams) (*GetPricingRuleResult, error)
	// list pricing rule
	ListPricingRules(context.Context, *ListPricingRulesParams) (*ListPricingRulesResult, error)
	// calculate pricing rule
	CalculatePricingRule(context.Context, *CalculatePricingRuleParams) (*CalculatePricingRuleResult, error)
	// preview pricing rule
	PreviewPricingRule(context.Context, *PreviewPricingRuleParams) (*PreviewPricingRuleResult, error)
	// delete pricing rule
	DeletePricingRule(context.Context, *DeletePricingRuleParams) (*DeletePricingRuleResult, error)
	// list associated services, used for pricing rule selecting services
	ListAssociatedServices(context.Context, *ListAssociatedServicesParams) (*ListAssociatedServicesResult, error)
	// check configuration
	CheckConfiguration(context.Context, *CheckConfigurationParams) (*CheckConfigurationResult, error)
	// get discount setting
	GetDiscountSetting(context.Context, *GetDiscountSettingParams) (*GetDiscountSettingResult, error)
	// update discount setting
	UpdateDiscountSetting(context.Context, *UpdateDiscountSettingParams) (*UpdateDiscountSettingResult, error)
	// get pricing rule overview
	GetPricingRuleOverview(context.Context, *GetPricingRuleOverviewParams) (*GetPricingRuleOverviewResult, error)
	mustEmbedUnimplementedPricingRuleServiceServer()
}

// UnimplementedPricingRuleServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPricingRuleServiceServer struct {
}

func (UnimplementedPricingRuleServiceServer) UpsertPricingRule(context.Context, *UpsertPricingRuleParams) (*UpsertPricingRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertPricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) GetPricingRule(context.Context, *GetPricingRuleParams) (*GetPricingRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) ListPricingRules(context.Context, *ListPricingRulesParams) (*ListPricingRulesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPricingRules not implemented")
}
func (UnimplementedPricingRuleServiceServer) CalculatePricingRule(context.Context, *CalculatePricingRuleParams) (*CalculatePricingRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculatePricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) PreviewPricingRule(context.Context, *PreviewPricingRuleParams) (*PreviewPricingRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewPricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) DeletePricingRule(context.Context, *DeletePricingRuleParams) (*DeletePricingRuleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePricingRule not implemented")
}
func (UnimplementedPricingRuleServiceServer) ListAssociatedServices(context.Context, *ListAssociatedServicesParams) (*ListAssociatedServicesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssociatedServices not implemented")
}
func (UnimplementedPricingRuleServiceServer) CheckConfiguration(context.Context, *CheckConfigurationParams) (*CheckConfigurationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckConfiguration not implemented")
}
func (UnimplementedPricingRuleServiceServer) GetDiscountSetting(context.Context, *GetDiscountSettingParams) (*GetDiscountSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountSetting not implemented")
}
func (UnimplementedPricingRuleServiceServer) UpdateDiscountSetting(context.Context, *UpdateDiscountSettingParams) (*UpdateDiscountSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDiscountSetting not implemented")
}
func (UnimplementedPricingRuleServiceServer) GetPricingRuleOverview(context.Context, *GetPricingRuleOverviewParams) (*GetPricingRuleOverviewResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingRuleOverview not implemented")
}
func (UnimplementedPricingRuleServiceServer) mustEmbedUnimplementedPricingRuleServiceServer() {}

// UnsafePricingRuleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PricingRuleServiceServer will
// result in compilation errors.
type UnsafePricingRuleServiceServer interface {
	mustEmbedUnimplementedPricingRuleServiceServer()
}

func RegisterPricingRuleServiceServer(s grpc.ServiceRegistrar, srv PricingRuleServiceServer) {
	s.RegisterService(&PricingRuleService_ServiceDesc, srv)
}

func _PricingRuleService_UpsertPricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertPricingRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).UpsertPricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/UpsertPricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).UpsertPricingRule(ctx, req.(*UpsertPricingRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_GetPricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPricingRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).GetPricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/GetPricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).GetPricingRule(ctx, req.(*GetPricingRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_ListPricingRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPricingRulesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).ListPricingRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/ListPricingRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).ListPricingRules(ctx, req.(*ListPricingRulesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_CalculatePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculatePricingRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).CalculatePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/CalculatePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).CalculatePricingRule(ctx, req.(*CalculatePricingRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_PreviewPricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewPricingRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).PreviewPricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/PreviewPricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).PreviewPricingRule(ctx, req.(*PreviewPricingRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_DeletePricingRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePricingRuleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).DeletePricingRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/DeletePricingRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).DeletePricingRule(ctx, req.(*DeletePricingRuleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_ListAssociatedServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAssociatedServicesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).ListAssociatedServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/ListAssociatedServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).ListAssociatedServices(ctx, req.(*ListAssociatedServicesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_CheckConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckConfigurationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).CheckConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/CheckConfiguration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).CheckConfiguration(ctx, req.(*CheckConfigurationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_GetDiscountSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).GetDiscountSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/GetDiscountSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).GetDiscountSetting(ctx, req.(*GetDiscountSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_UpdateDiscountSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDiscountSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).UpdateDiscountSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/UpdateDiscountSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).UpdateDiscountSetting(ctx, req.(*UpdateDiscountSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingRuleService_GetPricingRuleOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPricingRuleOverviewParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingRuleServiceServer).GetPricingRuleOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v2.PricingRuleService/GetPricingRuleOverview",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingRuleServiceServer).GetPricingRuleOverview(ctx, req.(*GetPricingRuleOverviewParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PricingRuleService_ServiceDesc is the grpc.ServiceDesc for PricingRuleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PricingRuleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.offering.v2.PricingRuleService",
	HandlerType: (*PricingRuleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertPricingRule",
			Handler:    _PricingRuleService_UpsertPricingRule_Handler,
		},
		{
			MethodName: "GetPricingRule",
			Handler:    _PricingRuleService_GetPricingRule_Handler,
		},
		{
			MethodName: "ListPricingRules",
			Handler:    _PricingRuleService_ListPricingRules_Handler,
		},
		{
			MethodName: "CalculatePricingRule",
			Handler:    _PricingRuleService_CalculatePricingRule_Handler,
		},
		{
			MethodName: "PreviewPricingRule",
			Handler:    _PricingRuleService_PreviewPricingRule_Handler,
		},
		{
			MethodName: "DeletePricingRule",
			Handler:    _PricingRuleService_DeletePricingRule_Handler,
		},
		{
			MethodName: "ListAssociatedServices",
			Handler:    _PricingRuleService_ListAssociatedServices_Handler,
		},
		{
			MethodName: "CheckConfiguration",
			Handler:    _PricingRuleService_CheckConfiguration_Handler,
		},
		{
			MethodName: "GetDiscountSetting",
			Handler:    _PricingRuleService_GetDiscountSetting_Handler,
		},
		{
			MethodName: "UpdateDiscountSetting",
			Handler:    _PricingRuleService_UpdateDiscountSetting_Handler,
		},
		{
			MethodName: "GetPricingRuleOverview",
			Handler:    _PricingRuleService_GetPricingRuleOverview_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/offering/v2/pricing_rule_api.proto",
}
