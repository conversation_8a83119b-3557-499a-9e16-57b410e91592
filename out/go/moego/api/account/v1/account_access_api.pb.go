// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/account/v1/account_access_api.proto

package accountapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// check identifier available request
type CheckIdentifierAvailableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*CheckIdentifierAvailableRequest_Email
	//	*CheckIdentifierAvailableRequest_PhoneNumber
	Identifier isCheckIdentifierAvailableRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *CheckIdentifierAvailableRequest) Reset() {
	*x = CheckIdentifierAvailableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIdentifierAvailableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIdentifierAvailableRequest) ProtoMessage() {}

func (x *CheckIdentifierAvailableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIdentifierAvailableRequest.ProtoReflect.Descriptor instead.
func (*CheckIdentifierAvailableRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{0}
}

func (m *CheckIdentifierAvailableRequest) GetIdentifier() isCheckIdentifierAvailableRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *CheckIdentifierAvailableRequest) GetEmail() string {
	if x, ok := x.GetIdentifier().(*CheckIdentifierAvailableRequest_Email); ok {
		return x.Email
	}
	return ""
}

func (x *CheckIdentifierAvailableRequest) GetPhoneNumber() string {
	if x, ok := x.GetIdentifier().(*CheckIdentifierAvailableRequest_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return ""
}

type isCheckIdentifierAvailableRequest_Identifier interface {
	isCheckIdentifierAvailableRequest_Identifier()
}

type CheckIdentifierAvailableRequest_Email struct {
	// email
	Email string `protobuf:"bytes,1,opt,name=email,proto3,oneof"`
}

type CheckIdentifierAvailableRequest_PhoneNumber struct {
	// phone number
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

func (*CheckIdentifierAvailableRequest_Email) isCheckIdentifierAvailableRequest_Identifier() {}

func (*CheckIdentifierAvailableRequest_PhoneNumber) isCheckIdentifierAvailableRequest_Identifier() {}

// check identifier available response
type CheckIdentifierAvailableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if identifier is used
	Used bool `protobuf:"varint,1,opt,name=used,proto3" json:"used,omitempty"`
}

func (x *CheckIdentifierAvailableResponse) Reset() {
	*x = CheckIdentifierAvailableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIdentifierAvailableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIdentifierAvailableResponse) ProtoMessage() {}

func (x *CheckIdentifierAvailableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIdentifierAvailableResponse.ProtoReflect.Descriptor instead.
func (*CheckIdentifierAvailableResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{1}
}

func (x *CheckIdentifierAvailableResponse) GetUsed() bool {
	if x != nil {
		return x.Used
	}
	return false
}

// account register request
type AccountRegisterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// phone number
	PhoneNumber *string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// password
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// verification
	Verification *v1.VerificationCodeDef `protobuf:"bytes,6,opt,name=verification,proto3,oneof" json:"verification,omitempty"`
}

func (x *AccountRegisterRequest) Reset() {
	*x = AccountRegisterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountRegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountRegisterRequest) ProtoMessage() {}

func (x *AccountRegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountRegisterRequest.ProtoReflect.Descriptor instead.
func (*AccountRegisterRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{2}
}

func (x *AccountRegisterRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AccountRegisterRequest) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *AccountRegisterRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *AccountRegisterRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *AccountRegisterRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *AccountRegisterRequest) GetVerification() *v1.VerificationCodeDef {
	if x != nil {
		return x.Verification
	}
	return nil
}

// account register response
type AccountRegisterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AccountRegisterResponse) Reset() {
	*x = AccountRegisterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountRegisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountRegisterResponse) ProtoMessage() {}

func (x *AccountRegisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountRegisterResponse.ProtoReflect.Descriptor instead.
func (*AccountRegisterResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{3}
}

// login by token def
type TokenDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *TokenDef) Reset() {
	*x = TokenDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenDef) ProtoMessage() {}

func (x *TokenDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenDef.ProtoReflect.Descriptor instead.
func (*TokenDef) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{4}
}

func (x *TokenDef) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// login by email and password def
type EmailPasswordDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// password
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *EmailPasswordDef) Reset() {
	*x = EmailPasswordDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailPasswordDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailPasswordDef) ProtoMessage() {}

func (x *EmailPasswordDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailPasswordDef.ProtoReflect.Descriptor instead.
func (*EmailPasswordDef) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{5}
}

func (x *EmailPasswordDef) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *EmailPasswordDef) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// login by phone number and verification code def
type PhoneNumberVerificationCodeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phone number
	PhoneNumber string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// scenario
	Scenario v1.VerificationCodeScenario `protobuf:"varint,2,opt,name=scenario,proto3,enum=moego.models.risk_control.v1.VerificationCodeScenario" json:"scenario,omitempty"`
	// verification token
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	// verification code
	Code string `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *PhoneNumberVerificationCodeDef) Reset() {
	*x = PhoneNumberVerificationCodeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneNumberVerificationCodeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneNumberVerificationCodeDef) ProtoMessage() {}

func (x *PhoneNumberVerificationCodeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneNumberVerificationCodeDef.ProtoReflect.Descriptor instead.
func (*PhoneNumberVerificationCodeDef) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{6}
}

func (x *PhoneNumberVerificationCodeDef) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *PhoneNumberVerificationCodeDef) GetScenario() v1.VerificationCodeScenario {
	if x != nil {
		return x.Scenario
	}
	return v1.VerificationCodeScenario(0)
}

func (x *PhoneNumberVerificationCodeDef) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *PhoneNumberVerificationCodeDef) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// account login request
type AccountLoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// login method
	//
	// Types that are assignable to LoginMethod:
	//
	//	*AccountLoginRequest_ByToken
	//	*AccountLoginRequest_ByEmailPassword
	LoginMethod isAccountLoginRequest_LoginMethod `protobuf_oneof:"login_method"`
}

func (x *AccountLoginRequest) Reset() {
	*x = AccountLoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountLoginRequest) ProtoMessage() {}

func (x *AccountLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountLoginRequest.ProtoReflect.Descriptor instead.
func (*AccountLoginRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{7}
}

func (m *AccountLoginRequest) GetLoginMethod() isAccountLoginRequest_LoginMethod {
	if m != nil {
		return m.LoginMethod
	}
	return nil
}

func (x *AccountLoginRequest) GetByToken() *TokenDef {
	if x, ok := x.GetLoginMethod().(*AccountLoginRequest_ByToken); ok {
		return x.ByToken
	}
	return nil
}

func (x *AccountLoginRequest) GetByEmailPassword() *EmailPasswordDef {
	if x, ok := x.GetLoginMethod().(*AccountLoginRequest_ByEmailPassword); ok {
		return x.ByEmailPassword
	}
	return nil
}

type isAccountLoginRequest_LoginMethod interface {
	isAccountLoginRequest_LoginMethod()
}

type AccountLoginRequest_ByToken struct {
	// login by token
	ByToken *TokenDef `protobuf:"bytes,1,opt,name=by_token,json=byToken,proto3,oneof"`
}

type AccountLoginRequest_ByEmailPassword struct {
	// login by email and password
	ByEmailPassword *EmailPasswordDef `protobuf:"bytes,2,opt,name=by_email_password,json=byEmailPassword,proto3,oneof"`
}

func (*AccountLoginRequest_ByToken) isAccountLoginRequest_LoginMethod() {}

func (*AccountLoginRequest_ByEmailPassword) isAccountLoginRequest_LoginMethod() {}

// account login response
type AccountLoginResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AccountLoginResponse) Reset() {
	*x = AccountLoginResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountLoginResponse) ProtoMessage() {}

func (x *AccountLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountLoginResponse.ProtoReflect.Descriptor instead.
func (*AccountLoginResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{8}
}

// register or login request
type RegisterOrLoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// access method
	//
	// Types that are assignable to AccessMethod:
	//
	//	*RegisterOrLoginRequest_ByPhoneVerifyCode
	AccessMethod isRegisterOrLoginRequest_AccessMethod `protobuf_oneof:"access_method"`
	// namespace, default is MOEGO(id=0)
	Namespace *v11.NamespaceDef `protobuf:"bytes,2,opt,name=namespace,proto3,oneof" json:"namespace,omitempty"`
}

func (x *RegisterOrLoginRequest) Reset() {
	*x = RegisterOrLoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterOrLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterOrLoginRequest) ProtoMessage() {}

func (x *RegisterOrLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterOrLoginRequest.ProtoReflect.Descriptor instead.
func (*RegisterOrLoginRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{9}
}

func (m *RegisterOrLoginRequest) GetAccessMethod() isRegisterOrLoginRequest_AccessMethod {
	if m != nil {
		return m.AccessMethod
	}
	return nil
}

func (x *RegisterOrLoginRequest) GetByPhoneVerifyCode() *PhoneNumberVerificationCodeDef {
	if x, ok := x.GetAccessMethod().(*RegisterOrLoginRequest_ByPhoneVerifyCode); ok {
		return x.ByPhoneVerifyCode
	}
	return nil
}

func (x *RegisterOrLoginRequest) GetNamespace() *v11.NamespaceDef {
	if x != nil {
		return x.Namespace
	}
	return nil
}

type isRegisterOrLoginRequest_AccessMethod interface {
	isRegisterOrLoginRequest_AccessMethod()
}

type RegisterOrLoginRequest_ByPhoneVerifyCode struct {
	// login by phone and verify code
	ByPhoneVerifyCode *PhoneNumberVerificationCodeDef `protobuf:"bytes,1,opt,name=by_phone_verify_code,json=byPhoneVerifyCode,proto3,oneof"`
}

func (*RegisterOrLoginRequest_ByPhoneVerifyCode) isRegisterOrLoginRequest_AccessMethod() {}

// register or login response
type RegisterOrLoginResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// return true if register as a new account, false if login as an existing account
	Registered bool `protobuf:"varint,1,opt,name=registered,proto3" json:"registered,omitempty"`
}

func (x *RegisterOrLoginResponse) Reset() {
	*x = RegisterOrLoginResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterOrLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterOrLoginResponse) ProtoMessage() {}

func (x *RegisterOrLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterOrLoginResponse.ProtoReflect.Descriptor instead.
func (*RegisterOrLoginResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{10}
}

func (x *RegisterOrLoginResponse) GetRegistered() bool {
	if x != nil {
		return x.Registered
	}
	return false
}

// fork session request
type ForkSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ForkSessionRequest) Reset() {
	*x = ForkSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForkSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForkSessionRequest) ProtoMessage() {}

func (x *ForkSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForkSessionRequest.ProtoReflect.Descriptor instead.
func (*ForkSessionRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{11}
}

// fork session response
type ForkSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// new session token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *ForkSessionResponse) Reset() {
	*x = ForkSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForkSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForkSessionResponse) ProtoMessage() {}

func (x *ForkSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_account_v1_account_access_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForkSessionResponse.ProtoReflect.Descriptor instead.
func (*ForkSessionResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_account_v1_account_access_api_proto_rawDescGZIP(), []int{12}
}

func (x *ForkSessionResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_moego_api_account_v1_account_access_api_proto protoreflect.FileDescriptor

var file_moego_api_account_v1_account_access_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x96,
	0x01, 0x0a, 0x1f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x64, 0x60, 0x01, 0x48, 0x00, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x3d, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15,
	0x72, 0x13, 0x32, 0x11, 0x5e, 0x5c, 0x2b, 0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5c, 0x64, 0x7b, 0x31,
	0x2c, 0x31, 0x38, 0x7d, 0x24, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x36, 0x0a, 0x20, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x22,
	0xf1, 0x02, 0x0a, 0x16, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x18, 0x64, 0x60, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x43, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1b, 0xfa, 0x42, 0x18, 0x72, 0x16, 0x32, 0x11, 0x5e, 0x5c, 0x2b, 0x5b, 0x31, 0x2d,
	0x39, 0x5d, 0x5c, 0x64, 0x7b, 0x31, 0x2c, 0x31, 0x38, 0x7d, 0x24, 0xd0, 0x01, 0x01, 0x48, 0x00,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x25, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x06, 0x18, 0x64, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x32, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x24, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x08, 0x6c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5a, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x66, 0x48, 0x01,
	0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x19, 0x0a, 0x17, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c,
	0x0a, 0x08, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x20, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0x80, 0x02, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x58, 0x0a, 0x10,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x66,
	0x12, 0x1f, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x03, 0x18, 0x64, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x23, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0xe7, 0x01, 0x0a, 0x1e, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x66, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x5c, 0x2b, 0x5b, 0x31, 0x2d, 0x39, 0x5d,
	0x5c, 0x64, 0x7b, 0x31, 0x2c, 0x31, 0x38, 0x7d, 0x24, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x5e, 0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72,
	0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x73, 0x63,
	0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0xbd, 0x01, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x08, 0x62, 0x79, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07, 0x62, 0x79,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x54, 0x0a, 0x11, 0x62, 0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0f, 0x62, 0x79, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x13, 0x0a, 0x0c, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x03, 0xf8, 0x42, 0x01,
	0x22, 0x16, 0x0a, 0x14, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xef, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x67, 0x0a, 0x14, 0x62, 0x79, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x11, 0x62, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x48, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0x39, 0x0a, 0x17, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x65, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x46, 0x6f, 0x72, 0x6b, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2b, 0x0a, 0x13, 0x46,
	0x6f, 0x72, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0xf9, 0x04, 0x0a, 0x14, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x89, 0x01, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a,
	0x08, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x0f, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x4f, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4f, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74,
	0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x62, 0x0a, 0x0b, 0x46, 0x6f, 0x72, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x72, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x6f, 0x72, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x78, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_account_v1_account_access_api_proto_rawDescOnce sync.Once
	file_moego_api_account_v1_account_access_api_proto_rawDescData = file_moego_api_account_v1_account_access_api_proto_rawDesc
)

func file_moego_api_account_v1_account_access_api_proto_rawDescGZIP() []byte {
	file_moego_api_account_v1_account_access_api_proto_rawDescOnce.Do(func() {
		file_moego_api_account_v1_account_access_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_account_v1_account_access_api_proto_rawDescData)
	})
	return file_moego_api_account_v1_account_access_api_proto_rawDescData
}

var file_moego_api_account_v1_account_access_api_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_api_account_v1_account_access_api_proto_goTypes = []interface{}{
	(*CheckIdentifierAvailableRequest)(nil),  // 0: moego.api.account.v1.CheckIdentifierAvailableRequest
	(*CheckIdentifierAvailableResponse)(nil), // 1: moego.api.account.v1.CheckIdentifierAvailableResponse
	(*AccountRegisterRequest)(nil),           // 2: moego.api.account.v1.AccountRegisterRequest
	(*AccountRegisterResponse)(nil),          // 3: moego.api.account.v1.AccountRegisterResponse
	(*TokenDef)(nil),                         // 4: moego.api.account.v1.TokenDef
	(*EmailPasswordDef)(nil),                 // 5: moego.api.account.v1.EmailPasswordDef
	(*PhoneNumberVerificationCodeDef)(nil),   // 6: moego.api.account.v1.PhoneNumberVerificationCodeDef
	(*AccountLoginRequest)(nil),              // 7: moego.api.account.v1.AccountLoginRequest
	(*AccountLoginResponse)(nil),             // 8: moego.api.account.v1.AccountLoginResponse
	(*RegisterOrLoginRequest)(nil),           // 9: moego.api.account.v1.RegisterOrLoginRequest
	(*RegisterOrLoginResponse)(nil),          // 10: moego.api.account.v1.RegisterOrLoginResponse
	(*ForkSessionRequest)(nil),               // 11: moego.api.account.v1.ForkSessionRequest
	(*ForkSessionResponse)(nil),              // 12: moego.api.account.v1.ForkSessionResponse
	(*v1.VerificationCodeDef)(nil),           // 13: moego.models.risk_control.v1.VerificationCodeDef
	(v1.VerificationCodeScenario)(0),         // 14: moego.models.risk_control.v1.VerificationCodeScenario
	(*v11.NamespaceDef)(nil),                 // 15: moego.models.account.v1.NamespaceDef
	(*emptypb.Empty)(nil),                    // 16: google.protobuf.Empty
}
var file_moego_api_account_v1_account_access_api_proto_depIdxs = []int32{
	13, // 0: moego.api.account.v1.AccountRegisterRequest.verification:type_name -> moego.models.risk_control.v1.VerificationCodeDef
	14, // 1: moego.api.account.v1.PhoneNumberVerificationCodeDef.scenario:type_name -> moego.models.risk_control.v1.VerificationCodeScenario
	4,  // 2: moego.api.account.v1.AccountLoginRequest.by_token:type_name -> moego.api.account.v1.TokenDef
	5,  // 3: moego.api.account.v1.AccountLoginRequest.by_email_password:type_name -> moego.api.account.v1.EmailPasswordDef
	6,  // 4: moego.api.account.v1.RegisterOrLoginRequest.by_phone_verify_code:type_name -> moego.api.account.v1.PhoneNumberVerificationCodeDef
	15, // 5: moego.api.account.v1.RegisterOrLoginRequest.namespace:type_name -> moego.models.account.v1.NamespaceDef
	0,  // 6: moego.api.account.v1.AccountAccessService.CheckIdentifierAvailable:input_type -> moego.api.account.v1.CheckIdentifierAvailableRequest
	2,  // 7: moego.api.account.v1.AccountAccessService.Register:input_type -> moego.api.account.v1.AccountRegisterRequest
	7,  // 8: moego.api.account.v1.AccountAccessService.Login:input_type -> moego.api.account.v1.AccountLoginRequest
	9,  // 9: moego.api.account.v1.AccountAccessService.RegisterOrLogin:input_type -> moego.api.account.v1.RegisterOrLoginRequest
	16, // 10: moego.api.account.v1.AccountAccessService.Logout:input_type -> google.protobuf.Empty
	11, // 11: moego.api.account.v1.AccountAccessService.ForkSession:input_type -> moego.api.account.v1.ForkSessionRequest
	1,  // 12: moego.api.account.v1.AccountAccessService.CheckIdentifierAvailable:output_type -> moego.api.account.v1.CheckIdentifierAvailableResponse
	3,  // 13: moego.api.account.v1.AccountAccessService.Register:output_type -> moego.api.account.v1.AccountRegisterResponse
	8,  // 14: moego.api.account.v1.AccountAccessService.Login:output_type -> moego.api.account.v1.AccountLoginResponse
	10, // 15: moego.api.account.v1.AccountAccessService.RegisterOrLogin:output_type -> moego.api.account.v1.RegisterOrLoginResponse
	16, // 16: moego.api.account.v1.AccountAccessService.Logout:output_type -> google.protobuf.Empty
	12, // 17: moego.api.account.v1.AccountAccessService.ForkSession:output_type -> moego.api.account.v1.ForkSessionResponse
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_moego_api_account_v1_account_access_api_proto_init() }
func file_moego_api_account_v1_account_access_api_proto_init() {
	if File_moego_api_account_v1_account_access_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_account_v1_account_access_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIdentifierAvailableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIdentifierAvailableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountRegisterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountRegisterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailPasswordDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneNumberVerificationCodeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountLoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountLoginResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterOrLoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterOrLoginResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForkSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_account_v1_account_access_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForkSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_account_v1_account_access_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CheckIdentifierAvailableRequest_Email)(nil),
		(*CheckIdentifierAvailableRequest_PhoneNumber)(nil),
	}
	file_moego_api_account_v1_account_access_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_account_v1_account_access_api_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*AccountLoginRequest_ByToken)(nil),
		(*AccountLoginRequest_ByEmailPassword)(nil),
	}
	file_moego_api_account_v1_account_access_api_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*RegisterOrLoginRequest_ByPhoneVerifyCode)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_account_v1_account_access_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_account_v1_account_access_api_proto_goTypes,
		DependencyIndexes: file_moego_api_account_v1_account_access_api_proto_depIdxs,
		MessageInfos:      file_moego_api_account_v1_account_access_api_proto_msgTypes,
	}.Build()
	File_moego_api_account_v1_account_access_api_proto = out.File
	file_moego_api_account_v1_account_access_api_proto_rawDesc = nil
	file_moego_api_account_v1_account_access_api_proto_goTypes = nil
	file_moego_api_account_v1_account_access_api_proto_depIdxs = nil
}
