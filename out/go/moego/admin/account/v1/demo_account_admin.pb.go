// @since 2023-06-24 15:07:57
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/account/v1/demo_account_admin.proto

package accountapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CreateDemoAccountParams is the request params of CreateDemoAccount
type CreateDemoAccountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email is the email of the account
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// first_name is the first name of the account
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last_name is the last name of the account
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// business_name is the business name of the account
	BusinessName string `protobuf:"bytes,4,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// address1 is the address1 of the account
	Address1 string `protobuf:"bytes,5,opt,name=address1,proto3" json:"address1,omitempty"`
	// address2 is the address2 of the account
	AddressCity string `protobuf:"bytes,6,opt,name=address_city,json=addressCity,proto3" json:"address_city,omitempty"`
	// state
	AddressState string `protobuf:"bytes,7,opt,name=address_state,json=addressState,proto3" json:"address_state,omitempty"`
	// country
	AddressCountry string `protobuf:"bytes,8,opt,name=address_country,json=addressCountry,proto3" json:"address_country,omitempty"`
	// zipcode
	AddressZipcode string `protobuf:"bytes,9,opt,name=address_zipcode,json=addressZipcode,proto3" json:"address_zipcode,omitempty"`
	// business_type
	AppType int32 `protobuf:"varint,10,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	// lat
	AddressLat float64 `protobuf:"fixed64,11,opt,name=address_lat,json=addressLat,proto3" json:"address_lat,omitempty"`
	// lng
	AddressLng float64 `protobuf:"fixed64,12,opt,name=address_lng,json=addressLng,proto3" json:"address_lng,omitempty"`
	// source
	Source int32 `protobuf:"varint,13,opt,name=source,proto3" json:"source,omitempty"`
	// phone_number
	PhoneNumber string `protobuf:"bytes,14,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// website
	Website string `protobuf:"bytes,15,opt,name=website,proto3" json:"website,omitempty"`
	// know_about_us
	KnowAboutUs string `protobuf:"bytes,16,opt,name=know_about_us,json=knowAboutUs,proto3" json:"know_about_us,omitempty"`
	// country
	Country string `protobuf:"bytes,17,opt,name=country,proto3" json:"country,omitempty"`
	// country_alpha2_code
	CountryAlpha2Code string `protobuf:"bytes,18,opt,name=country_alpha2_code,json=countryAlpha2Code,proto3" json:"country_alpha2_code,omitempty"`
	// how_many_locations
	HowManyLocations int32 `protobuf:"varint,19,opt,name=how_many_locations,json=howManyLocations,proto3" json:"how_many_locations,omitempty"`
	// how_many_staff
	HowManyStaff int32 `protobuf:"varint,20,opt,name=how_many_staff,json=howManyStaff,proto3" json:"how_many_staff,omitempty"`
	// timezone_name
	TimezoneName string `protobuf:"bytes,21,opt,name=timezone_name,json=timezoneName,proto3" json:"timezone_name,omitempty"`
	// timezone_seconds
	TimezoneSeconds int32 `protobuf:"varint,22,opt,name=timezone_seconds,json=timezoneSeconds,proto3" json:"timezone_seconds,omitempty"`
	// currency_symbol
	CurrencySymbol string `protobuf:"bytes,23,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// currency_code
	CurrencyCode string `protobuf:"bytes,24,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// date_format_type
	DateFormatType int32 `protobuf:"varint,25,opt,name=date_format_type,json=dateFormatType,proto3" json:"date_format_type,omitempty"`
	// source_from
	SourceFrom int32 `protobuf:"varint,26,opt,name=source_from,json=sourceFrom,proto3" json:"source_from,omitempty"`
	// appt_per_week
	ApptPerWeek int32 `protobuf:"varint,27,opt,name=appt_per_week,json=apptPerWeek,proto3" json:"appt_per_week,omitempty"`
	// business_years
	BusinessYears int32 `protobuf:"varint,28,opt,name=business_years,json=businessYears,proto3" json:"business_years,omitempty"`
	// move_from
	MoveFrom int32 `protobuf:"varint,29,opt,name=move_from,json=moveFrom,proto3" json:"move_from,omitempty"`
	// retail_enable
	RetailEnable int32 `protobuf:"varint,30,opt,name=retail_enable,json=retailEnable,proto3" json:"retail_enable,omitempty"`
	// password
	Password string `protobuf:"bytes,31,opt,name=password,proto3" json:"password,omitempty"`
	// van num
	VansNum *int32 `protobuf:"varint,32,opt,name=vans_num,json=vansNum,proto3,oneof" json:"vans_num,omitempty"`
	// location num
	LocationNum *int32 `protobuf:"varint,33,opt,name=location_num,json=locationNum,proto3,oneof" json:"location_num,omitempty"`
	// enable square
	SquareEnabled bool `protobuf:"varint,34,opt,name=square_enabled,json=squareEnabled,proto3" json:"square_enabled,omitempty"`
	// enable boarding daycare
	EnableBoardingDaycare bool `protobuf:"varint,35,opt,name=enable_boarding_daycare,json=enableBoardingDaycare,proto3" json:"enable_boarding_daycare,omitempty"`
}

func (x *CreateDemoAccountParams) Reset() {
	*x = CreateDemoAccountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDemoAccountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDemoAccountParams) ProtoMessage() {}

func (x *CreateDemoAccountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDemoAccountParams.ProtoReflect.Descriptor instead.
func (*CreateDemoAccountParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_demo_account_admin_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDemoAccountParams) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateDemoAccountParams) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CreateDemoAccountParams) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CreateDemoAccountParams) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *CreateDemoAccountParams) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *CreateDemoAccountParams) GetAddressCity() string {
	if x != nil {
		return x.AddressCity
	}
	return ""
}

func (x *CreateDemoAccountParams) GetAddressState() string {
	if x != nil {
		return x.AddressState
	}
	return ""
}

func (x *CreateDemoAccountParams) GetAddressCountry() string {
	if x != nil {
		return x.AddressCountry
	}
	return ""
}

func (x *CreateDemoAccountParams) GetAddressZipcode() string {
	if x != nil {
		return x.AddressZipcode
	}
	return ""
}

func (x *CreateDemoAccountParams) GetAppType() int32 {
	if x != nil {
		return x.AppType
	}
	return 0
}

func (x *CreateDemoAccountParams) GetAddressLat() float64 {
	if x != nil {
		return x.AddressLat
	}
	return 0
}

func (x *CreateDemoAccountParams) GetAddressLng() float64 {
	if x != nil {
		return x.AddressLng
	}
	return 0
}

func (x *CreateDemoAccountParams) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *CreateDemoAccountParams) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CreateDemoAccountParams) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

func (x *CreateDemoAccountParams) GetKnowAboutUs() string {
	if x != nil {
		return x.KnowAboutUs
	}
	return ""
}

func (x *CreateDemoAccountParams) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *CreateDemoAccountParams) GetCountryAlpha2Code() string {
	if x != nil {
		return x.CountryAlpha2Code
	}
	return ""
}

func (x *CreateDemoAccountParams) GetHowManyLocations() int32 {
	if x != nil {
		return x.HowManyLocations
	}
	return 0
}

func (x *CreateDemoAccountParams) GetHowManyStaff() int32 {
	if x != nil {
		return x.HowManyStaff
	}
	return 0
}

func (x *CreateDemoAccountParams) GetTimezoneName() string {
	if x != nil {
		return x.TimezoneName
	}
	return ""
}

func (x *CreateDemoAccountParams) GetTimezoneSeconds() int32 {
	if x != nil {
		return x.TimezoneSeconds
	}
	return 0
}

func (x *CreateDemoAccountParams) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *CreateDemoAccountParams) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *CreateDemoAccountParams) GetDateFormatType() int32 {
	if x != nil {
		return x.DateFormatType
	}
	return 0
}

func (x *CreateDemoAccountParams) GetSourceFrom() int32 {
	if x != nil {
		return x.SourceFrom
	}
	return 0
}

func (x *CreateDemoAccountParams) GetApptPerWeek() int32 {
	if x != nil {
		return x.ApptPerWeek
	}
	return 0
}

func (x *CreateDemoAccountParams) GetBusinessYears() int32 {
	if x != nil {
		return x.BusinessYears
	}
	return 0
}

func (x *CreateDemoAccountParams) GetMoveFrom() int32 {
	if x != nil {
		return x.MoveFrom
	}
	return 0
}

func (x *CreateDemoAccountParams) GetRetailEnable() int32 {
	if x != nil {
		return x.RetailEnable
	}
	return 0
}

func (x *CreateDemoAccountParams) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateDemoAccountParams) GetVansNum() int32 {
	if x != nil && x.VansNum != nil {
		return *x.VansNum
	}
	return 0
}

func (x *CreateDemoAccountParams) GetLocationNum() int32 {
	if x != nil && x.LocationNum != nil {
		return *x.LocationNum
	}
	return 0
}

func (x *CreateDemoAccountParams) GetSquareEnabled() bool {
	if x != nil {
		return x.SquareEnabled
	}
	return false
}

func (x *CreateDemoAccountParams) GetEnableBoardingDaycare() bool {
	if x != nil {
		return x.EnableBoardingDaycare
	}
	return false
}

// CreateDemoAccountResult is the response result of CreateDemoAccount
type CreateDemoAccountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account
	Account *v1.AccountModel `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	// business
	DeprecatedBusiness *structpb.Struct `protobuf:"bytes,2,opt,name=deprecated_business,json=deprecatedBusiness,proto3" json:"deprecated_business,omitempty"`
	// staff
	DeprecatedStaff *structpb.Struct `protobuf:"bytes,3,opt,name=deprecated_staff,json=deprecatedStaff,proto3" json:"deprecated_staff,omitempty"`
	// subscription
	DeprecatedSubscriptionState *structpb.Struct `protobuf:"bytes,4,opt,name=deprecated_subscription_state,json=deprecatedSubscriptionState,proto3" json:"deprecated_subscription_state,omitempty"`
	// company
	DeprecatedCompany *structpb.Struct `protobuf:"bytes,5,opt,name=deprecated_company,json=deprecatedCompany,proto3" json:"deprecated_company,omitempty"`
}

func (x *CreateDemoAccountResult) Reset() {
	*x = CreateDemoAccountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDemoAccountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDemoAccountResult) ProtoMessage() {}

func (x *CreateDemoAccountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDemoAccountResult.ProtoReflect.Descriptor instead.
func (*CreateDemoAccountResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_demo_account_admin_proto_rawDescGZIP(), []int{1}
}

func (x *CreateDemoAccountResult) GetAccount() *v1.AccountModel {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *CreateDemoAccountResult) GetDeprecatedBusiness() *structpb.Struct {
	if x != nil {
		return x.DeprecatedBusiness
	}
	return nil
}

func (x *CreateDemoAccountResult) GetDeprecatedStaff() *structpb.Struct {
	if x != nil {
		return x.DeprecatedStaff
	}
	return nil
}

func (x *CreateDemoAccountResult) GetDeprecatedSubscriptionState() *structpb.Struct {
	if x != nil {
		return x.DeprecatedSubscriptionState
	}
	return nil
}

func (x *CreateDemoAccountResult) GetDeprecatedCompany() *structpb.Struct {
	if x != nil {
		return x.DeprecatedCompany
	}
	return nil
}

// UpdateDemoAccountParams
type UpdateDemoAccountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// van num
	VansNum *int32 `protobuf:"varint,32,opt,name=vans_num,json=vansNum,proto3,oneof" json:"vans_num,omitempty"`
	// location num
	LocationNum *int32 `protobuf:"varint,33,opt,name=location_num,json=locationNum,proto3,oneof" json:"location_num,omitempty"`
	// retail_enable
	RetailEnabled *bool `protobuf:"varint,30,opt,name=retail_enabled,json=retailEnabled,proto3,oneof" json:"retail_enabled,omitempty"`
	// enable square
	SquareEnabled *bool `protobuf:"varint,34,opt,name=square_enabled,json=squareEnabled,proto3,oneof" json:"square_enabled,omitempty"`
}

func (x *UpdateDemoAccountParams) Reset() {
	*x = UpdateDemoAccountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDemoAccountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDemoAccountParams) ProtoMessage() {}

func (x *UpdateDemoAccountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDemoAccountParams.ProtoReflect.Descriptor instead.
func (*UpdateDemoAccountParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_demo_account_admin_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateDemoAccountParams) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *UpdateDemoAccountParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateDemoAccountParams) GetVansNum() int32 {
	if x != nil && x.VansNum != nil {
		return *x.VansNum
	}
	return 0
}

func (x *UpdateDemoAccountParams) GetLocationNum() int32 {
	if x != nil && x.LocationNum != nil {
		return *x.LocationNum
	}
	return 0
}

func (x *UpdateDemoAccountParams) GetRetailEnabled() bool {
	if x != nil && x.RetailEnabled != nil {
		return *x.RetailEnabled
	}
	return false
}

func (x *UpdateDemoAccountParams) GetSquareEnabled() bool {
	if x != nil && x.SquareEnabled != nil {
		return *x.SquareEnabled
	}
	return false
}

// UpdateDemoAccountResult
type UpdateDemoAccountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateDemoAccountResult) Reset() {
	*x = UpdateDemoAccountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDemoAccountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDemoAccountResult) ProtoMessage() {}

func (x *UpdateDemoAccountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDemoAccountResult.ProtoReflect.Descriptor instead.
func (*UpdateDemoAccountResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_demo_account_admin_proto_rawDescGZIP(), []int{3}
}

// DescribeDemoAccountsParams is the request params of DescribeDemoAccounts
type DescribeDemoAccountsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email
	EmailLike *string `protobuf:"bytes,1,opt,name=email_like,json=emailLike,proto3,oneof" json:"email_like,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *DescribeDemoAccountsParams) Reset() {
	*x = DescribeDemoAccountsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDemoAccountsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDemoAccountsParams) ProtoMessage() {}

func (x *DescribeDemoAccountsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDemoAccountsParams.ProtoReflect.Descriptor instead.
func (*DescribeDemoAccountsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_demo_account_admin_proto_rawDescGZIP(), []int{4}
}

func (x *DescribeDemoAccountsParams) GetEmailLike() string {
	if x != nil && x.EmailLike != nil {
		return *x.EmailLike
	}
	return ""
}

func (x *DescribeDemoAccountsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// DescribeDemoAccountsResult is the response result of DescribeDemoAccounts
type DescribeDemoAccountsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accounts
	Accounts []*v1.AccountModel `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	// deprecated business map, key is account id
	DeprecatedBusinessMap map[int64]*structpb.Struct `protobuf:"bytes,2,rep,name=deprecated_business_map,json=deprecatedBusinessMap,proto3" json:"deprecated_business_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeDemoAccountsResult) Reset() {
	*x = DescribeDemoAccountsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDemoAccountsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDemoAccountsResult) ProtoMessage() {}

func (x *DescribeDemoAccountsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDemoAccountsResult.ProtoReflect.Descriptor instead.
func (*DescribeDemoAccountsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_demo_account_admin_proto_rawDescGZIP(), []int{5}
}

func (x *DescribeDemoAccountsResult) GetAccounts() []*v1.AccountModel {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *DescribeDemoAccountsResult) GetDeprecatedBusinessMap() map[int64]*structpb.Struct {
	if x != nil {
		return x.DeprecatedBusinessMap
	}
	return nil
}

func (x *DescribeDemoAccountsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

var File_moego_admin_account_v1_demo_account_admin_proto protoreflect.FileDescriptor

var file_moego_admin_account_v1_demo_account_admin_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x6d, 0x6f, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x0c, 0x0a, 0x17, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x10, 0x01, 0x18, 0x32, 0x60, 0x01,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x28, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52,
	0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x0d, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x0c, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x12,
	0x2a, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0b,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x0a, 0x0d, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0c, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x0f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0e, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x30, 0x0a, 0x0f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0e, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x30, 0x00, 0x30, 0x01, 0x30, 0x02, 0x52, 0x07, 0x61, 0x70,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x6c, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12,
	0x12, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x56, 0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x80, 0x56, 0xc0, 0x52, 0x0a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x61, 0x74, 0x12,
	0x38, 0x0a, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x6e, 0x67, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x19, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x80, 0x66, 0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x66, 0xc0, 0x52, 0x0a, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x2a, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a,
	0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e,
	0xfa, 0x42, 0x0b, 0x72, 0x09, 0x18, 0xff, 0x01, 0xd0, 0x01, 0x01, 0x88, 0x01, 0x01, 0x52, 0x07,
	0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x5f,
	0x61, 0x62, 0x6f, 0x75, 0x74, 0x5f, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x41, 0x62, 0x6f,
	0x75, 0x74, 0x55, 0x73, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x07,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x13, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x32, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0x98, 0x01, 0x02, 0xd0, 0x01,
	0x01, 0x52, 0x11, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x41, 0x6c, 0x70, 0x68, 0x61, 0x32,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x68, 0x6f, 0x77, 0x5f, 0x6d, 0x61, 0x6e, 0x79,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x10, 0x68, 0x6f, 0x77, 0x4d, 0x61, 0x6e, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x68, 0x6f, 0x77, 0x5f, 0x6d, 0x61, 0x6e, 0x79, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x68, 0x6f, 0x77, 0x4d,
	0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x2c, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65,
	0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f,
	0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f,
	0x6e, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x73, 0x12, 0x30, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79,
	0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x32, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x12, 0x2c, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x32, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x61, 0x74,
	0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x22, 0x0a, 0x0d,
	0x61, 0x70, 0x70, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x74, 0x50, 0x65, 0x72, 0x57, 0x65, 0x65, 0x6b,
	0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x79, 0x65, 0x61,
	0x72, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x59, 0x65, 0x61, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x76, 0x65, 0x5f,
	0x66, 0x72, 0x6f, 0x6d, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x6f, 0x76, 0x65,
	0x46, 0x72, 0x6f, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09,
	0x72, 0x07, 0x10, 0x0c, 0x18, 0x32, 0xd0, 0x01, 0x01, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x76, 0x61, 0x6e, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x07, 0x76, 0x61, 0x6e, 0x73, 0x4e, 0x75, 0x6d,
	0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x21, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x0b, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x71, 0x75, 0x61, 0x72, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x22, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18, 0x23, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76,
	0x61, 0x6e, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x22, 0x8d, 0x03, 0x0a, 0x17, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x3f, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x13, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x12, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x42, 0x0a, 0x10, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x52, 0x0f, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x12, 0x5b, 0x0a, 0x1d, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x52, 0x1b, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x46, 0x0a, 0x12, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x11, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x22, 0xbb, 0x02, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x76, 0x61, 0x6e, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x20,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x07, 0x76, 0x61, 0x6e, 0x73, 0x4e, 0x75, 0x6d, 0x88,
	0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x21, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x72, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x22, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03,
	0x52, 0x0d, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76, 0x61, 0x6e, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x72, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x19, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0xaf, 0x01, 0x0a, 0x1a, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65,
	0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x2b, 0x0a, 0x0a, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6c, 0x69, 0x6b, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52,
	0x09, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x6b, 0x65, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x6c, 0x69, 0x6b, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x8e, 0x03, 0x0a, 0x1a, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x41, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x85, 0x01, 0x0a, 0x17, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x72,
	0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x42, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0x61, 0x0a, 0x1a, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x32, 0x82, 0x03, 0x0a, 0x12, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x75, 0x0a, 0x11, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x7e, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65,
	0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x6d, 0x6f,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x75, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x6d, 0x6f,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x6d, 0x6f, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7c, 0x0a, 0x1e, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_account_v1_demo_account_admin_proto_rawDescOnce sync.Once
	file_moego_admin_account_v1_demo_account_admin_proto_rawDescData = file_moego_admin_account_v1_demo_account_admin_proto_rawDesc
)

func file_moego_admin_account_v1_demo_account_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_account_v1_demo_account_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_account_v1_demo_account_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_account_v1_demo_account_admin_proto_rawDescData)
	})
	return file_moego_admin_account_v1_demo_account_admin_proto_rawDescData
}

var file_moego_admin_account_v1_demo_account_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_admin_account_v1_demo_account_admin_proto_goTypes = []interface{}{
	(*CreateDemoAccountParams)(nil),    // 0: moego.admin.account.v1.CreateDemoAccountParams
	(*CreateDemoAccountResult)(nil),    // 1: moego.admin.account.v1.CreateDemoAccountResult
	(*UpdateDemoAccountParams)(nil),    // 2: moego.admin.account.v1.UpdateDemoAccountParams
	(*UpdateDemoAccountResult)(nil),    // 3: moego.admin.account.v1.UpdateDemoAccountResult
	(*DescribeDemoAccountsParams)(nil), // 4: moego.admin.account.v1.DescribeDemoAccountsParams
	(*DescribeDemoAccountsResult)(nil), // 5: moego.admin.account.v1.DescribeDemoAccountsResult
	nil,                                // 6: moego.admin.account.v1.DescribeDemoAccountsResult.DeprecatedBusinessMapEntry
	(*v1.AccountModel)(nil),            // 7: moego.models.account.v1.AccountModel
	(*structpb.Struct)(nil),            // 8: google.protobuf.Struct
	(*v2.PaginationRequest)(nil),       // 9: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),      // 10: moego.utils.v2.PaginationResponse
}
var file_moego_admin_account_v1_demo_account_admin_proto_depIdxs = []int32{
	7,  // 0: moego.admin.account.v1.CreateDemoAccountResult.account:type_name -> moego.models.account.v1.AccountModel
	8,  // 1: moego.admin.account.v1.CreateDemoAccountResult.deprecated_business:type_name -> google.protobuf.Struct
	8,  // 2: moego.admin.account.v1.CreateDemoAccountResult.deprecated_staff:type_name -> google.protobuf.Struct
	8,  // 3: moego.admin.account.v1.CreateDemoAccountResult.deprecated_subscription_state:type_name -> google.protobuf.Struct
	8,  // 4: moego.admin.account.v1.CreateDemoAccountResult.deprecated_company:type_name -> google.protobuf.Struct
	9,  // 5: moego.admin.account.v1.DescribeDemoAccountsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	7,  // 6: moego.admin.account.v1.DescribeDemoAccountsResult.accounts:type_name -> moego.models.account.v1.AccountModel
	6,  // 7: moego.admin.account.v1.DescribeDemoAccountsResult.deprecated_business_map:type_name -> moego.admin.account.v1.DescribeDemoAccountsResult.DeprecatedBusinessMapEntry
	10, // 8: moego.admin.account.v1.DescribeDemoAccountsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	8,  // 9: moego.admin.account.v1.DescribeDemoAccountsResult.DeprecatedBusinessMapEntry.value:type_name -> google.protobuf.Struct
	0,  // 10: moego.admin.account.v1.DemoAccountService.CreateDemoAccount:input_type -> moego.admin.account.v1.CreateDemoAccountParams
	4,  // 11: moego.admin.account.v1.DemoAccountService.DescribeDemoAccounts:input_type -> moego.admin.account.v1.DescribeDemoAccountsParams
	2,  // 12: moego.admin.account.v1.DemoAccountService.UpdateDemoAccount:input_type -> moego.admin.account.v1.UpdateDemoAccountParams
	1,  // 13: moego.admin.account.v1.DemoAccountService.CreateDemoAccount:output_type -> moego.admin.account.v1.CreateDemoAccountResult
	5,  // 14: moego.admin.account.v1.DemoAccountService.DescribeDemoAccounts:output_type -> moego.admin.account.v1.DescribeDemoAccountsResult
	3,  // 15: moego.admin.account.v1.DemoAccountService.UpdateDemoAccount:output_type -> moego.admin.account.v1.UpdateDemoAccountResult
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_admin_account_v1_demo_account_admin_proto_init() }
func file_moego_admin_account_v1_demo_account_admin_proto_init() {
	if File_moego_admin_account_v1_demo_account_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDemoAccountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDemoAccountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDemoAccountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDemoAccountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDemoAccountsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDemoAccountsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_account_v1_demo_account_admin_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_account_v1_demo_account_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_account_v1_demo_account_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_account_v1_demo_account_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_account_v1_demo_account_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_account_v1_demo_account_admin_proto = out.File
	file_moego_admin_account_v1_demo_account_admin_proto_rawDesc = nil
	file_moego_admin_account_v1_demo_account_admin_proto_goTypes = nil
	file_moego_admin_account_v1_demo_account_admin_proto_depIdxs = nil
}
