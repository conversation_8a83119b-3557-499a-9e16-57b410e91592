// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v1/reporting_models.proto

package reportingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// message topic
type PaymentReport_Topic int32

const (
	// pulsar namespace/topic: reporting/PAYMENT
	PaymentReport_PAYMENT PaymentReport_Topic = 0
)

// Enum value maps for PaymentReport_Topic.
var (
	PaymentReport_Topic_name = map[int32]string{
		0: "PAYMENT",
	}
	PaymentReport_Topic_value = map[string]int32{
		"PAYMENT": 0,
	}
)

func (x PaymentReport_Topic) Enum() *PaymentReport_Topic {
	p := new(PaymentReport_Topic)
	*p = x
	return p
}

func (x PaymentReport_Topic) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentReport_Topic) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v1_reporting_models_proto_enumTypes[0].Descriptor()
}

func (PaymentReport_Topic) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v1_reporting_models_proto_enumTypes[0]
}

func (x PaymentReport_Topic) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentReport_Topic.Descriptor instead.
func (PaymentReport_Topic) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_reporting_models_proto_rawDescGZIP(), []int{1, 0}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// message topic
type AppointmentReport_Topic int32

const (
	// pulsar namespace/topic: reporting/APPOINTMENT
	AppointmentReport_APPOINTMENT AppointmentReport_Topic = 0
)

// Enum value maps for AppointmentReport_Topic.
var (
	AppointmentReport_Topic_name = map[int32]string{
		0: "APPOINTMENT",
	}
	AppointmentReport_Topic_value = map[string]int32{
		"APPOINTMENT": 0,
	}
)

func (x AppointmentReport_Topic) Enum() *AppointmentReport_Topic {
	p := new(AppointmentReport_Topic)
	*p = x
	return p
}

func (x AppointmentReport_Topic) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentReport_Topic) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v1_reporting_models_proto_enumTypes[1].Descriptor()
}

func (AppointmentReport_Topic) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v1_reporting_models_proto_enumTypes[1]
}

func (x AppointmentReport_Topic) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentReport_Topic.Descriptor instead.
func (AppointmentReport_Topic) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_reporting_models_proto_rawDescGZIP(), []int{2, 0}
}

// campaign report
type CampaignReportModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign id
	CampaignId int64 `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	// starter
	Starter string `protobuf:"bytes,2,opt,name=starter,proto3" json:"starter,omitempty"`
	// started at
	StartedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	// notification id
	NotificationId int64 `protobuf:"varint,5,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	// notification title
	NotificationTitle string `protobuf:"bytes,6,opt,name=notification_title,json=notificationTitle,proto3" json:"notification_title,omitempty"`
	// notification body
	NotificationBody string `protobuf:"bytes,7,opt,name=notification_body,json=notificationBody,proto3" json:"notification_body,omitempty"`
	// notification record id
	NotificationRecordId int64 `protobuf:"varint,8,opt,name=notification_record_id,json=notificationRecordId,proto3" json:"notification_record_id,omitempty"`
	// receiver account id
	ReceiverAccountId int64 `protobuf:"varint,9,opt,name=receiver_account_id,json=receiverAccountId,proto3" json:"receiver_account_id,omitempty"`
	// receiver account id
	ReceiverBusinessId int64 `protobuf:"varint,10,opt,name=receiver_business_id,json=receiverBusinessId,proto3" json:"receiver_business_id,omitempty"`
	// receiver account id
	ReceiverCompanyId int64 `protobuf:"varint,11,opt,name=receiver_company_id,json=receiverCompanyId,proto3" json:"receiver_company_id,omitempty"`
	// receiver account id
	ReceiverStaffId int64 `protobuf:"varint,12,opt,name=receiver_staff_id,json=receiverStaffId,proto3" json:"receiver_staff_id,omitempty"`
	// receiver name
	ReceiverName string `protobuf:"bytes,13,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name,omitempty"`
	// is notification read
	IsRead bool `protobuf:"varint,14,opt,name=is_read,json=isRead,proto3" json:"is_read,omitempty"`
	// app clicked at
	ReadAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=read_at,json=readAt,proto3" json:"read_at,omitempty"`
	// is notification clicked on app
	IsAppClicked bool `protobuf:"varint,16,opt,name=is_app_clicked,json=isAppClicked,proto3" json:"is_app_clicked,omitempty"`
	// notification clicked on app at
	AppClickedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=app_clicked_at,json=appClickedAt,proto3" json:"app_clicked_at,omitempty"`
}

func (x *CampaignReportModel) Reset() {
	*x = CampaignReportModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v1_reporting_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignReportModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignReportModel) ProtoMessage() {}

func (x *CampaignReportModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v1_reporting_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignReportModel.ProtoReflect.Descriptor instead.
func (*CampaignReportModel) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_reporting_models_proto_rawDescGZIP(), []int{0}
}

func (x *CampaignReportModel) GetCampaignId() int64 {
	if x != nil {
		return x.CampaignId
	}
	return 0
}

func (x *CampaignReportModel) GetStarter() string {
	if x != nil {
		return x.Starter
	}
	return ""
}

func (x *CampaignReportModel) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *CampaignReportModel) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

func (x *CampaignReportModel) GetNotificationTitle() string {
	if x != nil {
		return x.NotificationTitle
	}
	return ""
}

func (x *CampaignReportModel) GetNotificationBody() string {
	if x != nil {
		return x.NotificationBody
	}
	return ""
}

func (x *CampaignReportModel) GetNotificationRecordId() int64 {
	if x != nil {
		return x.NotificationRecordId
	}
	return 0
}

func (x *CampaignReportModel) GetReceiverAccountId() int64 {
	if x != nil {
		return x.ReceiverAccountId
	}
	return 0
}

func (x *CampaignReportModel) GetReceiverBusinessId() int64 {
	if x != nil {
		return x.ReceiverBusinessId
	}
	return 0
}

func (x *CampaignReportModel) GetReceiverCompanyId() int64 {
	if x != nil {
		return x.ReceiverCompanyId
	}
	return 0
}

func (x *CampaignReportModel) GetReceiverStaffId() int64 {
	if x != nil {
		return x.ReceiverStaffId
	}
	return 0
}

func (x *CampaignReportModel) GetReceiverName() string {
	if x != nil {
		return x.ReceiverName
	}
	return ""
}

func (x *CampaignReportModel) GetIsRead() bool {
	if x != nil {
		return x.IsRead
	}
	return false
}

func (x *CampaignReportModel) GetReadAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadAt
	}
	return nil
}

func (x *CampaignReportModel) GetIsAppClicked() bool {
	if x != nil {
		return x.IsAppClicked
	}
	return false
}

func (x *CampaignReportModel) GetAppClickedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AppClickedAt
	}
	return nil
}

// payment report
type PaymentReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment id
	PaymentId int64 `protobuf:"varint,1,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// rick control rules
	RickControlRules map[string]string `protobuf:"bytes,2,rep,name=rick_control_rules,json=rickControlRules,proto3" json:"rick_control_rules,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// order id
	OrderId int64 `protobuf:"varint,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,7,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *PaymentReport) Reset() {
	*x = PaymentReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v1_reporting_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentReport) ProtoMessage() {}

func (x *PaymentReport) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v1_reporting_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentReport.ProtoReflect.Descriptor instead.
func (*PaymentReport) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_reporting_models_proto_rawDescGZIP(), []int{1}
}

func (x *PaymentReport) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *PaymentReport) GetRickControlRules() map[string]string {
	if x != nil {
		return x.RickControlRules
	}
	return nil
}

func (x *PaymentReport) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *PaymentReport) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *PaymentReport) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PaymentReport) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PaymentReport) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// appointment report
type AppointmentReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// operation id
	OperationId int64 `protobuf:"varint,1,opt,name=operation_id,json=operationId,proto3" json:"operation_id,omitempty"`
	// service_id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// appointment_id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,7,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,8,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// van id
	VanId int64 `protobuf:"varint,9,opt,name=van_id,json=vanId,proto3" json:"van_id,omitempty"`
}

func (x *AppointmentReport) Reset() {
	*x = AppointmentReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v1_reporting_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentReport) ProtoMessage() {}

func (x *AppointmentReport) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v1_reporting_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentReport.ProtoReflect.Descriptor instead.
func (*AppointmentReport) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v1_reporting_models_proto_rawDescGZIP(), []int{2}
}

func (x *AppointmentReport) GetOperationId() int64 {
	if x != nil {
		return x.OperationId
	}
	return 0
}

func (x *AppointmentReport) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *AppointmentReport) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *AppointmentReport) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentReport) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *AppointmentReport) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *AppointmentReport) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AppointmentReport) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AppointmentReport) GetVanId() int64 {
	if x != nil {
		return x.VanId
	}
	return 0
}

var File_moego_models_reporting_v1_reporting_models_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v1_reporting_models_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdf,
	0x05, 0x0a, 0x13, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x72, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x12, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64,
	0x79, 0x12, 0x34, 0x0a, 0x16, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x72, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73,
	0x5f, 0x72, 0x65, 0x61, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x52,
	0x65, 0x61, 0x64, 0x12, 0x33, 0x0a, 0x07, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x64, 0x41, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61,
	0x70, 0x70, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0c, 0x69, 0x73, 0x41, 0x70, 0x70, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x40,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x43, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x74,
	0x22, 0x9a, 0x03, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x6c, 0x0a, 0x12, 0x72, 0x69, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x52, 0x69, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x72,
	0x69, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x1a, 0x43, 0x0a, 0x15, 0x52, 0x69, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x00, 0x22, 0xc0, 0x02,
	0x0a, 0x11, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x76, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x76, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x18, 0x0a, 0x05, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12,
	0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x00,
	0x42, 0x81, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_reporting_v1_reporting_models_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v1_reporting_models_proto_rawDescData = file_moego_models_reporting_v1_reporting_models_proto_rawDesc
)

func file_moego_models_reporting_v1_reporting_models_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v1_reporting_models_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v1_reporting_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v1_reporting_models_proto_rawDescData)
	})
	return file_moego_models_reporting_v1_reporting_models_proto_rawDescData
}

var file_moego_models_reporting_v1_reporting_models_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_reporting_v1_reporting_models_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_reporting_v1_reporting_models_proto_goTypes = []interface{}{
	(PaymentReport_Topic)(0),      // 0: moego.models.reporting.v1.PaymentReport.Topic
	(AppointmentReport_Topic)(0),  // 1: moego.models.reporting.v1.AppointmentReport.Topic
	(*CampaignReportModel)(nil),   // 2: moego.models.reporting.v1.CampaignReportModel
	(*PaymentReport)(nil),         // 3: moego.models.reporting.v1.PaymentReport
	(*AppointmentReport)(nil),     // 4: moego.models.reporting.v1.AppointmentReport
	nil,                           // 5: moego.models.reporting.v1.PaymentReport.RickControlRulesEntry
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
}
var file_moego_models_reporting_v1_reporting_models_proto_depIdxs = []int32{
	6, // 0: moego.models.reporting.v1.CampaignReportModel.started_at:type_name -> google.protobuf.Timestamp
	6, // 1: moego.models.reporting.v1.CampaignReportModel.read_at:type_name -> google.protobuf.Timestamp
	6, // 2: moego.models.reporting.v1.CampaignReportModel.app_clicked_at:type_name -> google.protobuf.Timestamp
	5, // 3: moego.models.reporting.v1.PaymentReport.rick_control_rules:type_name -> moego.models.reporting.v1.PaymentReport.RickControlRulesEntry
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v1_reporting_models_proto_init() }
func file_moego_models_reporting_v1_reporting_models_proto_init() {
	if File_moego_models_reporting_v1_reporting_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v1_reporting_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignReportModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v1_reporting_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v1_reporting_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v1_reporting_models_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v1_reporting_models_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v1_reporting_models_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v1_reporting_models_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v1_reporting_models_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v1_reporting_models_proto = out.File
	file_moego_models_reporting_v1_reporting_models_proto_rawDesc = nil
	file_moego_models_reporting_v1_reporting_models_proto_goTypes = nil
	file_moego_models_reporting_v1_reporting_models_proto_depIdxs = nil
}
