// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v2/field_model.proto

package reportingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Field type enumeration
type Field_Type int32

const (
	// Unspecified field type
	Field_TYPE_UNSPECIFIED Field_Type = 0
	// A money field type
	Field_MONEY Field_Type = 1
	// A text field type
	Field_TEXT Field_Type = 2
	// A date field type
	Field_DATE Field_Type = 3
	// A time field type
	Field_TIME Field_Type = 4
	// A number field type
	Field_NUMBER Field_Type = 5
	// A client field type
	Field_CLIENT_NAME Field_Type = 6
	// An invoice field type
	Field_INVOICE_ID Field_Type = 7
	// A booking field type
	Field_BOOKING_ID Field_Type = 8
	// An avatar field type
	Field_AVATAR_URL Field_Type = 9
	// A decimal number field type
	Field_DECIMAL_NUMBER Field_Type = 10
	// A percentage field type
	Field_PERCENTAGE Field_Type = 11
	// A duration field type, in minutes
	Field_DURATION Field_Type = 12
	// With both date and time info
	Field_DATETIME Field_Type = 13
	// Pet incident ID
	Field_PET_INCIDENT_ID Field_Type = 14
	// color
	Field_COLOR Field_Type = 15
	// Client ID
	Field_CLIENT_ID Field_Type = 16
)

// Enum value maps for Field_Type.
var (
	Field_Type_name = map[int32]string{
		0:  "TYPE_UNSPECIFIED",
		1:  "MONEY",
		2:  "TEXT",
		3:  "DATE",
		4:  "TIME",
		5:  "NUMBER",
		6:  "CLIENT_NAME",
		7:  "INVOICE_ID",
		8:  "BOOKING_ID",
		9:  "AVATAR_URL",
		10: "DECIMAL_NUMBER",
		11: "PERCENTAGE",
		12: "DURATION",
		13: "DATETIME",
		14: "PET_INCIDENT_ID",
		15: "COLOR",
		16: "CLIENT_ID",
	}
	Field_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MONEY":            1,
		"TEXT":             2,
		"DATE":             3,
		"TIME":             4,
		"NUMBER":           5,
		"CLIENT_NAME":      6,
		"INVOICE_ID":       7,
		"BOOKING_ID":       8,
		"AVATAR_URL":       9,
		"DECIMAL_NUMBER":   10,
		"PERCENTAGE":       11,
		"DURATION":         12,
		"DATETIME":         13,
		"PET_INCIDENT_ID":  14,
		"COLOR":            15,
		"CLIENT_ID":        16,
	}
)

func (x Field_Type) Enum() *Field_Type {
	p := new(Field_Type)
	*p = x
	return p
}

func (x Field_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Field_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_field_model_proto_enumTypes[0].Descriptor()
}

func (Field_Type) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_field_model_proto_enumTypes[0]
}

func (x Field_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Field_Type.Descriptor instead.
func (Field_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_field_model_proto_rawDescGZIP(), []int{0, 0}
}

// A field
type Field struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The label of the field, which should be a string between 1 and 50 characters
	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	// The key of the field
	Type Field_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.reporting.v2.Field_Type" json:"type,omitempty"`
	// The key of the field
	Key string `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	// The drill configuration of the field
	DrillConfig *DrillConfig `protobuf:"bytes,4,opt,name=drill_config,json=drillConfig,proto3,oneof" json:"drill_config,omitempty"`
	// Whether the field is sortable
	Sortable bool `protobuf:"varint,5,opt,name=sortable,proto3" json:"sortable,omitempty"`
	// Whether the field is removable
	Removable bool `protobuf:"varint,6,opt,name=removable,proto3" json:"removable,omitempty"`
	// Whether the field can be grouped by
	GroupByEnable bool `protobuf:"varint,7,opt,name=group_by_enable,json=groupByEnable,proto3" json:"group_by_enable,omitempty"`
	// Whether the field is movable
	Movable bool `protobuf:"varint,8,opt,name=movable,proto3" json:"movable,omitempty"`
	// Field trend type: BENEFIT, HARMFUL, NEUTRAL
	Trend Trend `protobuf:"varint,9,opt,name=trend,proto3,enum=moego.models.reporting.v2.Trend" json:"trend,omitempty"`
	// Current field's required permission code
	PermissionCode string `protobuf:"bytes,10,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	// Current field's description/tooltips
	Description *string `protobuf:"bytes,11,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// linkage config to another diagram
	LinkageConfig *LinkageConfig `protobuf:"bytes,12,opt,name=linkage_config,json=linkageConfig,proto3,oneof" json:"linkage_config,omitempty"`
}

func (x *Field) Reset() {
	*x = Field{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_field_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Field) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Field) ProtoMessage() {}

func (x *Field) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_field_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Field.ProtoReflect.Descriptor instead.
func (*Field) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_field_model_proto_rawDescGZIP(), []int{0}
}

func (x *Field) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Field) GetType() Field_Type {
	if x != nil {
		return x.Type
	}
	return Field_TYPE_UNSPECIFIED
}

func (x *Field) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Field) GetDrillConfig() *DrillConfig {
	if x != nil {
		return x.DrillConfig
	}
	return nil
}

func (x *Field) GetSortable() bool {
	if x != nil {
		return x.Sortable
	}
	return false
}

func (x *Field) GetRemovable() bool {
	if x != nil {
		return x.Removable
	}
	return false
}

func (x *Field) GetGroupByEnable() bool {
	if x != nil {
		return x.GroupByEnable
	}
	return false
}

func (x *Field) GetMovable() bool {
	if x != nil {
		return x.Movable
	}
	return false
}

func (x *Field) GetTrend() Trend {
	if x != nil {
		return x.Trend
	}
	return Trend_TREND_UNSPECIFIED
}

func (x *Field) GetPermissionCode() string {
	if x != nil {
		return x.PermissionCode
	}
	return ""
}

func (x *Field) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Field) GetLinkageConfig() *LinkageConfig {
	if x != nil {
		return x.LinkageConfig
	}
	return nil
}

var File_moego_models_reporting_v2_field_model_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v2_field_model_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcc, 0x06, 0x0a, 0x05, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x4e, 0x0a, 0x0c, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x73, 0x6f, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x76, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6d, 0x6f, 0x76, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x36, 0x0a, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x72, 0x65, 0x6e, 0x64,
	0x52, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x6e, 0x6b,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x02, 0x52, 0x0d, 0x6c, 0x69, 0x6e,
	0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x22, 0x81, 0x02,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05,
	0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10,
	0x02, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x54,
	0x49, 0x4d, 0x45, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10,
	0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x44,
	0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x44,
	0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x56, 0x41, 0x54, 0x41, 0x52, 0x5f, 0x55, 0x52, 0x4c,
	0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x43, 0x49, 0x4d, 0x41, 0x4c, 0x5f, 0x4e, 0x55,
	0x4d, 0x42, 0x45, 0x52, 0x10, 0x0a, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e,
	0x54, 0x41, 0x47, 0x45, 0x10, 0x0b, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x0c, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x41, 0x54, 0x45, 0x54, 0x49, 0x4d, 0x45,
	0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x0e, 0x12, 0x09, 0x0a, 0x05, 0x43, 0x4f, 0x4c, 0x4f, 0x52,
	0x10, 0x0f, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10,
	0x10, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x81, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_reporting_v2_field_model_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v2_field_model_proto_rawDescData = file_moego_models_reporting_v2_field_model_proto_rawDesc
)

func file_moego_models_reporting_v2_field_model_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v2_field_model_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v2_field_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v2_field_model_proto_rawDescData)
	})
	return file_moego_models_reporting_v2_field_model_proto_rawDescData
}

var file_moego_models_reporting_v2_field_model_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_reporting_v2_field_model_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_reporting_v2_field_model_proto_goTypes = []interface{}{
	(Field_Type)(0),       // 0: moego.models.reporting.v2.Field.Type
	(*Field)(nil),         // 1: moego.models.reporting.v2.Field
	(*DrillConfig)(nil),   // 2: moego.models.reporting.v2.DrillConfig
	(Trend)(0),            // 3: moego.models.reporting.v2.Trend
	(*LinkageConfig)(nil), // 4: moego.models.reporting.v2.LinkageConfig
}
var file_moego_models_reporting_v2_field_model_proto_depIdxs = []int32{
	0, // 0: moego.models.reporting.v2.Field.type:type_name -> moego.models.reporting.v2.Field.Type
	2, // 1: moego.models.reporting.v2.Field.drill_config:type_name -> moego.models.reporting.v2.DrillConfig
	3, // 2: moego.models.reporting.v2.Field.trend:type_name -> moego.models.reporting.v2.Trend
	4, // 3: moego.models.reporting.v2.Field.linkage_config:type_name -> moego.models.reporting.v2.LinkageConfig
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v2_field_model_proto_init() }
func file_moego_models_reporting_v2_field_model_proto_init() {
	if File_moego_models_reporting_v2_field_model_proto != nil {
		return
	}
	file_moego_models_reporting_v2_common_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v2_field_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Field); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_reporting_v2_field_model_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v2_field_model_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v2_field_model_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v2_field_model_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v2_field_model_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v2_field_model_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v2_field_model_proto = out.File
	file_moego_models_reporting_v2_field_model_proto_rawDesc = nil
	file_moego_models_reporting_v2_field_model_proto_goTypes = nil
	file_moego_models_reporting_v2_field_model_proto_depIdxs = nil
}
