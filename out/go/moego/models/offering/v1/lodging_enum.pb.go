// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/lodging_enum.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// type enum for lodging type
// property of all lodging unit under one lodging type
type LodgingUnitType int32

const (
	// Unspecified lodging unit type
	LodgingUnitType_LODGING_UNIT_TYPE_UNSPECIFIED LodgingUnitType = 0
	// Room/kennel type
	LodgingUnitType_ROOM LodgingUnitType = 1
	// Area type
	LodgingUnitType_AREA LodgingUnitType = 2
)

// Enum value maps for LodgingUnitType.
var (
	LodgingUnitType_name = map[int32]string{
		0: "LODGING_UNIT_TYPE_UNSPECIFIED",
		1: "ROOM",
		2: "AREA",
	}
	LodgingUnitType_value = map[string]int32{
		"LODGING_UNIT_TYPE_UNSPECIFIED": 0,
		"ROOM":                          1,
		"AREA":                          2,
	}
)

func (x LodgingUnitType) Enum() *LodgingUnitType {
	p := new(LodgingUnitType)
	*p = x
	return p
}

func (x LodgingUnitType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LodgingUnitType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_lodging_enum_proto_enumTypes[0].Descriptor()
}

func (LodgingUnitType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_lodging_enum_proto_enumTypes[0]
}

func (x LodgingUnitType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LodgingUnitType.Descriptor instead.
func (LodgingUnitType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_lodging_enum_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_offering_v1_lodging_enum_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_lodging_enum_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2a, 0x48, 0x0a, 0x0f, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f,
	0x44, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x52, 0x4f, 0x4f, 0x4d, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x52, 0x45, 0x41, 0x10,
	0x02, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_lodging_enum_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_lodging_enum_proto_rawDescData = file_moego_models_offering_v1_lodging_enum_proto_rawDesc
)

func file_moego_models_offering_v1_lodging_enum_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_lodging_enum_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_lodging_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_lodging_enum_proto_rawDescData)
	})
	return file_moego_models_offering_v1_lodging_enum_proto_rawDescData
}

var file_moego_models_offering_v1_lodging_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_offering_v1_lodging_enum_proto_goTypes = []interface{}{
	(LodgingUnitType)(0), // 0: moego.models.offering.v1.LodgingUnitType
}
var file_moego_models_offering_v1_lodging_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_lodging_enum_proto_init() }
func file_moego_models_offering_v1_lodging_enum_proto_init() {
	if File_moego_models_offering_v1_lodging_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_lodging_enum_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_lodging_enum_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_lodging_enum_proto_depIdxs,
		EnumInfos:         file_moego_models_offering_v1_lodging_enum_proto_enumTypes,
	}.Build()
	File_moego_models_offering_v1_lodging_enum_proto = out.File
	file_moego_models_offering_v1_lodging_enum_proto_rawDesc = nil
	file_moego_models_offering_v1_lodging_enum_proto_goTypes = nil
	file_moego_models_offering_v1_lodging_enum_proto_depIdxs = nil
}
