// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/order_models.proto

package orderpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// order type
// 自 invoice 4 期开始， order_type 字段含义调整为订单本身的类型，不再用于区分是否为 “主单”
type OrderModel_OrderType int32

const (
	// order type.
	OrderModel_ORDER_TYPE_UNSPECIFIED OrderModel_OrderType = 0
	// origin order
	// 复用定义，含义从主单调整为普通订单 / sales order.
	OrderModel_ORIGIN OrderModel_OrderType = 1
	// extra order.
	// deprecated: please use other types.
	OrderModel_EXTRA OrderModel_OrderType = 2
	// Deposit order.
	OrderModel_DEPOSIT OrderModel_OrderType = 3
	// Tip.
	OrderModel_TIP OrderModel_OrderType = 4
)

// Enum value maps for OrderModel_OrderType.
var (
	OrderModel_OrderType_name = map[int32]string{
		0: "ORDER_TYPE_UNSPECIFIED",
		1: "ORIGIN",
		2: "EXTRA",
		3: "DEPOSIT",
		4: "TIP",
	}
	OrderModel_OrderType_value = map[string]int32{
		"ORDER_TYPE_UNSPECIFIED": 0,
		"ORIGIN":                 1,
		"EXTRA":                  2,
		"DEPOSIT":                3,
		"TIP":                    4,
	}
)

func (x OrderModel_OrderType) Enum() *OrderModel_OrderType {
	p := new(OrderModel_OrderType)
	*p = x
	return p
}

func (x OrderModel_OrderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderModel_OrderType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_order_models_proto_enumTypes[0].Descriptor()
}

func (OrderModel_OrderType) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_order_models_proto_enumTypes[0]
}

func (x OrderModel_OrderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderModel_OrderType.Descriptor instead.
func (OrderModel_OrderType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{1, 0}
}

// payment status
type OrderModel_PaymentStatus int32

const (
	// payment status: UNPAID, PARTIAL_PAID, PAID, PARTIAL_REFUNDED, REFUNDED
	OrderModel_PAYMENT_STATUS_UNSPECIFIED OrderModel_PaymentStatus = 0
	// unpaid
	OrderModel_UNPAID OrderModel_PaymentStatus = 1
	// partial paid
	OrderModel_PARTIAL_PAID OrderModel_PaymentStatus = 2
	// paid
	OrderModel_PAID OrderModel_PaymentStatus = 3
)

// Enum value maps for OrderModel_PaymentStatus.
var (
	OrderModel_PaymentStatus_name = map[int32]string{
		0: "PAYMENT_STATUS_UNSPECIFIED",
		1: "UNPAID",
		2: "PARTIAL_PAID",
		3: "PAID",
	}
	OrderModel_PaymentStatus_value = map[string]int32{
		"PAYMENT_STATUS_UNSPECIFIED": 0,
		"UNPAID":                     1,
		"PARTIAL_PAID":               2,
		"PAID":                       3,
	}
)

func (x OrderModel_PaymentStatus) Enum() *OrderModel_PaymentStatus {
	p := new(OrderModel_PaymentStatus)
	*p = x
	return p
}

func (x OrderModel_PaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderModel_PaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_order_models_proto_enumTypes[1].Descriptor()
}

func (OrderModel_PaymentStatus) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_order_models_proto_enumTypes[1]
}

func (x OrderModel_PaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderModel_PaymentStatus.Descriptor instead.
func (OrderModel_PaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{1, 1}
}

// order fulfillment status: indicate the appointment is finished or not
type OrderModel_FulfillmentStatus int32

const (
	// fulfillment status: UNFULFILLED, PARTIAL_FULFILLED, FULFILLED
	OrderModel_FULFILLMENT_STATUS_UNSPECIFIED OrderModel_FulfillmentStatus = 0
	// init
	OrderModel_INIT OrderModel_FulfillmentStatus = 1
	// completed
	OrderModel_COMPLETED OrderModel_FulfillmentStatus = 2
	// canceled
	OrderModel_CANCELED OrderModel_FulfillmentStatus = 3
)

// Enum value maps for OrderModel_FulfillmentStatus.
var (
	OrderModel_FulfillmentStatus_name = map[int32]string{
		0: "FULFILLMENT_STATUS_UNSPECIFIED",
		1: "INIT",
		2: "COMPLETED",
		3: "CANCELED",
	}
	OrderModel_FulfillmentStatus_value = map[string]int32{
		"FULFILLMENT_STATUS_UNSPECIFIED": 0,
		"INIT":                           1,
		"COMPLETED":                      2,
		"CANCELED":                       3,
	}
)

func (x OrderModel_FulfillmentStatus) Enum() *OrderModel_FulfillmentStatus {
	p := new(OrderModel_FulfillmentStatus)
	*p = x
	return p
}

func (x OrderModel_FulfillmentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderModel_FulfillmentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_order_models_proto_enumTypes[2].Descriptor()
}

func (OrderModel_FulfillmentStatus) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_order_models_proto_enumTypes[2]
}

func (x OrderModel_FulfillmentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderModel_FulfillmentStatus.Descriptor instead.
func (OrderModel_FulfillmentStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{1, 2}
}

// *
// order type view
type OrderModelHistoryView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// order type
	OrderType OrderModel_OrderType `protobuf:"varint,2,opt,name=order_type,json=orderType,proto3,enum=moego.models.order.v1.OrderModel_OrderType" json:"order_type,omitempty"`
	// order status
	Status string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	// order source type
	SourceType OrderSourceType `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// payment status
	PaymentStatus OrderModel_PaymentStatus `protobuf:"varint,5,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderModel_PaymentStatus" json:"payment_status,omitempty"`
	// fulfillment status
	FulfillmentStatus OrderModel_FulfillmentStatus `protobuf:"varint,6,opt,name=fulfillment_status,json=fulfillmentStatus,proto3,enum=moego.models.order.v1.OrderModel_FulfillmentStatus" json:"fulfillment_status,omitempty"`
	// complete time
	CompleteTime int64 `protobuf:"varint,7,opt,name=complete_time,json=completeTime,proto3" json:"complete_time,omitempty"`
	// source id
	SourceId int64 `protobuf:"varint,8,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// paid amount
	PaidAmount float64 `protobuf:"fixed64,9,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount,omitempty"`
	// total amount
	TotalAmount float64 `protobuf:"fixed64,10,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,11,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,12,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// order version
	OrderVersion int32 `protobuf:"varint,13,opt,name=order_version,json=orderVersion,proto3" json:"order_version,omitempty"`
}

func (x *OrderModelHistoryView) Reset() {
	*x = OrderModelHistoryView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderModelHistoryView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderModelHistoryView) ProtoMessage() {}

func (x *OrderModelHistoryView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderModelHistoryView.ProtoReflect.Descriptor instead.
func (*OrderModelHistoryView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{0}
}

func (x *OrderModelHistoryView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderModelHistoryView) GetOrderType() OrderModel_OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderModel_ORDER_TYPE_UNSPECIFIED
}

func (x *OrderModelHistoryView) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *OrderModelHistoryView) GetSourceType() OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return OrderSourceType_ORDER_SOURCE_TYPE_UNSPECIFIED
}

func (x *OrderModelHistoryView) GetPaymentStatus() OrderModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return OrderModel_PAYMENT_STATUS_UNSPECIFIED
}

func (x *OrderModelHistoryView) GetFulfillmentStatus() OrderModel_FulfillmentStatus {
	if x != nil {
		return x.FulfillmentStatus
	}
	return OrderModel_FULFILLMENT_STATUS_UNSPECIFIED
}

func (x *OrderModelHistoryView) GetCompleteTime() int64 {
	if x != nil {
		return x.CompleteTime
	}
	return 0
}

func (x *OrderModelHistoryView) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *OrderModelHistoryView) GetPaidAmount() float64 {
	if x != nil {
		return x.PaidAmount
	}
	return 0
}

func (x *OrderModelHistoryView) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *OrderModelHistoryView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OrderModelHistoryView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderModelHistoryView) GetOrderVersion() int32 {
	if x != nil {
		return x.OrderVersion
	}
	return 0
}

// *
// order info
type OrderModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// order status
	Status int32 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	// order creating source: GROOMING, RETAIL
	SourceType string `protobuf:"bytes,5,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	// payment status: UNPAID, PARTIAL_PAID, PAID, PARTIAL_REFUNDED, REFUNDED
	PaymentStatus *string `protobuf:"bytes,6,opt,name=payment_status,json=paymentStatus,proto3,oneof" json:"payment_status,omitempty"`
	// order guid
	Guid *string `protobuf:"bytes,7,opt,name=guid,proto3,oneof" json:"guid,omitempty"`
	// source id
	SourceId *int64 `protobuf:"varint,8,opt,name=source_id,json=sourceId,proto3,oneof" json:"source_id,omitempty"`
	// line item types: a record for querying
	LineItemTypes *int32 `protobuf:"varint,9,opt,name=line_item_types,json=lineItemTypes,proto3,oneof" json:"line_item_types,omitempty"`
	// order version
	Version *int32 `protobuf:"varint,10,opt,name=version,proto3,oneof" json:"version,omitempty"`
	// tips amount
	TipsAmount *float64 `protobuf:"fixed64,11,opt,name=tips_amount,json=tipsAmount,proto3,oneof" json:"tips_amount,omitempty"`
	// tax amount
	TaxAmount *float64 `protobuf:"fixed64,12,opt,name=tax_amount,json=taxAmount,proto3,oneof" json:"tax_amount,omitempty"`
	// discount amount
	DiscountAmount *float64 `protobuf:"fixed64,13,opt,name=discount_amount,json=discountAmount,proto3,oneof" json:"discount_amount,omitempty"`
	// deducted deposit amount
	DepositAmount *float64 `protobuf:"fixed64,42,opt,name=deposit_amount,json=depositAmount,proto3,oneof" json:"deposit_amount,omitempty"`
	// extra fee amount
	ExtraFeeAmount *float64 `protobuf:"fixed64,14,opt,name=extra_fee_amount,json=extraFeeAmount,proto3,oneof" json:"extra_fee_amount,omitempty"`
	// subTotal amount
	SubTotalAmount *float64 `protobuf:"fixed64,15,opt,name=sub_total_amount,json=subTotalAmount,proto3,oneof" json:"sub_total_amount,omitempty"`
	// amount of tips based on
	TipsBasedAmount *float64 `protobuf:"fixed64,16,opt,name=tips_based_amount,json=tipsBasedAmount,proto3,oneof" json:"tips_based_amount,omitempty"`
	// total amount
	TotalAmount *float64 `protobuf:"fixed64,17,opt,name=total_amount,json=totalAmount,proto3,oneof" json:"total_amount,omitempty"`
	// paid amount
	PaidAmount *float64 `protobuf:"fixed64,18,opt,name=paid_amount,json=paidAmount,proto3,oneof" json:"paid_amount,omitempty"`
	// remain amount to pay
	RemainAmount *float64 `protobuf:"fixed64,19,opt,name=remain_amount,json=remainAmount,proto3,oneof" json:"remain_amount,omitempty"`
	// refunded amount
	RefundedAmount *float64 `protobuf:"fixed64,20,opt,name=refunded_amount,json=refundedAmount,proto3,oneof" json:"refunded_amount,omitempty"`
	// order title
	Title *string `protobuf:"bytes,21,opt,name=title,proto3,oneof" json:"title,omitempty"`
	// staff id of creating this order
	CreateBy *int64 `protobuf:"varint,22,opt,name=create_by,json=createBy,proto3,oneof" json:"create_by,omitempty"`
	// staff id of last updating this order
	UpdateBy *int64 `protobuf:"varint,23,opt,name=update_by,json=updateBy,proto3,oneof" json:"update_by,omitempty"`
	// create time
	CreateTime *int64 `protobuf:"varint,24,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`
	// update time
	UpdateTime *int64 `protobuf:"varint,25,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`
	// fulfillment status: reserved field
	FulfillmentStatus *string `protobuf:"bytes,26,opt,name=fulfillment_status,json=fulfillmentStatus,proto3,oneof" json:"fulfillment_status,omitempty"`
	// description
	Description *string `protobuf:"bytes,28,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// tax applied on this order
	LineTaxes []*OrderLineTaxModel `protobuf:"bytes,29,rep,name=line_taxes,json=lineTaxes,proto3" json:"line_taxes,omitempty"`
	// discount applied on this order
	LineDiscounts []*OrderLineDiscountModel `protobuf:"bytes,30,rep,name=line_discounts,json=lineDiscounts,proto3" json:"line_discounts,omitempty"`
	// extra fee applied on this order
	LineExtraFees []*OrderLineExtraFeeModel `protobuf:"bytes,31,rep,name=line_extra_fees,json=lineExtraFees,proto3" json:"line_extra_fees,omitempty"`
	// appointment source
	Source *v1.AppointmentSource `protobuf:"varint,32,opt,name=source,proto3,enum=moego.models.grooming.v1.AppointmentSource,oneof" json:"source,omitempty"`
	// add companyId
	CompanyId *int64 `protobuf:"varint,33,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// order type
	OrderType *OrderModel_OrderType `protobuf:"varint,34,opt,name=order_type,json=orderType,proto3,enum=moego.models.order.v1.OrderModel_OrderType,oneof" json:"order_type,omitempty"`
	// complete time
	CompleteTime *int64 `protobuf:"varint,35,opt,name=complete_time,json=completeTime,proto3,oneof" json:"complete_time,omitempty"`
	// order ref id
	OrderRefId *int64 `protobuf:"varint,36,opt,name=order_ref_id,json=orderRefId,proto3,oneof" json:"order_ref_id,omitempty"`
	// extra charge reason
	ExtraChargeReason *string `protobuf:"bytes,37,opt,name=extra_charge_reason,json=extraChargeReason,proto3,oneof" json:"extra_charge_reason,omitempty"`
	// order version
	OrderVersion *int32 `protobuf:"varint,38,opt,name=order_version,json=orderVersion,proto3,oneof" json:"order_version,omitempty"`
	// order has extra order
	HasExtraOrder bool `protobuf:"varint,39,opt,name=has_extra_order,json=hasExtraOrder,proto3" json:"has_extra_order,omitempty"`
	// currency code.
	CurrencyCode string `protobuf:"bytes,40,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// 当前订单可支持的退款模式.
	RefundableModes []RefundMode `protobuf:"varint,41,rep,packed,name=refundable_modes,json=refundableModes,proto3,enum=moego.models.order.v1.RefundMode" json:"refundable_modes,omitempty"`
}

func (x *OrderModel) Reset() {
	*x = OrderModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderModel) ProtoMessage() {}

func (x *OrderModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderModel.ProtoReflect.Descriptor instead.
func (*OrderModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{1}
}

func (x *OrderModel) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *OrderModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *OrderModel) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *OrderModel) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

func (x *OrderModel) GetPaymentStatus() string {
	if x != nil && x.PaymentStatus != nil {
		return *x.PaymentStatus
	}
	return ""
}

func (x *OrderModel) GetGuid() string {
	if x != nil && x.Guid != nil {
		return *x.Guid
	}
	return ""
}

func (x *OrderModel) GetSourceId() int64 {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return 0
}

func (x *OrderModel) GetLineItemTypes() int32 {
	if x != nil && x.LineItemTypes != nil {
		return *x.LineItemTypes
	}
	return 0
}

func (x *OrderModel) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *OrderModel) GetTipsAmount() float64 {
	if x != nil && x.TipsAmount != nil {
		return *x.TipsAmount
	}
	return 0
}

func (x *OrderModel) GetTaxAmount() float64 {
	if x != nil && x.TaxAmount != nil {
		return *x.TaxAmount
	}
	return 0
}

func (x *OrderModel) GetDiscountAmount() float64 {
	if x != nil && x.DiscountAmount != nil {
		return *x.DiscountAmount
	}
	return 0
}

func (x *OrderModel) GetDepositAmount() float64 {
	if x != nil && x.DepositAmount != nil {
		return *x.DepositAmount
	}
	return 0
}

func (x *OrderModel) GetExtraFeeAmount() float64 {
	if x != nil && x.ExtraFeeAmount != nil {
		return *x.ExtraFeeAmount
	}
	return 0
}

func (x *OrderModel) GetSubTotalAmount() float64 {
	if x != nil && x.SubTotalAmount != nil {
		return *x.SubTotalAmount
	}
	return 0
}

func (x *OrderModel) GetTipsBasedAmount() float64 {
	if x != nil && x.TipsBasedAmount != nil {
		return *x.TipsBasedAmount
	}
	return 0
}

func (x *OrderModel) GetTotalAmount() float64 {
	if x != nil && x.TotalAmount != nil {
		return *x.TotalAmount
	}
	return 0
}

func (x *OrderModel) GetPaidAmount() float64 {
	if x != nil && x.PaidAmount != nil {
		return *x.PaidAmount
	}
	return 0
}

func (x *OrderModel) GetRemainAmount() float64 {
	if x != nil && x.RemainAmount != nil {
		return *x.RemainAmount
	}
	return 0
}

func (x *OrderModel) GetRefundedAmount() float64 {
	if x != nil && x.RefundedAmount != nil {
		return *x.RefundedAmount
	}
	return 0
}

func (x *OrderModel) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *OrderModel) GetCreateBy() int64 {
	if x != nil && x.CreateBy != nil {
		return *x.CreateBy
	}
	return 0
}

func (x *OrderModel) GetUpdateBy() int64 {
	if x != nil && x.UpdateBy != nil {
		return *x.UpdateBy
	}
	return 0
}

func (x *OrderModel) GetCreateTime() int64 {
	if x != nil && x.CreateTime != nil {
		return *x.CreateTime
	}
	return 0
}

func (x *OrderModel) GetUpdateTime() int64 {
	if x != nil && x.UpdateTime != nil {
		return *x.UpdateTime
	}
	return 0
}

func (x *OrderModel) GetFulfillmentStatus() string {
	if x != nil && x.FulfillmentStatus != nil {
		return *x.FulfillmentStatus
	}
	return ""
}

func (x *OrderModel) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *OrderModel) GetLineTaxes() []*OrderLineTaxModel {
	if x != nil {
		return x.LineTaxes
	}
	return nil
}

func (x *OrderModel) GetLineDiscounts() []*OrderLineDiscountModel {
	if x != nil {
		return x.LineDiscounts
	}
	return nil
}

func (x *OrderModel) GetLineExtraFees() []*OrderLineExtraFeeModel {
	if x != nil {
		return x.LineExtraFees
	}
	return nil
}

func (x *OrderModel) GetSource() v1.AppointmentSource {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.AppointmentSource(0)
}

func (x *OrderModel) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *OrderModel) GetOrderType() OrderModel_OrderType {
	if x != nil && x.OrderType != nil {
		return *x.OrderType
	}
	return OrderModel_ORDER_TYPE_UNSPECIFIED
}

func (x *OrderModel) GetCompleteTime() int64 {
	if x != nil && x.CompleteTime != nil {
		return *x.CompleteTime
	}
	return 0
}

func (x *OrderModel) GetOrderRefId() int64 {
	if x != nil && x.OrderRefId != nil {
		return *x.OrderRefId
	}
	return 0
}

func (x *OrderModel) GetExtraChargeReason() string {
	if x != nil && x.ExtraChargeReason != nil {
		return *x.ExtraChargeReason
	}
	return ""
}

func (x *OrderModel) GetOrderVersion() int32 {
	if x != nil && x.OrderVersion != nil {
		return *x.OrderVersion
	}
	return 0
}

func (x *OrderModel) GetHasExtraOrder() bool {
	if x != nil {
		return x.HasExtraOrder
	}
	return false
}

func (x *OrderModel) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *OrderModel) GetRefundableModes() []RefundMode {
	if x != nil {
		return x.RefundableModes
	}
	return nil
}

// OrderModelV1 与 OrderModel 一致，但是调整部分字段的类型:
// - 枚举字段的类型从 string 调整为真正的 enum 类型.
// - 钱有关的字段从 float64 调整 Money.
type OrderModelV1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// order status
	Status OrderStatus `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.order.v1.OrderStatus" json:"status,omitempty"`
	// order creating source: GROOMING, RETAIL
	SourceType string `protobuf:"bytes,5,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	// payment status: UNPAID, PARTIAL_PAID, PAID, PARTIAL_REFUNDED, REFUNDED
	PaymentStatus OrderModel_PaymentStatus `protobuf:"varint,6,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderModel_PaymentStatus" json:"payment_status,omitempty"`
	// order guid
	Guid string `protobuf:"bytes,7,opt,name=guid,proto3" json:"guid,omitempty"`
	// source id
	SourceId int64 `protobuf:"varint,8,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// line item types: a record for querying
	LineItemTypes int32 `protobuf:"varint,9,opt,name=line_item_types,json=lineItemTypes,proto3" json:"line_item_types,omitempty"`
	// order version
	Version int32 `protobuf:"varint,10,opt,name=version,proto3" json:"version,omitempty"`
	// tips amount
	TipsAmount *money.Money `protobuf:"bytes,11,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// tax amount
	TaxAmount *money.Money `protobuf:"bytes,12,opt,name=tax_amount,json=taxAmount,proto3" json:"tax_amount,omitempty"`
	// discount amount
	DiscountAmount *money.Money `protobuf:"bytes,13,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	// deposit amount
	DepositAmount *money.Money `protobuf:"bytes,42,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// convenience_fee amount
	ConvenienceFee *money.Money `protobuf:"bytes,14,opt,name=convenience_fee,json=convenienceFee,proto3" json:"convenience_fee,omitempty"`
	// subTotal amount
	SubTotalAmount *money.Money `protobuf:"bytes,15,opt,name=sub_total_amount,json=subTotalAmount,proto3" json:"sub_total_amount,omitempty"`
	// amount of tips based on
	TipsBasedAmount *money.Money `protobuf:"bytes,16,opt,name=tips_based_amount,json=tipsBasedAmount,proto3" json:"tips_based_amount,omitempty"`
	// total amount
	TotalAmount *money.Money `protobuf:"bytes,17,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// paid amount
	PaidAmount *money.Money `protobuf:"bytes,18,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount,omitempty"`
	// remain amount to pay
	RemainAmount *money.Money `protobuf:"bytes,19,opt,name=remain_amount,json=remainAmount,proto3" json:"remain_amount,omitempty"`
	// refunded amount
	RefundedAmount *money.Money `protobuf:"bytes,20,opt,name=refunded_amount,json=refundedAmount,proto3" json:"refunded_amount,omitempty"`
	// order title
	Title string `protobuf:"bytes,21,opt,name=title,proto3" json:"title,omitempty"`
	// staff id of creating this order
	CreateBy int64 `protobuf:"varint,22,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`
	// staff id of last updating this order
	UpdateBy int64 `protobuf:"varint,23,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,24,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,25,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// fulfillment status: reserved field
	FulfillmentStatus string `protobuf:"bytes,26,opt,name=fulfillment_status,json=fulfillmentStatus,proto3" json:"fulfillment_status,omitempty"`
	// description
	Description string `protobuf:"bytes,28,opt,name=description,proto3" json:"description,omitempty"`
	// tax applied on this order
	LineTaxes []*OrderLineTaxModel `protobuf:"bytes,29,rep,name=line_taxes,json=lineTaxes,proto3" json:"line_taxes,omitempty"`
	// discount applied on this order
	LineDiscounts []*OrderLineDiscountModel `protobuf:"bytes,30,rep,name=line_discounts,json=lineDiscounts,proto3" json:"line_discounts,omitempty"`
	// extra fee applied on this order
	LineExtraFees []*OrderLineExtraFeeModel `protobuf:"bytes,31,rep,name=line_extra_fees,json=lineExtraFees,proto3" json:"line_extra_fees,omitempty"`
	// appointment source
	Source v1.AppointmentSource `protobuf:"varint,32,opt,name=source,proto3,enum=moego.models.grooming.v1.AppointmentSource" json:"source,omitempty"`
	// add companyId
	CompanyId int64 `protobuf:"varint,33,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// order type
	OrderType OrderModel_OrderType `protobuf:"varint,34,opt,name=order_type,json=orderType,proto3,enum=moego.models.order.v1.OrderModel_OrderType" json:"order_type,omitempty"`
	// complete time
	CompleteTime int64 `protobuf:"varint,35,opt,name=complete_time,json=completeTime,proto3" json:"complete_time,omitempty"`
	// order ref id
	OrderRefId int64 `protobuf:"varint,36,opt,name=order_ref_id,json=orderRefId,proto3" json:"order_ref_id,omitempty"`
	// extra charge reason
	ExtraChargeReason string `protobuf:"bytes,37,opt,name=extra_charge_reason,json=extraChargeReason,proto3" json:"extra_charge_reason,omitempty"`
	// order version
	OrderVersion int32 `protobuf:"varint,38,opt,name=order_version,json=orderVersion,proto3" json:"order_version,omitempty"`
	// order has extra order
	HasExtraOrder bool `protobuf:"varint,39,opt,name=has_extra_order,json=hasExtraOrder,proto3" json:"has_extra_order,omitempty"`
	// currency code.
	CurrencyCode string `protobuf:"bytes,40,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// 当前订单可支持的退款模式.
	RefundableModes []RefundMode `protobuf:"varint,41,rep,packed,name=refundable_modes,json=refundableModes,proto3,enum=moego.models.order.v1.RefundMode" json:"refundable_modes,omitempty"`
}

func (x *OrderModelV1) Reset() {
	*x = OrderModelV1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderModelV1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderModelV1) ProtoMessage() {}

func (x *OrderModelV1) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderModelV1.ProtoReflect.Descriptor instead.
func (*OrderModelV1) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{2}
}

func (x *OrderModelV1) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderModelV1) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderModelV1) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *OrderModelV1) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_CREATED
}

func (x *OrderModelV1) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

func (x *OrderModelV1) GetPaymentStatus() OrderModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return OrderModel_PAYMENT_STATUS_UNSPECIFIED
}

func (x *OrderModelV1) GetGuid() string {
	if x != nil {
		return x.Guid
	}
	return ""
}

func (x *OrderModelV1) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *OrderModelV1) GetLineItemTypes() int32 {
	if x != nil {
		return x.LineItemTypes
	}
	return 0
}

func (x *OrderModelV1) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *OrderModelV1) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *OrderModelV1) GetTaxAmount() *money.Money {
	if x != nil {
		return x.TaxAmount
	}
	return nil
}

func (x *OrderModelV1) GetDiscountAmount() *money.Money {
	if x != nil {
		return x.DiscountAmount
	}
	return nil
}

func (x *OrderModelV1) GetDepositAmount() *money.Money {
	if x != nil {
		return x.DepositAmount
	}
	return nil
}

func (x *OrderModelV1) GetConvenienceFee() *money.Money {
	if x != nil {
		return x.ConvenienceFee
	}
	return nil
}

func (x *OrderModelV1) GetSubTotalAmount() *money.Money {
	if x != nil {
		return x.SubTotalAmount
	}
	return nil
}

func (x *OrderModelV1) GetTipsBasedAmount() *money.Money {
	if x != nil {
		return x.TipsBasedAmount
	}
	return nil
}

func (x *OrderModelV1) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *OrderModelV1) GetPaidAmount() *money.Money {
	if x != nil {
		return x.PaidAmount
	}
	return nil
}

func (x *OrderModelV1) GetRemainAmount() *money.Money {
	if x != nil {
		return x.RemainAmount
	}
	return nil
}

func (x *OrderModelV1) GetRefundedAmount() *money.Money {
	if x != nil {
		return x.RefundedAmount
	}
	return nil
}

func (x *OrderModelV1) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *OrderModelV1) GetCreateBy() int64 {
	if x != nil {
		return x.CreateBy
	}
	return 0
}

func (x *OrderModelV1) GetUpdateBy() int64 {
	if x != nil {
		return x.UpdateBy
	}
	return 0
}

func (x *OrderModelV1) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *OrderModelV1) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *OrderModelV1) GetFulfillmentStatus() string {
	if x != nil {
		return x.FulfillmentStatus
	}
	return ""
}

func (x *OrderModelV1) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *OrderModelV1) GetLineTaxes() []*OrderLineTaxModel {
	if x != nil {
		return x.LineTaxes
	}
	return nil
}

func (x *OrderModelV1) GetLineDiscounts() []*OrderLineDiscountModel {
	if x != nil {
		return x.LineDiscounts
	}
	return nil
}

func (x *OrderModelV1) GetLineExtraFees() []*OrderLineExtraFeeModel {
	if x != nil {
		return x.LineExtraFees
	}
	return nil
}

func (x *OrderModelV1) GetSource() v1.AppointmentSource {
	if x != nil {
		return x.Source
	}
	return v1.AppointmentSource(0)
}

func (x *OrderModelV1) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OrderModelV1) GetOrderType() OrderModel_OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderModel_ORDER_TYPE_UNSPECIFIED
}

func (x *OrderModelV1) GetCompleteTime() int64 {
	if x != nil {
		return x.CompleteTime
	}
	return 0
}

func (x *OrderModelV1) GetOrderRefId() int64 {
	if x != nil {
		return x.OrderRefId
	}
	return 0
}

func (x *OrderModelV1) GetExtraChargeReason() string {
	if x != nil {
		return x.ExtraChargeReason
	}
	return ""
}

func (x *OrderModelV1) GetOrderVersion() int32 {
	if x != nil {
		return x.OrderVersion
	}
	return 0
}

func (x *OrderModelV1) GetHasExtraOrder() bool {
	if x != nil {
		return x.HasExtraOrder
	}
	return false
}

func (x *OrderModelV1) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *OrderModelV1) GetRefundableModes() []RefundMode {
	if x != nil {
		return x.RefundableModes
	}
	return nil
}

// order model v1 history view
type OrderModelV1HistoryView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// order type
	OrderType OrderModel_OrderType `protobuf:"varint,2,opt,name=order_type,json=orderType,proto3,enum=moego.models.order.v1.OrderModel_OrderType" json:"order_type,omitempty"`
	// order status
	Status string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	// order source type
	SourceType OrderSourceType `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// payment status
	PaymentStatus OrderModel_PaymentStatus `protobuf:"varint,5,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderModel_PaymentStatus" json:"payment_status,omitempty"`
	// fulfillment status
	FulfillmentStatus OrderModel_FulfillmentStatus `protobuf:"varint,6,opt,name=fulfillment_status,json=fulfillmentStatus,proto3,enum=moego.models.order.v1.OrderModel_FulfillmentStatus" json:"fulfillment_status,omitempty"`
	// complete time
	CompleteTime int64 `protobuf:"varint,7,opt,name=complete_time,json=completeTime,proto3" json:"complete_time,omitempty"`
	// source id
	SourceId int64 `protobuf:"varint,8,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// tips amount
	TipsAmount *money.Money `protobuf:"bytes,9,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// paid amount
	PaidAmount *money.Money `protobuf:"bytes,10,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount,omitempty"`
	// total amount
	TotalAmount *money.Money `protobuf:"bytes,11,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,12,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,13,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// order version
	OrderVersion int32 `protobuf:"varint,14,opt,name=order_version,json=orderVersion,proto3" json:"order_version,omitempty"`
	// order ref id
	OrderRefId int64 `protobuf:"varint,15,opt,name=order_ref_id,json=orderRefId,proto3" json:"order_ref_id,omitempty"`
}

func (x *OrderModelV1HistoryView) Reset() {
	*x = OrderModelV1HistoryView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderModelV1HistoryView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderModelV1HistoryView) ProtoMessage() {}

func (x *OrderModelV1HistoryView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderModelV1HistoryView.ProtoReflect.Descriptor instead.
func (*OrderModelV1HistoryView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{3}
}

func (x *OrderModelV1HistoryView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderModelV1HistoryView) GetOrderType() OrderModel_OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderModel_ORDER_TYPE_UNSPECIFIED
}

func (x *OrderModelV1HistoryView) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *OrderModelV1HistoryView) GetSourceType() OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return OrderSourceType_ORDER_SOURCE_TYPE_UNSPECIFIED
}

func (x *OrderModelV1HistoryView) GetPaymentStatus() OrderModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return OrderModel_PAYMENT_STATUS_UNSPECIFIED
}

func (x *OrderModelV1HistoryView) GetFulfillmentStatus() OrderModel_FulfillmentStatus {
	if x != nil {
		return x.FulfillmentStatus
	}
	return OrderModel_FULFILLMENT_STATUS_UNSPECIFIED
}

func (x *OrderModelV1HistoryView) GetCompleteTime() int64 {
	if x != nil {
		return x.CompleteTime
	}
	return 0
}

func (x *OrderModelV1HistoryView) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *OrderModelV1HistoryView) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

func (x *OrderModelV1HistoryView) GetPaidAmount() *money.Money {
	if x != nil {
		return x.PaidAmount
	}
	return nil
}

func (x *OrderModelV1HistoryView) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *OrderModelV1HistoryView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OrderModelV1HistoryView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderModelV1HistoryView) GetOrderVersion() int32 {
	if x != nil {
		return x.OrderVersion
	}
	return 0
}

func (x *OrderModelV1HistoryView) GetOrderRefId() int64 {
	if x != nil {
		return x.OrderRefId
	}
	return 0
}

// trigger refund
type RefundChannelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// comb refund
	IsCombination bool `protobuf:"varint,1,opt,name=is_combination,json=isCombination,proto3" json:"is_combination,omitempty"`
	// refund amount
	RefundAmount float64 `protobuf:"fixed64,2,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount,omitempty"`
	// channel
	ChannelList []*RefundChannel `protobuf:"bytes,3,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	// invoice id
	InvoiceId int64 `protobuf:"varint,4,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
}

func (x *RefundChannelResponse) Reset() {
	*x = RefundChannelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundChannelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundChannelResponse) ProtoMessage() {}

func (x *RefundChannelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundChannelResponse.ProtoReflect.Descriptor instead.
func (*RefundChannelResponse) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{4}
}

func (x *RefundChannelResponse) GetIsCombination() bool {
	if x != nil {
		return x.IsCombination
	}
	return false
}

func (x *RefundChannelResponse) GetRefundAmount() float64 {
	if x != nil {
		return x.RefundAmount
	}
	return 0
}

func (x *RefundChannelResponse) GetChannelList() []*RefundChannel {
	if x != nil {
		return x.ChannelList
	}
	return nil
}

func (x *RefundChannelResponse) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

// each refund channel
type RefundChannel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// like : visa(1111)
	PaymentMethod string `protobuf:"bytes,1,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// payment id
	PaymentId int64 `protobuf:"varint,2,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// remain amount
	CanRefundAmount float64 `protobuf:"fixed64,3,opt,name=can_refund_amount,json=canRefundAmount,proto3" json:"can_refund_amount,omitempty"`
}

func (x *RefundChannel) Reset() {
	*x = RefundChannel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundChannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundChannel) ProtoMessage() {}

func (x *RefundChannel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundChannel.ProtoReflect.Descriptor instead.
func (*RefundChannel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{5}
}

func (x *RefundChannel) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *RefundChannel) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *RefundChannel) GetCanRefundAmount() float64 {
	if x != nil {
		return x.CanRefundAmount
	}
	return 0
}

// grooming detail relation model
type GroomingDetailRelationModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// grooming id
	GroomingId int64 `protobuf:"varint,3,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// pet detail id
	PetDetailId int64 `protobuf:"varint,4,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
}

func (x *GroomingDetailRelationModel) Reset() {
	*x = GroomingDetailRelationModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingDetailRelationModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingDetailRelationModel) ProtoMessage() {}

func (x *GroomingDetailRelationModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingDetailRelationModel.ProtoReflect.Descriptor instead.
func (*GroomingDetailRelationModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{6}
}

func (x *GroomingDetailRelationModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroomingDetailRelationModel) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *GroomingDetailRelationModel) GetGroomingId() int64 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *GroomingDetailRelationModel) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

// edit staff commission operation, multi staff item
type EditStaffCommissionOperationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// staff work ratio
	Ratio float64 `protobuf:"fixed64,2,opt,name=ratio,proto3" json:"ratio,omitempty"`
	// staff work duration, min
	Duration int32 `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	// operation name
	OperationName string `protobuf:"bytes,4,opt,name=operation_name,json=operationName,proto3" json:"operation_name,omitempty"`
}

func (x *EditStaffCommissionOperationItem) Reset() {
	*x = EditStaffCommissionOperationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffCommissionOperationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffCommissionOperationItem) ProtoMessage() {}

func (x *EditStaffCommissionOperationItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffCommissionOperationItem.ProtoReflect.Descriptor instead.
func (*EditStaffCommissionOperationItem) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{7}
}

func (x *EditStaffCommissionOperationItem) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *EditStaffCommissionOperationItem) GetRatio() float64 {
	if x != nil {
		return x.Ratio
	}
	return 0
}

func (x *EditStaffCommissionOperationItem) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *EditStaffCommissionOperationItem) GetOperationName() string {
	if x != nil {
		return x.OperationName
	}
	return ""
}

// edit staff commission item
type EditStaffCommissionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	PetDetailId int64 `protobuf:"varint,1,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// company id
	CompanyId *int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// operation list
	Operation []*EditStaffCommissionOperationItem `protobuf:"bytes,7,rep,name=operation,proto3" json:"operation,omitempty"`
	// order item type: product/service
	OrderItemType string `protobuf:"bytes,8,opt,name=order_item_type,json=orderItemType,proto3" json:"order_item_type,omitempty"`
	// order item id
	OrderItemId int64 `protobuf:"varint,9,opt,name=order_item_id,json=orderItemId,proto3" json:"order_item_id,omitempty"`
}

func (x *EditStaffCommissionItem) Reset() {
	*x = EditStaffCommissionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffCommissionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffCommissionItem) ProtoMessage() {}

func (x *EditStaffCommissionItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffCommissionItem.ProtoReflect.Descriptor instead.
func (*EditStaffCommissionItem) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{8}
}

func (x *EditStaffCommissionItem) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *EditStaffCommissionItem) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *EditStaffCommissionItem) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *EditStaffCommissionItem) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *EditStaffCommissionItem) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *EditStaffCommissionItem) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *EditStaffCommissionItem) GetOperation() []*EditStaffCommissionOperationItem {
	if x != nil {
		return x.Operation
	}
	return nil
}

func (x *EditStaffCommissionItem) GetOrderItemType() string {
	if x != nil {
		return x.OrderItemType
	}
	return ""
}

func (x *EditStaffCommissionItem) GetOrderItemId() int64 {
	if x != nil {
		return x.OrderItemId
	}
	return 0
}

// order event model
type OrderEventModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 事件类型
	EventType EventType `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3,enum=moego.models.order.v1.EventType" json:"event_type,omitempty"`
	// order详情
	Order *OrderModelHistoryView `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *OrderEventModel) Reset() {
	*x = OrderEventModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderEventModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderEventModel) ProtoMessage() {}

func (x *OrderEventModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderEventModel.ProtoReflect.Descriptor instead.
func (*OrderEventModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{9}
}

func (x *OrderEventModel) GetEventType() EventType {
	if x != nil {
		return x.EventType
	}
	return EventType_EVENT_TYPE_UNSPECIFIED
}

func (x *OrderEventModel) GetOrder() *OrderModelHistoryView {
	if x != nil {
		return x.Order
	}
	return nil
}

// Order Payment.
type OrderPaymentModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order Payment ID.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 关联的 Order 的 ID.
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// 关联的 Order 的 Company ID.
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 关联的 Order 的 Business ID.
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 关联的 Order 的 Customer ID.
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 发起支付的 staff ID.
	StaffId int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 支付方式的 ID.
	PaymentMethodId int64 `protobuf:"varint,11,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
	// 支付方式的名字.
	PaymentMethod string `protobuf:"bytes,12,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// 支付方式用于展示的名字.
	PaymentMethodDisplayName string `protobuf:"bytes,13,opt,name=payment_method_display_name,json=paymentMethodDisplayName,proto3" json:"payment_method_display_name,omitempty"`
	// 支付方式的扩展字段，不同的支付方式内容不同.
	PaymentMethodExtra *v11.PaymentMethodExtra `protobuf:"bytes,14,opt,name=payment_method_extra,json=paymentMethodExtra,proto3" json:"payment_method_extra,omitempty"`
	// 支付方式的供应商.
	PaymentMethodVendor string `protobuf:"bytes,15,opt,name=payment_method_vendor,json=paymentMethodVendor,proto3" json:"payment_method_vendor,omitempty"`
	// 是否为 Pay by link 的支付单.
	IsOnline bool `protobuf:"varint,16,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	// 是否为定金.
	IsDeposit bool `protobuf:"varint,17,opt,name=is_deposit,json=isDeposit,proto3" json:"is_deposit,omitempty"`
	// Paid by.
	PaidBy string `protobuf:"bytes,18,opt,name=paid_by,json=paidBy,proto3" json:"paid_by,omitempty"`
	// 支付货币，支付的货币，符合 ISO-4217 规范的三个大写字母.
	// https://www.iso.org/iso-4217-currency-codes.html
	CurrencyCode string `protobuf:"bytes,21,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// 总的支付金额，与重构前 Payment 的 Amount 对应.
	//
	//	total_amount = amount + payment_tips + convenience_fee.
	//
	// 内部的 currency_code 需要与上面的 currency_code 相同.
	TotalAmount *money.Money `protobuf:"bytes,22,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// 应付金额，创建 OrderPayment 时写入，不可更改.
	// 不包含 payment_tips_after_create.
	// 内部的 currency_code 需要与上面的 currency_code 相同.
	Amount *money.Money `protobuf:"bytes,23,opt,name=amount,proto3" json:"amount,omitempty"`
	// 已经退款的金额，内部的 currency_code 需要与上面的 currency_code 相同.
	RefundedAmount *money.Money `protobuf:"bytes,24,opt,name=refunded_amount,json=refundedAmount,proto3" json:"refunded_amount,omitempty"`
	// 本次支付的 processing fee, 内部的 currency_code 需要与上面的 currency_code 相同.
	ProcessingFee *money.Money `protobuf:"bytes,25,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	// 该笔支付单总的 Tips，包含 payment_tips_before_create 和 payment_tips_after_create.
	// 内部的 currency_code 需要与上面的 currency_code 相同.
	//
	// 支付状态变成 Paid 之后，会同步回订单.
	PaymentTips *money.Money `protobuf:"bytes,26,opt,name=payment_tips,json=paymentTips,proto3" json:"payment_tips,omitempty"`
	// 支付发起前，在 MoeGo 侧， Customer 附加的 tips，在发起时可明确知道金额.
	// 内部的 currency_code 需要与上面的 currency_code 相同.
	// 包含在 amount 中，也包含在 total_amount 中.
	//
	// 支付状态变成 Paid 之后，会同步回订单.
	PaymentTipsBeforeCreate *money.Money `protobuf:"bytes,27,opt,name=payment_tips_before_create,json=paymentTipsBeforeCreate,proto3" json:"payment_tips_before_create,omitempty"`
	// 支付发起后，在供应商侧， Customer 附加的 tips，需要在回调时才可知道具体的金额.
	// 内部的 currency_code 需要与上面的 currency_code 相同.
	// 不包含在 amount 中，包含在 total_amount 中.
	// 特别的：
	//
	//	Square 侧该值与支付结果的回调是分开的，当支付结果先回调成功时，订单可能已经满足关单条件从而关单。
	//	然后再收到 Tips 回调，此时会陷入关单后改单的情况.
	//
	// 支付状态变成 Paid 之后，会同步回订单.
	PaymentTipsAfterCreate *money.Money `protobuf:"bytes,28,opt,name=payment_tips_after_create,json=paymentTipsAfterCreate,proto3" json:"payment_tips_after_create,omitempty"`
	// 由于本次支付增加的 Convenience Fee.
	//
	// 支付状态变成 Paid 之后，会同步回订单.
	PaymentConvenienceFee *money.Money `protobuf:"bytes,29,opt,name=payment_convenience_fee,json=paymentConvenienceFee,proto3" json:"payment_convenience_fee,omitempty"`
	// 已退还的 Convenience Fee.
	RefundedPaymentConvenienceFee *money.Money `protobuf:"bytes,30,opt,name=refunded_payment_convenience_fee,json=refundedPaymentConvenienceFee,proto3" json:"refunded_payment_convenience_fee,omitempty"`
	// Payment 状态.
	PaymentStatus OrderPaymentStatus `protobuf:"varint,31,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderPaymentStatus" json:"payment_status,omitempty"`
	// 搭配 Payment status 展示，进入该状态的原因:
	//
	//	Failed -> 失败的原因
	//	Canceled -> 取消的原因
	PaymentStatusReason string `protobuf:"bytes,32,opt,name=payment_status_reason,json=paymentStatusReason,proto3" json:"payment_status_reason,omitempty"`
	// 创建时间，单位秒.
	CreateTime int64 `protobuf:"varint,33,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 支付时间，单位秒.
	PayTime int64 `protobuf:"varint,34,opt,name=pay_time,json=payTime,proto3" json:"pay_time,omitempty"`
	// 支付失败的时间，单位秒.
	FailTime int64 `protobuf:"varint,35,opt,name=fail_time,json=failTime,proto3" json:"fail_time,omitempty"`
	// 支付取消的时间，单位秒.
	CancelTime int64 `protobuf:"varint,36,opt,name=cancel_time,json=cancelTime,proto3" json:"cancel_time,omitempty"`
	// 更新时间，单位秒.
	UpdateTime int64 `protobuf:"varint,37,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// payment id
	PaymentId int64 `protobuf:"varint,38,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// transaction id, 重构后 payment 以 transaction 为主体
	TransactionId int64 `protobuf:"varint,39,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
}

func (x *OrderPaymentModel) Reset() {
	*x = OrderPaymentModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPaymentModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPaymentModel) ProtoMessage() {}

func (x *OrderPaymentModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPaymentModel.ProtoReflect.Descriptor instead.
func (*OrderPaymentModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{10}
}

func (x *OrderPaymentModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderPaymentModel) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *OrderPaymentModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OrderPaymentModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderPaymentModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *OrderPaymentModel) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *OrderPaymentModel) GetPaymentMethodId() int64 {
	if x != nil {
		return x.PaymentMethodId
	}
	return 0
}

func (x *OrderPaymentModel) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *OrderPaymentModel) GetPaymentMethodDisplayName() string {
	if x != nil {
		return x.PaymentMethodDisplayName
	}
	return ""
}

func (x *OrderPaymentModel) GetPaymentMethodExtra() *v11.PaymentMethodExtra {
	if x != nil {
		return x.PaymentMethodExtra
	}
	return nil
}

func (x *OrderPaymentModel) GetPaymentMethodVendor() string {
	if x != nil {
		return x.PaymentMethodVendor
	}
	return ""
}

func (x *OrderPaymentModel) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

func (x *OrderPaymentModel) GetIsDeposit() bool {
	if x != nil {
		return x.IsDeposit
	}
	return false
}

func (x *OrderPaymentModel) GetPaidBy() string {
	if x != nil {
		return x.PaidBy
	}
	return ""
}

func (x *OrderPaymentModel) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *OrderPaymentModel) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *OrderPaymentModel) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *OrderPaymentModel) GetRefundedAmount() *money.Money {
	if x != nil {
		return x.RefundedAmount
	}
	return nil
}

func (x *OrderPaymentModel) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *OrderPaymentModel) GetPaymentTips() *money.Money {
	if x != nil {
		return x.PaymentTips
	}
	return nil
}

func (x *OrderPaymentModel) GetPaymentTipsBeforeCreate() *money.Money {
	if x != nil {
		return x.PaymentTipsBeforeCreate
	}
	return nil
}

func (x *OrderPaymentModel) GetPaymentTipsAfterCreate() *money.Money {
	if x != nil {
		return x.PaymentTipsAfterCreate
	}
	return nil
}

func (x *OrderPaymentModel) GetPaymentConvenienceFee() *money.Money {
	if x != nil {
		return x.PaymentConvenienceFee
	}
	return nil
}

func (x *OrderPaymentModel) GetRefundedPaymentConvenienceFee() *money.Money {
	if x != nil {
		return x.RefundedPaymentConvenienceFee
	}
	return nil
}

func (x *OrderPaymentModel) GetPaymentStatus() OrderPaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return OrderPaymentStatus_ORDER_PAYMENT_STATUS_UNSPECIFIED
}

func (x *OrderPaymentModel) GetPaymentStatusReason() string {
	if x != nil {
		return x.PaymentStatusReason
	}
	return ""
}

func (x *OrderPaymentModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *OrderPaymentModel) GetPayTime() int64 {
	if x != nil {
		return x.PayTime
	}
	return 0
}

func (x *OrderPaymentModel) GetFailTime() int64 {
	if x != nil {
		return x.FailTime
	}
	return 0
}

func (x *OrderPaymentModel) GetCancelTime() int64 {
	if x != nil {
		return x.CancelTime
	}
	return 0
}

func (x *OrderPaymentModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *OrderPaymentModel) GetPaymentId() int64 {
	if x != nil {
		return x.PaymentId
	}
	return 0
}

func (x *OrderPaymentModel) GetTransactionId() int64 {
	if x != nil {
		return x.TransactionId
	}
	return 0
}

// The order model in appointment view
type OrderModelAppointmentView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// order type
	OrderType OrderModel_OrderType `protobuf:"varint,2,opt,name=order_type,json=orderType,proto3,enum=moego.models.order.v1.OrderModel_OrderType" json:"order_type,omitempty"`
	// order status
	Status OrderStatus `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.order.v1.OrderStatus" json:"status,omitempty"`
	// order source type
	SourceType OrderSourceType `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// payment status
	PaymentStatus OrderModel_PaymentStatus `protobuf:"varint,5,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderModel_PaymentStatus" json:"payment_status,omitempty"`
	// source id
	SourceId int64 `protobuf:"varint,8,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// subTotal amount
	SubTotalAmount *money.Money `protobuf:"bytes,15,opt,name=sub_total_amount,json=subTotalAmount,proto3" json:"sub_total_amount,omitempty"`
	// total amount
	TotalAmount *money.Money `protobuf:"bytes,17,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// paid amount
	PaidAmount *money.Money `protobuf:"bytes,18,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount,omitempty"`
	// remain amount to pay
	RemainAmount *money.Money `protobuf:"bytes,19,opt,name=remain_amount,json=remainAmount,proto3" json:"remain_amount,omitempty"`
	// refunded amount
	RefundedAmount *money.Money `protobuf:"bytes,20,opt,name=refunded_amount,json=refundedAmount,proto3" json:"refunded_amount,omitempty"`
	// tips amount
	TipsAmount *money.Money `protobuf:"bytes,21,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
}

func (x *OrderModelAppointmentView) Reset() {
	*x = OrderModelAppointmentView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderModelAppointmentView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderModelAppointmentView) ProtoMessage() {}

func (x *OrderModelAppointmentView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderModelAppointmentView.ProtoReflect.Descriptor instead.
func (*OrderModelAppointmentView) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_models_proto_rawDescGZIP(), []int{11}
}

func (x *OrderModelAppointmentView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderModelAppointmentView) GetOrderType() OrderModel_OrderType {
	if x != nil {
		return x.OrderType
	}
	return OrderModel_ORDER_TYPE_UNSPECIFIED
}

func (x *OrderModelAppointmentView) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_CREATED
}

func (x *OrderModelAppointmentView) GetSourceType() OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return OrderSourceType_ORDER_SOURCE_TYPE_UNSPECIFIED
}

func (x *OrderModelAppointmentView) GetPaymentStatus() OrderModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return OrderModel_PAYMENT_STATUS_UNSPECIFIED
}

func (x *OrderModelAppointmentView) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *OrderModelAppointmentView) GetSubTotalAmount() *money.Money {
	if x != nil {
		return x.SubTotalAmount
	}
	return nil
}

func (x *OrderModelAppointmentView) GetTotalAmount() *money.Money {
	if x != nil {
		return x.TotalAmount
	}
	return nil
}

func (x *OrderModelAppointmentView) GetPaidAmount() *money.Money {
	if x != nil {
		return x.PaidAmount
	}
	return nil
}

func (x *OrderModelAppointmentView) GetRemainAmount() *money.Money {
	if x != nil {
		return x.RemainAmount
	}
	return nil
}

func (x *OrderModelAppointmentView) GetRefundedAmount() *money.Money {
	if x != nil {
		return x.RefundedAmount
	}
	return nil
}

func (x *OrderModelAppointmentView) GetTipsAmount() *money.Money {
	if x != nil {
		return x.TipsAmount
	}
	return nil
}

var File_moego_models_order_v1_order_models_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_order_models_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfb,
	0x04, 0x0a, 0x15, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4a, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x0b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x62, 0x0a,
	0x12, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xbe, 0x14, 0x0a,
	0x0a, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x13, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x0e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x20, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x0d, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x1d, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x24,
	0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x01, 0x48, 0x06, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x48, 0x07, 0x52, 0x09, 0x74, 0x61, 0x78, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x08, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x01, 0x48, 0x09,
	0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0a, 0x52, 0x0e,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x2d, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0b, 0x52, 0x0e, 0x73,
	0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x2f, 0x0a, 0x11, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0c, 0x52, 0x0f, 0x74,
	0x69, 0x70, 0x73, 0x42, 0x61, 0x73, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x26, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0d, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x61, 0x69,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0e,
	0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x28, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0f, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x10, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x48, 0x11, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x03, 0x48, 0x12, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62,
	0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x48, 0x13, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x48, 0x14, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x15, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x32, 0x0a, 0x12, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x16,
	0x52, 0x11, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x17, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x47, 0x0a,
	0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x78, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69, 0x6e,
	0x65, 0x54, 0x61, 0x78, 0x65, 0x73, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c,
	0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x0f,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18,
	0x1f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46,
	0x65, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x20, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x48, 0x18, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x19, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x4f, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x22, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x48, 0x1a, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x03, 0x48, 0x1b, 0x52, 0x0c, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0c,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x24, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x1c, 0x52, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x66, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x13, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x1d, 0x52, 0x11, 0x65, 0x78, 0x74, 0x72, 0x61, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x26, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x1e, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x26, 0x0a, 0x0f, 0x68, 0x61, 0x73, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x27, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x73,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x4c, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x73, 0x18, 0x29, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0f, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x54, 0x0a,
	0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x52, 0x49, 0x47, 0x49, 0x4e,
	0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x58, 0x54, 0x52, 0x41, 0x10, 0x02, 0x12, 0x0b, 0x0a,
	0x07, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x49,
	0x50, 0x10, 0x04, 0x22, 0x57, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x4e, 0x50, 0x41, 0x49, 0x44, 0x10, 0x01,
	0x12, 0x10, 0x0a, 0x0c, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x49, 0x44,
	0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x49, 0x44, 0x10, 0x03, 0x22, 0x5e, 0x0a, 0x11,
	0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x01, 0x12,
	0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x42, 0x05, 0x0a, 0x03,
	0x5f, 0x69, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x67, 0x75, 0x69, 0x64, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x12, 0x0a, 0x10,
	0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65,
	0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x62, 0x79, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x15, 0x0a, 0x13, 0x5f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x66,
	0x5f, 0x69, 0x64, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc5, 0x0f,
	0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a,
	0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a,
	0x0a, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x74, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x3b, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a,
	0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x2a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x46, 0x65, 0x65, 0x12, 0x3c, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x11, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0f, 0x74, 0x69, 0x70, 0x73, 0x42, 0x61, 0x73, 0x65, 0x64, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x70, 0x61,
	0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x37, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x6d, 0x61,
	0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x74, 0x61, 0x78, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x54, 0x61,
	0x78, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x78, 0x65,
	0x73, 0x12, 0x54, 0x0a, 0x0e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x55, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x46, 0x65, 0x65, 0x73, 0x12, 0x43,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x4a, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x22, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x65, 0x78, 0x74, 0x72, 0x61, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x26, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x68, 0x61,
	0x73, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x27, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4c, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x29, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x73, 0x22, 0xfc, 0x05, 0x0a, 0x17, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x4a, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56,
	0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x62, 0x0a, 0x12, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b,
	0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x33, 0x0a, 0x0b, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x69, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x66, 0x49, 0x64, 0x22, 0xcb, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x61,
	0x6e, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x63, 0x61, 0x6e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x1b, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0x96, 0x01, 0x0a, 0x20, 0x45, 0x64, 0x69, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x9a, 0x03, 0x0a, 0x17, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x22, 0x0a, 0x0d, 0x70,
	0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x09, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0x96, 0x01, 0x0a,
	0x0f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x3f, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x42, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x9b, 0x0c, 0x0a, 0x11, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x12, 0x25,
	0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x3d, 0x0a, 0x1b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5d, 0x0a, 0x14, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x69, 0x64, 0x42, 0x79, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x65, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12, 0x35, 0x0a, 0x0c,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x69, 0x70, 0x73, 0x12, 0x4f, 0x0a, 0x1a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x69, 0x70, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x70, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x12, 0x4d, 0x0a, 0x19, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x69, 0x70, 0x73, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x16, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x70, 0x73, 0x41, 0x66, 0x74, 0x65, 0x72, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x12, 0x4a, 0x0a, 0x17, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x1d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x12,
	0x5b, 0x0a, 0x20, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x66, 0x65, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1d, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x12, 0x50, 0x0a, 0x0e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32,
	0x0a, 0x15, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x22, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x66, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x66, 0x61, 0x69, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x27,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x22, 0xc6, 0x05, 0x0a, 0x19, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x4a, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x56, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0b,
	0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x37, 0x0a, 0x0d, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x72, 0x65,
	0x6d, 0x61, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0f, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0a, 0x74, 0x69, 0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x75, 0x0a, 0x1d,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_order_models_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_order_models_proto_rawDescData = file_moego_models_order_v1_order_models_proto_rawDesc
)

func file_moego_models_order_v1_order_models_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_order_models_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_order_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_order_models_proto_rawDescData)
	})
	return file_moego_models_order_v1_order_models_proto_rawDescData
}

var file_moego_models_order_v1_order_models_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_order_v1_order_models_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_models_order_v1_order_models_proto_goTypes = []interface{}{
	(OrderModel_OrderType)(0),                // 0: moego.models.order.v1.OrderModel.OrderType
	(OrderModel_PaymentStatus)(0),            // 1: moego.models.order.v1.OrderModel.PaymentStatus
	(OrderModel_FulfillmentStatus)(0),        // 2: moego.models.order.v1.OrderModel.FulfillmentStatus
	(*OrderModelHistoryView)(nil),            // 3: moego.models.order.v1.OrderModelHistoryView
	(*OrderModel)(nil),                       // 4: moego.models.order.v1.OrderModel
	(*OrderModelV1)(nil),                     // 5: moego.models.order.v1.OrderModelV1
	(*OrderModelV1HistoryView)(nil),          // 6: moego.models.order.v1.OrderModelV1HistoryView
	(*RefundChannelResponse)(nil),            // 7: moego.models.order.v1.RefundChannelResponse
	(*RefundChannel)(nil),                    // 8: moego.models.order.v1.RefundChannel
	(*GroomingDetailRelationModel)(nil),      // 9: moego.models.order.v1.GroomingDetailRelationModel
	(*EditStaffCommissionOperationItem)(nil), // 10: moego.models.order.v1.EditStaffCommissionOperationItem
	(*EditStaffCommissionItem)(nil),          // 11: moego.models.order.v1.EditStaffCommissionItem
	(*OrderEventModel)(nil),                  // 12: moego.models.order.v1.OrderEventModel
	(*OrderPaymentModel)(nil),                // 13: moego.models.order.v1.OrderPaymentModel
	(*OrderModelAppointmentView)(nil),        // 14: moego.models.order.v1.OrderModelAppointmentView
	(OrderSourceType)(0),                     // 15: moego.models.order.v1.OrderSourceType
	(*OrderLineTaxModel)(nil),                // 16: moego.models.order.v1.OrderLineTaxModel
	(*OrderLineDiscountModel)(nil),           // 17: moego.models.order.v1.OrderLineDiscountModel
	(*OrderLineExtraFeeModel)(nil),           // 18: moego.models.order.v1.OrderLineExtraFeeModel
	(v1.AppointmentSource)(0),                // 19: moego.models.grooming.v1.AppointmentSource
	(RefundMode)(0),                          // 20: moego.models.order.v1.RefundMode
	(OrderStatus)(0),                         // 21: moego.models.order.v1.OrderStatus
	(*money.Money)(nil),                      // 22: google.type.Money
	(EventType)(0),                           // 23: moego.models.order.v1.EventType
	(*v11.PaymentMethodExtra)(nil),           // 24: moego.models.payment.v1.PaymentMethodExtra
	(OrderPaymentStatus)(0),                  // 25: moego.models.order.v1.OrderPaymentStatus
}
var file_moego_models_order_v1_order_models_proto_depIdxs = []int32{
	0,  // 0: moego.models.order.v1.OrderModelHistoryView.order_type:type_name -> moego.models.order.v1.OrderModel.OrderType
	15, // 1: moego.models.order.v1.OrderModelHistoryView.source_type:type_name -> moego.models.order.v1.OrderSourceType
	1,  // 2: moego.models.order.v1.OrderModelHistoryView.payment_status:type_name -> moego.models.order.v1.OrderModel.PaymentStatus
	2,  // 3: moego.models.order.v1.OrderModelHistoryView.fulfillment_status:type_name -> moego.models.order.v1.OrderModel.FulfillmentStatus
	16, // 4: moego.models.order.v1.OrderModel.line_taxes:type_name -> moego.models.order.v1.OrderLineTaxModel
	17, // 5: moego.models.order.v1.OrderModel.line_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModel
	18, // 6: moego.models.order.v1.OrderModel.line_extra_fees:type_name -> moego.models.order.v1.OrderLineExtraFeeModel
	19, // 7: moego.models.order.v1.OrderModel.source:type_name -> moego.models.grooming.v1.AppointmentSource
	0,  // 8: moego.models.order.v1.OrderModel.order_type:type_name -> moego.models.order.v1.OrderModel.OrderType
	20, // 9: moego.models.order.v1.OrderModel.refundable_modes:type_name -> moego.models.order.v1.RefundMode
	21, // 10: moego.models.order.v1.OrderModelV1.status:type_name -> moego.models.order.v1.OrderStatus
	1,  // 11: moego.models.order.v1.OrderModelV1.payment_status:type_name -> moego.models.order.v1.OrderModel.PaymentStatus
	22, // 12: moego.models.order.v1.OrderModelV1.tips_amount:type_name -> google.type.Money
	22, // 13: moego.models.order.v1.OrderModelV1.tax_amount:type_name -> google.type.Money
	22, // 14: moego.models.order.v1.OrderModelV1.discount_amount:type_name -> google.type.Money
	22, // 15: moego.models.order.v1.OrderModelV1.deposit_amount:type_name -> google.type.Money
	22, // 16: moego.models.order.v1.OrderModelV1.convenience_fee:type_name -> google.type.Money
	22, // 17: moego.models.order.v1.OrderModelV1.sub_total_amount:type_name -> google.type.Money
	22, // 18: moego.models.order.v1.OrderModelV1.tips_based_amount:type_name -> google.type.Money
	22, // 19: moego.models.order.v1.OrderModelV1.total_amount:type_name -> google.type.Money
	22, // 20: moego.models.order.v1.OrderModelV1.paid_amount:type_name -> google.type.Money
	22, // 21: moego.models.order.v1.OrderModelV1.remain_amount:type_name -> google.type.Money
	22, // 22: moego.models.order.v1.OrderModelV1.refunded_amount:type_name -> google.type.Money
	16, // 23: moego.models.order.v1.OrderModelV1.line_taxes:type_name -> moego.models.order.v1.OrderLineTaxModel
	17, // 24: moego.models.order.v1.OrderModelV1.line_discounts:type_name -> moego.models.order.v1.OrderLineDiscountModel
	18, // 25: moego.models.order.v1.OrderModelV1.line_extra_fees:type_name -> moego.models.order.v1.OrderLineExtraFeeModel
	19, // 26: moego.models.order.v1.OrderModelV1.source:type_name -> moego.models.grooming.v1.AppointmentSource
	0,  // 27: moego.models.order.v1.OrderModelV1.order_type:type_name -> moego.models.order.v1.OrderModel.OrderType
	20, // 28: moego.models.order.v1.OrderModelV1.refundable_modes:type_name -> moego.models.order.v1.RefundMode
	0,  // 29: moego.models.order.v1.OrderModelV1HistoryView.order_type:type_name -> moego.models.order.v1.OrderModel.OrderType
	15, // 30: moego.models.order.v1.OrderModelV1HistoryView.source_type:type_name -> moego.models.order.v1.OrderSourceType
	1,  // 31: moego.models.order.v1.OrderModelV1HistoryView.payment_status:type_name -> moego.models.order.v1.OrderModel.PaymentStatus
	2,  // 32: moego.models.order.v1.OrderModelV1HistoryView.fulfillment_status:type_name -> moego.models.order.v1.OrderModel.FulfillmentStatus
	22, // 33: moego.models.order.v1.OrderModelV1HistoryView.tips_amount:type_name -> google.type.Money
	22, // 34: moego.models.order.v1.OrderModelV1HistoryView.paid_amount:type_name -> google.type.Money
	22, // 35: moego.models.order.v1.OrderModelV1HistoryView.total_amount:type_name -> google.type.Money
	8,  // 36: moego.models.order.v1.RefundChannelResponse.channel_list:type_name -> moego.models.order.v1.RefundChannel
	10, // 37: moego.models.order.v1.EditStaffCommissionItem.operation:type_name -> moego.models.order.v1.EditStaffCommissionOperationItem
	23, // 38: moego.models.order.v1.OrderEventModel.event_type:type_name -> moego.models.order.v1.EventType
	3,  // 39: moego.models.order.v1.OrderEventModel.order:type_name -> moego.models.order.v1.OrderModelHistoryView
	24, // 40: moego.models.order.v1.OrderPaymentModel.payment_method_extra:type_name -> moego.models.payment.v1.PaymentMethodExtra
	22, // 41: moego.models.order.v1.OrderPaymentModel.total_amount:type_name -> google.type.Money
	22, // 42: moego.models.order.v1.OrderPaymentModel.amount:type_name -> google.type.Money
	22, // 43: moego.models.order.v1.OrderPaymentModel.refunded_amount:type_name -> google.type.Money
	22, // 44: moego.models.order.v1.OrderPaymentModel.processing_fee:type_name -> google.type.Money
	22, // 45: moego.models.order.v1.OrderPaymentModel.payment_tips:type_name -> google.type.Money
	22, // 46: moego.models.order.v1.OrderPaymentModel.payment_tips_before_create:type_name -> google.type.Money
	22, // 47: moego.models.order.v1.OrderPaymentModel.payment_tips_after_create:type_name -> google.type.Money
	22, // 48: moego.models.order.v1.OrderPaymentModel.payment_convenience_fee:type_name -> google.type.Money
	22, // 49: moego.models.order.v1.OrderPaymentModel.refunded_payment_convenience_fee:type_name -> google.type.Money
	25, // 50: moego.models.order.v1.OrderPaymentModel.payment_status:type_name -> moego.models.order.v1.OrderPaymentStatus
	0,  // 51: moego.models.order.v1.OrderModelAppointmentView.order_type:type_name -> moego.models.order.v1.OrderModel.OrderType
	21, // 52: moego.models.order.v1.OrderModelAppointmentView.status:type_name -> moego.models.order.v1.OrderStatus
	15, // 53: moego.models.order.v1.OrderModelAppointmentView.source_type:type_name -> moego.models.order.v1.OrderSourceType
	1,  // 54: moego.models.order.v1.OrderModelAppointmentView.payment_status:type_name -> moego.models.order.v1.OrderModel.PaymentStatus
	22, // 55: moego.models.order.v1.OrderModelAppointmentView.sub_total_amount:type_name -> google.type.Money
	22, // 56: moego.models.order.v1.OrderModelAppointmentView.total_amount:type_name -> google.type.Money
	22, // 57: moego.models.order.v1.OrderModelAppointmentView.paid_amount:type_name -> google.type.Money
	22, // 58: moego.models.order.v1.OrderModelAppointmentView.remain_amount:type_name -> google.type.Money
	22, // 59: moego.models.order.v1.OrderModelAppointmentView.refunded_amount:type_name -> google.type.Money
	22, // 60: moego.models.order.v1.OrderModelAppointmentView.tips_amount:type_name -> google.type.Money
	61, // [61:61] is the sub-list for method output_type
	61, // [61:61] is the sub-list for method input_type
	61, // [61:61] is the sub-list for extension type_name
	61, // [61:61] is the sub-list for extension extendee
	0,  // [0:61] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_order_models_proto_init() }
func file_moego_models_order_v1_order_models_proto_init() {
	if File_moego_models_order_v1_order_models_proto != nil {
		return
	}
	file_moego_models_order_v1_order_enums_proto_init()
	file_moego_models_order_v1_order_line_discount_models_proto_init()
	file_moego_models_order_v1_order_line_extra_fee_models_proto_init()
	file_moego_models_order_v1_order_line_tax_models_proto_init()
	file_moego_models_order_v1_refund_order_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_order_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderModelHistoryView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderModelV1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderModelV1HistoryView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundChannelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundChannel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingDetailRelationModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffCommissionOperationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffCommissionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderEventModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPaymentModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderModelAppointmentView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v1_order_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_order_v1_order_models_proto_msgTypes[8].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_order_models_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_order_models_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_order_models_proto_depIdxs,
		EnumInfos:         file_moego_models_order_v1_order_models_proto_enumTypes,
		MessageInfos:      file_moego_models_order_v1_order_models_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_order_models_proto = out.File
	file_moego_models_order_v1_order_models_proto_rawDesc = nil
	file_moego_models_order_v1_order_models_proto_goTypes = nil
	file_moego_models_order_v1_order_models_proto_depIdxs = nil
}
