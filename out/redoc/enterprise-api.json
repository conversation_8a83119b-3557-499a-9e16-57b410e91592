{"openapi": "3.0.3", "info": {"title": "", "version": "0.0.1"}, "paths": {"/moego.enterprise.account.v1.AccountAccessService/Logout": {"post": {"tags": ["moego.enterprise.account.v1.AccountAccessService"], "description": "Logout the current account.\n\n Error codes:\n - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.", "operationId": "Logout", "requestBody": {"content": {"application/json": {}}, "required": true}, "responses": {"200": {"description": "OK", "content": {}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.account.v1.AccountInfoService/GetAccountInfo": {"post": {"tags": ["moego.enterprise.account.v1.AccountInfoService"], "description": "Error codes:\n - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.", "operationId": "GetAccountInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.account.v1.GetAccountInfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.account.v1.GetAccountInfoResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.account.v1.AccountInfoService/UpdatePassword": {"post": {"tags": ["moego.enterprise.account.v1.AccountInfoService"], "description": "Update password of the account related to the current session.\n The request should be in a valid session.\n After success, all sessions of this account will be invalidated, and a new valid session will be created.\n\n Error codes:\n - OLD_PASSWORD_ERROR: old password error.", "operationId": "UpdatePassword", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.account.v1.UpdatePasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.account.v1.UpdatePasswordResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.account.v1.AccountInfoService/UpdateProfile": {"post": {"tags": ["moego.enterprise.account.v1.AccountInfoService"], "description": "Update profile of the account related to the current session.\n The request should be in a valid session.", "operationId": "UpdateProfile", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.account.v1.UpdateProfileRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.account.v1.UpdateProfileResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.automation.v1.WorkflowService/CreateWorkflow": {"post": {"tags": ["moego.enterprise.automation.v1.WorkflowService"], "description": "CreateWorkflow", "operationId": "CreateWorkflow", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.CreateWorkflowParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.CreateWorkflowResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.automation.v1.WorkflowService/FilterCustomer": {"post": {"tags": ["moego.enterprise.automation.v1.WorkflowService"], "description": "opt:FilterCustomer", "operationId": "FilterCustomer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.FilterCustomerParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.FilterCustomerResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.automation.v1.WorkflowService/GetWorkflowConfig": {"post": {"tags": ["moego.enterprise.automation.v1.WorkflowService"], "description": "GetWorkflowConfig", "operationId": "GetWorkflowConfig", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.GetWorkflowConfigParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.GetWorkflowConfigResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.automation.v1.WorkflowService/GetWorkflowInfo": {"post": {"tags": ["moego.enterprise.automation.v1.WorkflowService"], "description": "GetWorkflowInfo", "operationId": "GetWorkflowInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.GetWorkflowInfoParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.GetWorkflowInfoResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.automation.v1.WorkflowService/ListEnterpriseWorkflows": {"post": {"tags": ["moego.enterprise.automation.v1.WorkflowService"], "description": "ListEnterpriseWorkflows", "operationId": "ListEnterpriseWorkflows", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.ListEnterpriseWorkflowsResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.automation.v1.WorkflowService/PushWorkflows": {"post": {"tags": ["moego.enterprise.automation.v1.WorkflowService"], "description": "PushWorkflows", "operationId": "PushWorkflows", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.PushWorkflowsParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.PushWorkflowsResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.automation.v1.WorkflowService/UpdateWorkflowContent": {"post": {"tags": ["moego.enterprise.automation.v1.WorkflowService"], "description": "UpdateWorkflowContent", "operationId": "UpdateWorkflowContent", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.UpdateWorkflowContentParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.UpdateWorkflowContentResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.automation.v1.WorkflowService/UpdateWorkflowInfo": {"post": {"tags": ["moego.enterprise.automation.v1.WorkflowService"], "description": "UpdateWorkflowInfo", "operationId": "UpdateWorkflowInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.UpdateWorkflowInfoParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.automation.v1.UpdateWorkflowInfoResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.campaign.v1.CampaignService/CreateTemplate": {"post": {"tags": ["moego.enterprise.campaign.v1.CampaignService"], "description": "Create a campaign template", "operationId": "CreateTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.CreateTemplateParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.CreateTemplateResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.campaign.v1.CampaignService/GetTemplate": {"post": {"tags": ["moego.enterprise.campaign.v1.CampaignService"], "description": "Get a campaign template", "operationId": "GetTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.GetTemplateParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.GetTemplateResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.campaign.v1.CampaignService/ListTemplatePlaceholders": {"post": {"tags": ["moego.enterprise.campaign.v1.CampaignService"], "description": "List template variables", "operationId": "ListTemplatePlaceholders", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.ListTemplatePlaceholdersParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.ListTemplatePlaceholdersResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.campaign.v1.CampaignService/ListTemplates": {"post": {"tags": ["moego.enterprise.campaign.v1.CampaignService"], "description": "List campaign templates", "operationId": "ListTemplates", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.ListTemplatesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.ListTemplatesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.campaign.v1.CampaignService/PushTemplates": {"post": {"tags": ["moego.enterprise.campaign.v1.CampaignService"], "description": "Push campaign templates to tenants", "operationId": "PushTemplates", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.PushTemplatesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.PushTemplatesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.campaign.v1.CampaignService/SendTestEmail": {"post": {"tags": ["moego.enterprise.campaign.v1.CampaignService"], "description": "Send a test email", "operationId": "SendTestEmail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.SendTestEmailParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.SendTestEmailResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.campaign.v1.CampaignService/UpdateTemplate": {"post": {"tags": ["moego.enterprise.campaign.v1.CampaignService"], "description": "Update a campaign template", "operationId": "UpdateTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.UpdateTemplateParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.campaign.v1.UpdateTemplateResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.configuration.v1.ConfigurationService/ListTemplate": {"post": {"tags": ["moego.enterprise.configuration.v1.ConfigurationService"], "description": "list template", "operationId": "ListTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.configuration.v1.ListTemplateParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.configuration.v1.ListTemplateResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.configuration.v1.ConfigurationService/ListTemplatePublishRecord": {"post": {"tags": ["moego.enterprise.configuration.v1.ConfigurationService"], "description": "list template publish record", "operationId": "ListTemplatePublishRecord", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.configuration.v1.ListTemplatePublishRecordParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.configuration.v1.ListTemplatePublishRecordResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.configuration.v1.ConfigurationService/PublishTemplate": {"post": {"tags": ["moego.enterprise.configuration.v1.ConfigurationService"], "description": "publish template, apply change to franchisees", "operationId": "PublishTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.configuration.v1.PublishTemplateParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.configuration.v1.PublishTemplateResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.enterprise.v1.EnterpriseService/CreateEnterprise": {"post": {"tags": ["moego.enterprise.enterprise.v1.EnterpriseService"], "description": "CreateEnterprise", "operationId": "CreateEnterprise", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.CreateEnterpriseParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.CreateEnterpriseResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.enterprise.v1.EnterpriseService/GetEnterprise": {"post": {"tags": ["moego.enterprise.enterprise.v1.EnterpriseService"], "description": "GetEnterprise", "operationId": "GetEnterprise", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.GetEnterpriseParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.GetEnterpriseResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.enterprise.v1.EnterpriseService/GetEnterprisePreferenceSetting": {"post": {"tags": ["moego.enterprise.enterprise.v1.EnterpriseService"], "description": "GetEnterprisePreferenceSetting", "operationId": "GetEnterprisePreferenceSetting", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.enterprise.v1.EnterpriseService/GetOption": {"post": {"tags": ["moego.enterprise.enterprise.v1.EnterpriseService"], "description": "GetOptions", "operationId": "GetOption", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.GetOptionParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.GetOptionResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.enterprise.v1.EnterpriseService/ListTenantGroup": {"post": {"tags": ["moego.enterprise.enterprise.v1.EnterpriseService"], "description": "list tenant group", "operationId": "ListTenantGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.ListTenantGroupParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.ListTenantGroupResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.enterprise.v1.EnterpriseService/ListTenantTemplates": {"post": {"tags": ["moego.enterprise.enterprise.v1.EnterpriseService"], "description": "list tenant templates", "operationId": "ListTenantTemplates", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.ListTenantTemplateParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.ListTenantTemplateResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.enterprise.v1.EnterpriseService/SyncFranchisee": {"post": {"tags": ["moego.enterprise.enterprise.v1.EnterpriseService"], "description": "SyncFranchisee", "operationId": "SyncFranchisee", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.SyncFranchiseeParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.SyncFranchiseeResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.enterprise.v1.EnterpriseService/UpdateEnterprise": {"post": {"tags": ["moego.enterprise.enterprise.v1.EnterpriseService"], "description": "UpdateEnterprise", "operationId": "UpdateEnterprise", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.UpdateEnterpriseParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.UpdateEnterpriseResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.file.v1.FileService/GetUploadPresignedURL": {"post": {"tags": ["moego.enterprise.file.v1.FileService"], "description": "get presigned url and use put method to upload file with header 'content-md5: md5' and 'content_type: content_type'.\nset header 'x-amz-acl: public-read' to let the file be public", "operationId": "GetUploadPresignedURL", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.file.v1.GetUploadPresignedURLParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.file.v1.GetUploadPresignedURLResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.file.v1.FileService/QueryFile": {"post": {"tags": ["moego.enterprise.file.v1.FileService"], "description": "query file", "operationId": "QueryFile", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.file.v1.QueryFileParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.file.v1.QueryFileResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.map.v1.MapService/ListGooglePlace": {"post": {"tags": ["moego.enterprise.map.v1.MapService"], "description": "get google place list", "operationId": "ListGooglePlace", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.map.v1.ListGooglePlaceParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.map.v1.ListGooglePlaceResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.map.v1.MapService/ListZipCode": {"post": {"tags": ["moego.enterprise.map.v1.MapService"], "description": "list zip code", "operationId": "ListZipCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.map.v1.ListZipCodeParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.map.v1.ListZipCodeResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.map.v1.MapService/SearchZipCode": {"post": {"tags": ["moego.enterprise.map.v1.MapService"], "description": "search zip code", "operationId": "SearchZipCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.map.v1.SearchZipCodeParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.map.v1.SearchZipCodeResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.permission.v1.PermissionService/CreateRole": {"post": {"tags": ["moego.enterprise.permission.v1.PermissionService"], "description": "create role", "operationId": "CreateRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.CreateRoleParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.CreateRoleResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.permission.v1.PermissionService/DeleteRole": {"post": {"tags": ["moego.enterprise.permission.v1.PermissionService"], "description": "delete role", "operationId": "DeleteRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.DeleteRoleParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.DeleteRoleResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.permission.v1.PermissionService/DuplicateRole": {"post": {"tags": ["moego.enterprise.permission.v1.PermissionService"], "description": "duplicate role", "operationId": "DuplicateRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.DuplicateRoleParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.DuplicateRoleResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.permission.v1.PermissionService/EditPermissions": {"post": {"tags": ["moego.enterprise.permission.v1.PermissionService"], "description": "edit permissions", "operationId": "EditPermissions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.EditPermissionsParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.EditPermissionsResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.permission.v1.PermissionService/GetRole": {"post": {"tags": ["moego.enterprise.permission.v1.PermissionService"], "description": "get role detail", "operationId": "GetRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.GetRoleParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.GetRoleResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.permission.v1.PermissionService/ListRoles": {"post": {"tags": ["moego.enterprise.permission.v1.PermissionService"], "description": "get role list", "operationId": "ListRoles", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.ListRolesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.ListRolesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.permission.v1.PermissionService/UpdateRole": {"post": {"tags": ["moego.enterprise.permission.v1.PermissionService"], "description": "update role", "operationId": "UpdateRole", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.UpdateRoleParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.UpdateRoleResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/CreateService": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "create a service", "operationId": "CreateService", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.CreateServiceParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.CreateServiceResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/DeleteService": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "delete a service", "operationId": "DeleteService", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.DeleteServiceParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.DeleteServiceResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/GetService": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "get a service", "operationId": "GetService", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.GetServiceParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.GetServiceResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/ListPetBreeds": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "list pet breeds", "operationId": "ListPetBreeds", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListPetBreedsParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListPetBreedsResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/ListPetTypes": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "list pet types", "operationId": "ListPetTypes", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListPetTypesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListPetTypesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/ListPriceBooks": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "list price books", "operationId": "ListPriceBooks", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListPriceBooksParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListPriceBooksResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/ListServiceCategories": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "list service categories", "operationId": "ListServiceCategories", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListServiceCategoriesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListServiceCategoriesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/ListServiceChangeHistories": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "list service change histories", "operationId": "ListServiceChangeHistories", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListServiceChangeHistoriesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListServiceChangeHistoriesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/ListServiceChanges": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "list service changes", "operationId": "ListServiceChanges", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListServiceChangesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListServiceChangesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/ListServices": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "list services", "operationId": "ListServices", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListServicesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.ListServicesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/PushServiceChanges": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "push service changes", "operationId": "PushServiceChanges", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.PushServiceChangesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.PushServiceChangesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/SaveServiceCategories": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "create and update existed categories", "operationId": "SaveServiceCategories", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.SaveServiceCategoriesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.SaveServiceCategoriesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/SortServices": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "sort services", "operationId": "SortServices", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.SortServicesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.SortServicesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.price_book.v1.PriceBookService/UpdateService": {"post": {"tags": ["moego.enterprise.price_book.v1.PriceBookService"], "description": "update a service", "operationId": "UpdateService", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.UpdateServiceParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.price_book.v1.UpdateServiceResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseAttributeService/GetDimensions": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseAttributeService"], "description": "Get dimensions", "operationId": "GetDimensions", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.GetDimensionsParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.GetDimensionsResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseAttributeService/GetMetricsCategories": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseAttributeService"], "description": "Get metrics categories", "operationId": "GetMetricsCategories", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.GetMetricsCategoriesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.GetMetricsCategoriesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseCustomReportService/DeleteCustomReport": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseCustomReportService"], "description": "DeleteCustomReport", "operationId": "DeleteCustomReport", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.DeleteCustomReportParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.DeleteCustomReportResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseCustomReportService/DuplicateCustomReport": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseCustomReportService"], "description": "DuplicateCustomReport", "operationId": "DuplicateCustomReport", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.DuplicateCustomReportParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.DuplicateCustomReportResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseCustomReportService/ModifyCustomDiagram": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseCustomReportService"], "description": "ModifyCustomDiagram", "operationId": "ModifyCustomDiagram", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.ModifyCustomDiagramParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.ModifyCustomDiagramResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseCustomReportService/SaveCustomReport": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseCustomReportService"], "description": "SaveCustomReport", "operationId": "SaveCustomReport", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.SaveCustomReportParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.SaveCustomReportResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseDashboardService/FetchDashboardData": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseDashboardService"], "description": "FetchDashboardData fetches dashboard diagram data", "operationId": "FetchDashboardData", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.FetchDashboardDataRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.FetchDashboardDataResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseDashboardService/QueryDashboardPages": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseDashboardService"], "description": "QueryDashboardPages returns meta data of dashboard", "operationId": "QueryDashboardPages", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.QueryDashboardPagesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.QueryDashboardPagesResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/ExportData": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Common export data api", "operationId": "ExportData", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.ExportDataParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.ExportDataResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/ExportReportData": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Export report data", "operationId": "ExportReportData", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.ExportReportDataRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.ExportReportDataResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/FetchData": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Common fetch data api", "operationId": "FetchData", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.FetchDataParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.FetchDataResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/FetchReportData": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Fetch report data", "operationId": "FetchReportData", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.FetchReportDataRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.FetchReportDataResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/MarkReportFavorite": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Mark report favorite marks/removes a report as favorite, return the reports with new sequence", "operationId": "MarkReportFavorite", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.MarkReportFavoriteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.MarkReportFavoriteResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/QueryMetas": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Common meta api", "operationId": "QueryMetas", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.QueryMetasParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.QueryMetasResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/QueryPages": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Common page api", "operationId": "QueryPages", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.QueryPageMetaParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.models.reporting.v2.QueryPageMetaResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/QueryReportMetas": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Query metadata of reports", "operationId": "QueryReportMetas", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.QueryReportMetasRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.QueryReportsMetasResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/QueryReportPages": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Query report pages returns a list of report pages", "operationId": "QueryReportPages", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.QueryReportPagesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.QueryReportPagesResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.report.v1.EnterpriseReportService/SaveReportCustomizeConfig": {"post": {"tags": ["moego.enterprise.report.v1.EnterpriseReportService"], "description": "Save report customized config", "operationId": "SaveReportCustomizeConfig", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.report.v1.SaveReportCustomizeConfigRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.session.v1.SessionService/GetSessionData": {"post": {"tags": ["moego.enterprise.session.v1.SessionService"], "description": "GetSessionData", "operationId": "GetSessionData", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.session.v1.GetSessionDataParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.session.v1.GetSessionDataResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.staff.v1.StaffService/CreateStaff": {"post": {"tags": ["moego.enterprise.staff.v1.StaffService"], "description": "create staff", "operationId": "CreateStaff", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.CreateStaffParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.CreateStaffResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.staff.v1.StaffService/DeleteStaff": {"post": {"tags": ["moego.enterprise.staff.v1.StaffService"], "description": "delete staff", "operationId": "DeleteStaff", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.DeleteStaffParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.DeleteStaffResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.staff.v1.StaffService/GetStaff": {"post": {"tags": ["moego.enterprise.staff.v1.StaffService"], "description": "get staff", "operationId": "GetStaff", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.GetStaffParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.GetStaffResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.staff.v1.StaffService/ListStaffs": {"post": {"tags": ["moego.enterprise.staff.v1.StaffService"], "description": "list staff", "operationId": "ListStaffs", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.ListStaffsParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.ListStaffsResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.staff.v1.StaffService/SendStaffInviteLink": {"post": {"tags": ["moego.enterprise.staff.v1.StaffService"], "description": "send invite link", "operationId": "SendStaffInviteLink", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.SendStaffInviteLinkParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.SendStaffInviteLinkResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.staff.v1.StaffService/UnlinkStaff": {"post": {"tags": ["moego.enterprise.staff.v1.StaffService"], "description": "unlink staff", "operationId": "UnlinkStaff", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.UnlinkStaffParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.UnlinkStaffResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.staff.v1.StaffService/UpdateStaff": {"post": {"tags": ["moego.enterprise.staff.v1.StaffService"], "description": "update staff", "operationId": "UpdateStaff", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.UpdateStaffParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.staff.v1.UpdateStaffResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.subscription.v1.SubscriptionService/GetEnterpriseUnit": {"post": {"tags": ["moego.enterprise.subscription.v1.SubscriptionService"], "description": "GetEnterpriseUnit", "operationId": "GetEnterpriseUnit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.subscription.v1.GetEnterpriseUnitParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.subscription.v1.GetEnterpriseUnitResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.subscription.v1.SubscriptionService/ListCards": {"post": {"tags": ["moego.enterprise.subscription.v1.SubscriptionService"], "description": "ListCards", "operationId": "ListCards", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.subscription.v1.ListCardsParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.subscription.v1.ListCardsResponse"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TenantService/CreateTenant": {"post": {"tags": ["moego.enterprise.tenant.v1.TenantService"], "description": "create tenant", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.CreateTenantParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.CreateTenantResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TenantService/DeleteTenant": {"post": {"tags": ["moego.enterprise.tenant.v1.TenantService"], "description": "delete tenant", "operationId": "DeleteTenant", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.DeleteTenantParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.DeleteTenantResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TenantService/DeleteTenantGroup": {"post": {"tags": ["moego.enterprise.tenant.v1.TenantService"], "description": "delete tenant group", "operationId": "DeleteTenantGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.DeleteTenantGroupParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.DeleteTenantGroupResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TenantService/GetTenant": {"post": {"tags": ["moego.enterprise.tenant.v1.TenantService"], "description": "get tenant", "operationId": "Get<PERSON>enant", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.GetTenantParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.GetTenantResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TenantService/ListAllTenantAndGroup": {"post": {"tags": ["moego.enterprise.tenant.v1.TenantService"], "description": "list all tenant and group", "operationId": "ListAllTenantAndGroup", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListAllTenantAndGroupParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListAllTenantAndGroupResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TenantService/ListTenant": {"post": {"tags": ["moego.enterprise.tenant.v1.TenantService"], "description": "get tenant list", "operationId": "ListTenant", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTenantParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTenantResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TenantService/ListTenantGroups": {"post": {"tags": ["moego.enterprise.tenant.v1.TenantService"], "description": "list tenant groups", "operationId": "ListTenantGroups", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTenantGroupParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTenantGroupResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TenantService/UpdateTenant": {"post": {"tags": ["moego.enterprise.tenant.v1.TenantService"], "description": "update tenant", "operationId": "UpdateTenant", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.UpdateTenantParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.UpdateTenantResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TerritoryService/CreateTerritory": {"post": {"tags": ["moego.enterprise.tenant.v1.TerritoryService"], "description": "create territory", "operationId": "CreateTerritory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.CreateTerritoryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.CreateTerritoryResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TerritoryService/DeleteTerritory": {"post": {"tags": ["moego.enterprise.tenant.v1.TerritoryService"], "description": "delete territory", "operationId": "DeleteTerritory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.DeleteTerritoryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.DeleteTerritoryResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TerritoryService/GetTerritory": {"post": {"tags": ["moego.enterprise.tenant.v1.TerritoryService"], "description": "get territory", "operationId": "GetTerritory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.GetTerritoryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.GetTerritoryResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TerritoryService/ListTerritories": {"post": {"tags": ["moego.enterprise.tenant.v1.TerritoryService"], "description": "list territories", "operationId": "ListTerritories", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTerritoriesParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTerritoriesResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}, "/moego.enterprise.tenant.v1.TerritoryService/UpdateTerritory": {"post": {"tags": ["moego.enterprise.tenant.v1.TerritoryService"], "description": "update territory", "operationId": "UpdateTerritory", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.UpdateTerritoryParams"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/moego.enterprise.tenant.v1.UpdateTerritoryResult"}}}}, "default": {"description": "Default error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/google.rpc.Status"}}}}}}}}, "components": {"schemas": {"google.geo.type.Viewport": {"type": "object", "properties": {"low": {"allOf": [{"$ref": "#/components/schemas/google.type.LatLng"}], "description": "Required. The low point of the viewport."}, "high": {"allOf": [{"$ref": "#/components/schemas/google.type.LatLng"}], "description": "Required. The high point of the viewport."}}, "description": "A latitude-longitude viewport, represented as two diagonally opposite `low` and `high` points. A viewport is considered a closed region, i.e. it includes its boundary. The latitude bounds must range between -90 to 90 degrees inclusive, and the longitude bounds must range between -180 to 180 degrees inclusive. Various cases include:  - If `low` = `high`, the viewport consists of that single point.  - If `low.longitude` > `high.longitude`, the longitude range is inverted    (the viewport crosses the 180 degree longitude line).  - If `low.longitude` = -180 degrees and `high.longitude` = 180 degrees,    the viewport includes all longitudes.  - If `low.longitude` = 180 degrees and `high.longitude` = -180 degrees,    the longitude range is empty.  - If `low.latitude` > `high.latitude`, the latitude range is empty. Both `low` and `high` must be populated, and the represented box cannot be empty (as specified by the definitions above). An empty viewport will result in an error. For example, this viewport fully encloses New York City: {     \"low\": {         \"latitude\": 40.477398,         \"longitude\": -74.259087     },     \"high\": {         \"latitude\": 40.91618,         \"longitude\": -73.70018     } }"}, "google.protobuf.Any": {"type": "object", "properties": {"@type": {"type": "string", "description": "The type of the serialized message."}}, "additionalProperties": true, "description": "Contains an arbitrary serialized message along with a @type that describes the type of the serialized message."}, "google.protobuf.Duration": {"type": "object", "properties": {"seconds": {"type": "string", "description": "Signed seconds of the span of time. Must be from -315,576,000,000 to +315,576,000,000 inclusive. Note: these bounds are computed from: 60 sec/min * 60 min/hr * 24 hr/day * 365.25 days/year * 10000 years"}, "nanos": {"type": "integer", "description": "Signed fractions of a second at nanosecond resolution of the span of time. Durations less than one second are represented with a 0 `seconds` field and a positive or negative `nanos` field. For durations of one second or more, a non-zero value for the `nanos` field must be of the same sign as the `seconds` field. Must be from -999,999,999 to +999,999,999 inclusive.", "format": "int32"}}, "description": "A Duration represents a signed, fixed-length span of time represented as a count of seconds and fractions of seconds at nanosecond resolution. It is independent of any calendar and concepts like \"day\" or \"month\". It is related to Timestamp in that the difference between two Timestamp values is a Duration and it can be added or subtracted from a Timestamp. Range is approximately +-10,000 years. # Examples Example 1: Compute Duration from two Timestamps in pseudo code.     Timestamp start = ...;     Timestamp end = ...;     Duration duration = ...;     duration.seconds = end.seconds - start.seconds;     duration.nanos = end.nanos - start.nanos;     if (duration.seconds < 0 && duration.nanos > 0) {       duration.seconds += 1;       duration.nanos -= 1000000000;     } else if (duration.seconds > 0 && duration.nanos < 0) {       duration.seconds -= 1;       duration.nanos += 1000000000;     } Example 2: Compute Timestamp from Timestamp + Duration in pseudo code.     Timestamp start = ...;     Duration duration = ...;     Timestamp end = ...;     end.seconds = start.seconds + duration.seconds;     end.nanos = start.nanos + duration.nanos;     if (end.nanos < 0) {       end.seconds -= 1;       end.nanos += 1000000000;     } else if (end.nanos >= 1000000000) {       end.seconds += 1;       end.nanos -= 1000000000;     } Example 3: Compute Duration from datetime.timedelta in Python.     td = datetime.timedelta(days=3, minutes=10)     duration = Duration()     duration.FromTimedelta(td) # JSON Mapping In JSON format, the Duration type is encoded as a string rather than an object, where the string ends in the suffix \"s\" (indicating seconds) and is preceded by the number of seconds, with nanoseconds expressed as fractional seconds. For example, 3 seconds with 0 nanoseconds should be encoded in JSON format as \"3s\", while 3 seconds and 1 nanosecond should be expressed in JSON format as \"3.000000001s\", and 3 seconds and 1 microsecond should be expressed in JSON format as \"3.000001s\"."}, "google.rpc.Status": {"type": "object", "properties": {"code": {"type": "integer", "description": "The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].", "format": "int32"}, "message": {"type": "string", "description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client."}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/google.protobuf.Any"}, "description": "A list of messages that carry the error details.  There is a common set of message types for APIs to use."}}, "description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors)."}, "google.type.Interval": {"type": "object", "properties": {"startTime": {"type": "string", "description": "Optional. Inclusive start of the interval. If specified, a Timestamp matching this interval will have to be the same or after the start.", "format": "date-time"}, "endTime": {"type": "string", "description": "Optional. Exclusive end of the interval. If specified, a Timestamp matching this interval will have to be before the end.", "format": "date-time"}}, "description": "Represents a time interval, encoded as a Timestamp start (inclusive) and a Timestamp end (exclusive). The start must be less than or equal to the end. When the start equals the end, the interval is empty (matches no time). When both start and end are unspecified, the interval matches any time."}, "google.type.LatLng": {"type": "object", "properties": {"latitude": {"type": "number", "description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double"}, "longitude": {"type": "number", "description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "format": "double"}}, "description": "An object that represents a latitude/longitude pair. This is expressed as a pair of doubles to represent degrees latitude and degrees longitude. Unless specified otherwise, this must conform to the <a href=\"http://www.unoosa.org/pdf/icg/2012/template/WGS_84.pdf\">WGS84 standard</a>. Values must be within normalized ranges."}, "google.type.Money": {"type": "object", "properties": {"currencyCode": {"type": "string", "description": "The three-letter currency code defined in ISO 4217."}, "units": {"type": "string", "description": "The whole units of the amount. For example if `currencyCode` is `\"USD\"`, then 1 unit is one US dollar."}, "nanos": {"type": "integer", "description": "Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.", "format": "int32"}}, "description": "Represents an amount of money with its currency type."}, "google.type.TimeOfDay": {"type": "object", "properties": {"hours": {"type": "integer", "description": "Hours of day in 24 hour format. Should be from 0 to 23. An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32"}, "minutes": {"type": "integer", "description": "Minutes of hour of day. Must be from 0 to 59.", "format": "int32"}, "seconds": {"type": "integer", "description": "Seconds of minutes of the time. Must normally be from 0 to 59. An API may allow the value 60 if it allows leap-seconds.", "format": "int32"}, "nanos": {"type": "integer", "description": "Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.", "format": "int32"}}, "description": "Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are [google.type.Date][google.type.Date] and `google.protobuf.Timestamp`."}, "moego.enterprise.account.v1.GetAccountInfoRequest": {"type": "object", "properties": {}, "description": "get account info request"}, "moego.enterprise.account.v1.GetAccountInfoResponse": {"type": "object", "properties": {"account": {"allOf": [{"$ref": "#/components/schemas/moego.models.account.v1.AccountModel"}], "description": "account"}, "accountSecurity": {"allOf": [{"$ref": "#/components/schemas/moego.models.account.v1.AccountSecurityModel"}], "description": "account security"}}, "description": "get account info response"}, "moego.enterprise.account.v1.UpdatePasswordRequest": {"type": "object", "properties": {"oldPassword": {"type": "string", "description": "old password"}, "newPassword": {"type": "string", "description": "new password"}}, "description": "update password request"}, "moego.enterprise.account.v1.UpdatePasswordResponse": {"type": "object", "properties": {}, "description": "update password response"}, "moego.enterprise.account.v1.UpdateProfileRequest": {"type": "object", "properties": {"firstName": {"type": "string", "description": "first name"}, "lastName": {"type": "string", "description": "last name"}, "avatarPath": {"type": "string", "description": "avatar path"}}, "description": "update profile request"}, "moego.enterprise.account.v1.UpdateProfileResponse": {"type": "object", "properties": {"account": {"allOf": [{"$ref": "#/components/schemas/moego.models.account.v1.AccountModel"}], "description": "account"}}, "description": "update profile response"}, "moego.enterprise.automation.v1.CreateWorkflowParams": {"type": "object", "properties": {"workflow": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.CreateWorkflowDef"}], "description": "Workflow to be created"}}, "description": "CreateWorkflowParams"}, "moego.enterprise.automation.v1.CreateWorkflowResult": {"type": "object", "properties": {"workflow": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Workflow"}], "description": "Workflow"}}, "description": "CreateWorkflowResult"}, "moego.enterprise.automation.v1.FilterCustomerParams": {"type": "object", "properties": {"filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "Filter requests"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "Pagination"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "Tenants IDs, empty is all tenants"}, "customerId": {"type": "string", "description": "Customer ID filter"}}, "description": "FilterCustomerParams"}, "moego.enterprise.automation.v1.FilterCustomerResult": {"type": "object", "properties": {"customer": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.business_customer.v1.BusinessCustomerInfoModel"}, "description": "Customers"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "Pagination"}}, "description": "FilterCustomerResult"}, "moego.enterprise.automation.v1.GetWorkflowConfigParams": {"type": "object", "properties": {}, "description": "GetWorkflowConfigParams"}, "moego.enterprise.automation.v1.GetWorkflowConfigResult": {"type": "object", "properties": {"workflowConfigs": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.automation.v1.WorkflowConfig"}, "description": "WorkflowConfig List"}, "filterGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterGroup"}, "description": "Common Filters"}}, "description": "GetWorkflowConfigResult"}, "moego.enterprise.automation.v1.GetWorkflowInfoParams": {"type": "object", "properties": {"workflowId": {"type": "string", "description": "Workflow ID"}}, "description": "GetWorkflowInfoParams"}, "moego.enterprise.automation.v1.GetWorkflowInfoResult": {"type": "object", "properties": {"workflow": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Workflow"}], "description": "Workflow"}}, "description": "GetWorkflowInfoResult"}, "moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "Pagination"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams_Filter"}], "description": "Filter"}}, "description": "ListEnterpriseWorkflowsParams"}, "moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams_Filter": {"type": "object", "properties": {"name": {"type": "string", "description": "Workflow name filter"}, "status": {"type": "array", "items": {"enum": ["STATUS_UNSPECIFIED", "DRAFT", "ACTIVE", "INACTIVE", "DELETED"], "type": "string", "format": "enum"}, "description": "Workflow status filter"}, "tenantsGroupIds": {"type": "array", "items": {"type": "string"}, "description": "Tenant Group IDs filter"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "Tenant IDs filter"}}, "description": "Filter"}, "moego.enterprise.automation.v1.ListEnterpriseWorkflowsResult": {"type": "object", "properties": {"workflows": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.automation.v1.Workflow"}, "description": "Workflows"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "Pagination"}}, "description": "ListEnterpriseWorkflowsResult"}, "moego.enterprise.automation.v1.PushWorkflowsParams": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}, "description": "workflow ids"}, "targets": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantObject"}, "description": "tenant objects"}}, "description": "PushWorkflowsParams"}, "moego.enterprise.automation.v1.PushWorkflowsResult": {"type": "object", "properties": {"success": {"type": "boolean", "description": "success"}}, "description": "PushWorkflowsResult"}, "moego.enterprise.automation.v1.UpdateWorkflowContentParams": {"type": "object", "properties": {"workflowId": {"type": "string", "description": "Workflow ID"}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.automation.v1.CreateStepDef"}, "description": "Steps to update"}}, "description": "UpdateWorkflowContentParams"}, "moego.enterprise.automation.v1.UpdateWorkflowContentResult": {"type": "object", "properties": {"workflow": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Workflow"}], "description": "Workflow"}}, "description": "UpdateWorkflowContentResult"}, "moego.enterprise.automation.v1.UpdateWorkflowInfoParams": {"type": "object", "properties": {"workflowId": {"type": "string", "description": "Workflow ID"}, "name": {"type": "string", "description": "Workflow name"}, "desc": {"type": "string", "description": "Workflow description"}, "status": {"enum": ["STATUS_UNSPECIFIED", "DRAFT", "ACTIVE", "INACTIVE", "DELETED"], "type": "string", "description": "Workflow status", "format": "enum"}, "setting": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.WorkflowSetting"}], "description": "Workflow setting"}, "workflowEnterpriseApply": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.WorkflowEnterpriseApply"}], "description": "enterprise apply to"}}, "description": "UpdateWorkflowInfoParams"}, "moego.enterprise.automation.v1.UpdateWorkflowInfoResult": {"type": "object", "properties": {"workflow": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Workflow"}], "description": "Workflow"}}, "description": "UpdateWorkflowInfoResult"}, "moego.enterprise.campaign.v1.CreateTemplateParams": {"type": "object", "properties": {"name": {"type": "string", "description": "campaign template"}, "description": {"type": "string", "description": "description"}, "type": {"enum": ["TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "description": "type", "format": "enum"}, "cover": {"type": "string", "description": "cover"}, "subject": {"type": "string", "description": "subject"}, "content": {"type": "string", "description": "content"}}, "description": "CreateTemplateParams"}, "moego.enterprise.campaign.v1.CreateTemplateResult": {"type": "object", "properties": {"template": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CampaignTemplate"}], "description": "campaign template"}}, "description": "CreateTemplateResult"}, "moego.enterprise.campaign.v1.GetTemplateParams": {"type": "object", "properties": {"id": {"type": "string", "description": "campaign template id"}}, "description": "GetTemplateParams"}, "moego.enterprise.campaign.v1.GetTemplateResult": {"type": "object", "properties": {"template": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CampaignTemplate"}], "description": "campaign template"}}, "description": "GetTemplateResult"}, "moego.enterprise.campaign.v1.ListTemplatePlaceholdersParams": {"type": "object", "properties": {"type": {"enum": ["TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "description": "campaign type", "format": "enum"}}, "description": "ListTemplatePlaceholdersParams"}, "moego.enterprise.campaign.v1.ListTemplatePlaceholdersResult": {"type": "object", "properties": {"placeholders": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.message.v1.MessageTemplatePlaceholderSimpleView"}, "description": "template placeholders"}}, "description": "ListTemplatePlaceholdersResult"}, "moego.enterprise.campaign.v1.ListTemplatesParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "page"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.service.enterprise.v1.ListTemplatesRequest_Filter"}], "description": "filter"}}, "description": "ListTemplatesParams"}, "moego.enterprise.campaign.v1.ListTemplatesResult": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "page"}, "templates": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.CampaignTemplate"}, "description": "campaign templates"}}, "description": "ListTemplatesResult"}, "moego.enterprise.campaign.v1.PushTemplatesParams": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}, "description": "campaign template ids"}, "targets": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantObject"}, "description": "tenant ids"}}, "description": "PushTemplatesParams"}, "moego.enterprise.campaign.v1.PushTemplatesResult": {"type": "object", "properties": {"success": {"type": "boolean", "description": "success"}}, "description": "PushTemplatesResult"}, "moego.enterprise.campaign.v1.SendTestEmailParams": {"type": "object", "properties": {"subject": {"type": "string", "description": "email subject"}, "content": {"type": "string", "description": "email content"}, "recipientEmail": {"type": "string", "description": "recipient email"}}, "description": "SendTestEmailParams"}, "moego.enterprise.campaign.v1.SendTestEmailResult": {"type": "object", "properties": {}, "description": "SendTestEmailResult"}, "moego.enterprise.campaign.v1.UpdateTemplateParams": {"type": "object", "properties": {"id": {"type": "string", "description": "campaign template id"}, "name": {"type": "string", "description": "campaign template"}, "description": {"type": "string", "description": "description"}, "cover": {"type": "string", "description": "cover"}, "subject": {"type": "string", "description": "subject"}, "content": {"type": "string", "description": "content"}}, "description": "UpdateTemplateParams"}, "moego.enterprise.campaign.v1.UpdateTemplateResult": {"type": "object", "properties": {"template": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CampaignTemplate"}], "description": "campaign template"}}, "description": "UpdateTemplateResult"}, "moego.enterprise.configuration.v1.ListTemplateParams": {"type": "object", "properties": {"filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.configuration.v1.ListTemplateParams_Filter"}], "description": "filter"}}, "description": "list template request"}, "moego.enterprise.configuration.v1.ListTemplateParams_Filter": {"type": "object", "properties": {"tabs": {"type": "array", "items": {"enum": ["TAB_UNSPECIFIED", "AGREEMENT", "INTAKE_FORM", "CLIENT_AND_PET"], "type": "string", "format": "enum"}, "description": "tab"}, "nameLike": {"type": "string", "description": "name_like, fuzzy search"}}, "description": "filter"}, "moego.enterprise.configuration.v1.ListTemplatePublishRecordParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "pagination"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.configuration.v1.ListTemplatePublishRecordParams_Filter"}], "description": "filter"}}, "description": "list template publish record request, order by published time desc"}, "moego.enterprise.configuration.v1.ListTemplatePublishRecordParams_Filter": {"type": "object", "properties": {"templateId": {"type": "string", "description": "template id"}}, "description": "filter"}, "moego.enterprise.configuration.v1.ListTemplatePublishRecordResult": {"type": "object", "properties": {"templatePublishRecords": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.ConfigurationTemplatePublishRecord"}, "description": "template publish record"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "pagination"}}, "description": "list template publish record response"}, "moego.enterprise.configuration.v1.ListTemplateResult": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/components/schemas/moego.enterprise.configuration.v1.ListTemplateResult_Result"}, "description": "result"}}, "description": "list template response"}, "moego.enterprise.configuration.v1.ListTemplateResult_Result": {"type": "object", "properties": {"tab": {"enum": ["TAB_UNSPECIFIED", "AGREEMENT", "INTAKE_FORM", "CLIENT_AND_PET"], "type": "string", "description": "tab", "format": "enum"}, "templates": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.ConfigurationTemplate"}, "description": "template"}}, "description": "result"}, "moego.enterprise.configuration.v1.PublishTemplateParams": {"type": "object", "properties": {"sourceType": {"enum": ["TEMPLATE", "TEMPLATE_RECORD"], "type": "string", "description": "source type", "format": "enum"}, "sourceIds": {"type": "array", "items": {"type": "string"}, "description": "source_ids"}}, "description": "publish template request"}, "moego.enterprise.configuration.v1.PublishTemplateResult": {"type": "object", "properties": {}, "description": "publish template response"}, "moego.enterprise.enterprise.v1.CreateEnterpriseParams": {"type": "object", "properties": {"enterprise": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CreateEnterpriseDef"}], "description": "enterprise"}, "tenantTemplate": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CreateTenantTemplateDef"}], "description": "tenant template"}}, "description": "CreateEnterpriseParams"}, "moego.enterprise.enterprise.v1.CreateEnterpriseResult": {"type": "object", "properties": {"enterprise": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.EnterpriseModel"}], "description": "enterprise"}}, "description": "CreateEnterpriseResult"}, "moego.enterprise.enterprise.v1.GetEnterpriseParams": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "needTenantTemplate": {"type": "boolean", "description": "need tenant template"}}, "description": "GetEnterpriseParams"}, "moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingParams": {"type": "object", "properties": {}, "description": "GetEnterprisePreferenceSettingParams"}, "moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult": {"type": "object", "properties": {"tenantTextMappings": {"type": "array", "items": {"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult_TenantTextMapping"}, "description": "tenant text mappings"}}, "description": "GetEnterprisePreferenceSettingResult"}, "moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult_TenantTextMapping": {"type": "object", "properties": {"type": {"enum": ["TENANT_TEXT_TYPE_UNSPECIFIED", "TENANT_LOWERCASE", "TENANT_LOWERCASE_PLURAL", "TENANT_TITLE", "TENANT_TITLE_PLURAL"], "type": "string", "description": "type", "format": "enum"}, "value": {"type": "string", "description": "value"}}, "description": "tenant text mapping"}, "moego.enterprise.enterprise.v1.GetEnterpriseResult": {"type": "object", "properties": {"enterprise": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.EnterpriseModel"}], "description": "enterprise"}, "tenantTemplate": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantTemplateModel"}, "description": "tenant template"}}, "description": "GetEnterpriseResult"}, "moego.enterprise.enterprise.v1.GetOptionParams": {"type": "object", "properties": {}, "description": "get option params"}, "moego.enterprise.enterprise.v1.GetOptionResult": {"type": "object", "properties": {"option": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.OptionModel"}], "description": "option"}}, "description": "get option result"}, "moego.enterprise.enterprise.v1.ListTenantGroupParams": {"type": "object", "properties": {"enterpriseId": {"type": "string", "description": "enterprise id"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.ListTenantGroupParams_Filter"}], "description": "filter"}}, "description": "list tenant group params"}, "moego.enterprise.enterprise.v1.ListTenantGroupParams_Filter": {"type": "object", "properties": {"tenantId": {"type": "array", "items": {"type": "string"}, "description": "tenant id"}}, "description": "filter"}, "moego.enterprise.enterprise.v1.ListTenantGroupResult": {"type": "object", "properties": {"tenantGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantGroupModel"}, "description": "tenant groups"}}, "description": "list tenant group result"}, "moego.enterprise.enterprise.v1.ListTenantTemplateParams": {"type": "object", "properties": {"enterpriseId": {"type": "string", "description": "enterprise id"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.enterprise.v1.ListTenantTemplateParams_Filter"}], "description": "filter"}}, "description": "list tenant template params"}, "moego.enterprise.enterprise.v1.ListTenantTemplateParams_Filter": {"type": "object", "properties": {"tenantId": {"type": "array", "items": {"type": "string"}, "description": "tenant id"}, "status": {"type": "array", "items": {"enum": ["TENANT_TEMPLATE_STATUS_UNSPECIFIED", "NORMAL", "DELETE"], "type": "string", "format": "enum"}, "description": "status"}, "type": {"type": "array", "items": {"enum": ["TENANT_TEMPLATE_TYPE_UNSPECIFIED", "COMPANY", "CONFIG"], "type": "string", "format": "enum"}, "description": "type"}}, "description": "filter"}, "moego.enterprise.enterprise.v1.ListTenantTemplateResult": {"type": "object", "properties": {"tenantTemplates": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantTemplateModel"}, "description": "tenant templates"}}, "description": "list tenant templates result"}, "moego.enterprise.enterprise.v1.SyncFranchiseeParams": {"type": "object", "properties": {}, "description": "sync franchisee params"}, "moego.enterprise.enterprise.v1.SyncFranchiseeResult": {"type": "object", "properties": {}, "description": "sync franchisee result"}, "moego.enterprise.enterprise.v1.UpdateEnterpriseParams": {"type": "object", "properties": {"id": {"type": "string", "description": "enterprise id"}, "enterprise": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.UpdateEnterpriseDef"}], "description": "enterprise setting def"}, "tenantTemplate": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.UpdateTenantTemplateDef"}], "description": "optional"}}, "description": "UpdateEnterpriseParams"}, "moego.enterprise.enterprise.v1.UpdateEnterpriseResult": {"type": "object", "properties": {"enterprise": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.EnterpriseModel"}], "description": "enterprise"}, "tenantTemplate": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantTemplateModel"}], "description": "tenant template"}}, "description": "UpdateEnterpriseResult"}, "moego.enterprise.file.v1.GetUploadPresignedURLParams": {"type": "object", "properties": {"usage": {"type": "string", "description": "usage for the file ,eg: photo,avatar..."}, "md5": {"type": "string", "description": "file md5 after base64"}, "fileName": {"type": "string", "description": "file name(with extension)"}, "fileSizeByte": {"type": "string", "description": "file size byte"}, "ownerType": {"type": "string", "description": "owner type,eg:staff,pet..."}, "ownerId": {"type": "string", "description": "owner id"}}, "description": "get upload presigned url params"}, "moego.enterprise.file.v1.GetUploadPresignedURLResult": {"type": "object", "properties": {"presignedUrl": {"type": "string", "description": "presigned url for upload"}, "accessUrl": {"type": "string", "description": "access url for download"}, "contentType": {"type": "string", "description": "content-type,should add it into header when uploading file"}, "fileId": {"type": "string", "description": "file id"}}, "description": "get upload presigned url result"}, "moego.enterprise.file.v1.QueryFileParams": {"type": "object", "properties": {"fileId": {"type": "string", "description": "file id"}}, "description": "query file params"}, "moego.enterprise.file.v1.QueryFileResult": {"type": "object", "properties": {"file": {"allOf": [{"$ref": "#/components/schemas/moego.models.file.v2.FileModel"}], "description": "file info"}}, "description": "query file result"}, "moego.enterprise.map.v1.ListGooglePlaceParams": {"type": "object", "properties": {"placeIds": {"type": "array", "items": {"type": "string"}, "description": "place id list"}}, "description": "ListGooglePlaceParams"}, "moego.enterprise.map.v1.ListGooglePlaceResult": {"type": "object", "properties": {"places": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.map.v1.GooglePlace"}, "description": "google place list"}}, "description": "ListGooglePlaceResult"}, "moego.enterprise.map.v1.ListZipCodeParams": {"type": "object", "properties": {"zipCodes": {"type": "array", "items": {"type": "string"}, "description": "zip code"}}, "description": "list zip code params"}, "moego.enterprise.map.v1.ListZipCodeResult": {"type": "object", "properties": {"zipCodes": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.grooming.v1.ZipCodeModel"}, "description": "zip codes"}}, "description": "list zip code result"}, "moego.enterprise.map.v1.SearchZipCodeParams": {"type": "object", "properties": {"prefix": {"type": "string", "description": "prefix"}}, "description": "search zip code params"}, "moego.enterprise.map.v1.SearchZipCodeResult": {"type": "object", "properties": {"zipCodes": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.grooming.v1.ZipCodeModel"}, "description": "zip codes"}}, "description": "search zip code result"}, "moego.enterprise.permission.v1.CreateRoleParams": {"type": "object", "properties": {"name": {"type": "string", "description": "name"}}, "description": "create role request"}, "moego.enterprise.permission.v1.CreateRoleResult": {"type": "object", "properties": {"id": {"type": "string", "description": "created role id"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.RoleModel"}, "description": "role list after created"}}, "description": "create role response"}, "moego.enterprise.permission.v1.DeleteRoleParams": {"type": "object", "properties": {"id": {"type": "string", "description": "role id"}}, "description": "delete role request"}, "moego.enterprise.permission.v1.DeleteRoleResult": {"type": "object", "properties": {"roles": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.RoleModel"}, "description": "role list after deleted"}}, "description": "delete role response"}, "moego.enterprise.permission.v1.DuplicateRoleParams": {"type": "object", "properties": {"id": {"type": "string", "description": "role id"}, "name": {"type": "string", "description": "role name"}}, "description": "duplicate role request"}, "moego.enterprise.permission.v1.DuplicateRoleResult": {"type": "object", "properties": {"roles": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.RoleModel"}, "description": "role list after duplicated"}}, "description": "duplicate role response"}, "moego.enterprise.permission.v1.EditPermissionsParams": {"type": "object", "properties": {"roleId": {"type": "string", "description": "role id"}, "permissionCategories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.EditCategoryPermissionDef"}, "description": "permission list in a category"}}, "description": "edit permissions request"}, "moego.enterprise.permission.v1.EditPermissionsResult": {"type": "object", "properties": {"permissionCategories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.PermissionCategoryModel"}, "description": "permission list after edited"}}, "description": "edit permissions response"}, "moego.enterprise.permission.v1.GetRoleParams": {"type": "object", "properties": {"id": {"type": "string", "description": "role id"}, "needPermission": {"type": "boolean", "description": "need permission"}}, "description": "get role request"}, "moego.enterprise.permission.v1.GetRoleResult": {"type": "object", "properties": {"role": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.RoleModel"}], "description": "role"}, "permissionCategoryList": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.PermissionCategoryModel"}, "description": "role permissions"}}, "description": "get role response"}, "moego.enterprise.permission.v1.ListRolesParams": {"type": "object", "properties": {"enterpriseId": {"type": "string", "description": "enterprise id"}, "needPermission": {"type": "boolean", "description": "need permission"}}, "description": "get role list request"}, "moego.enterprise.permission.v1.ListRolesResult": {"type": "object", "properties": {"roles": {"type": "array", "items": {"$ref": "#/components/schemas/moego.enterprise.permission.v1.ListRolesResult_Result"}, "description": "role list"}}, "description": "get role list response"}, "moego.enterprise.permission.v1.ListRolesResult_Result": {"type": "object", "properties": {"role": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.RoleModel"}], "description": "role"}, "permissionCategoryList": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.PermissionCategoryModel"}, "description": "role permissions"}}, "description": "role result"}, "moego.enterprise.permission.v1.UpdateRoleParams": {"type": "object", "properties": {"id": {"type": "string", "description": "role id"}, "name": {"type": "string", "description": "role name"}}, "description": "update role request"}, "moego.enterprise.permission.v1.UpdateRoleResult": {"type": "object", "properties": {"role": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.RoleModel"}], "description": "role after updated"}}, "description": "update role response"}, "moego.enterprise.price_book.v1.CreateServiceParams": {"type": "object", "properties": {"priceBook": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.PriceBook"}], "description": "price book"}, "name": {"type": "string", "description": "name"}, "serviceItemType": {"enum": ["SERVICE_ITEM_TYPE_UNSPECIFIED", "GROOMING", "BOARDING", "DAYCARE", "EVALUATION", "DOG_WALKING", "GROUP_CLASS"], "type": "string", "description": "service item type", "format": "enum"}, "category": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceCategory"}], "description": "category"}, "description": {"type": "string", "description": "description"}, "inactive": {"type": "boolean", "description": "inactive"}, "color": {"type": "string", "description": "color"}, "sort": {"type": "string", "description": "sort"}, "price": {"allOf": [{"$ref": "#/components/schemas/google.type.Money"}], "description": "price"}, "servicePriceUnit": {"enum": ["SERVICE_PRICE_UNIT_UNSPECIFIED", "PER_SESSION", "PER_NIGHT", "PER_HOUR", "PER_DAY"], "type": "string", "description": "service price unit", "format": "enum"}, "taxRate": {"type": "string", "description": "万分位税率"}, "duration": {"allOf": [{"$ref": "#/components/schemas/google.protobuf.Duration"}], "description": "duration"}, "maxDuration": {"allOf": [{"$ref": "#/components/schemas/google.protobuf.Duration"}], "description": "max duration"}, "limitation": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Service_Limitation"}], "description": "limitation"}, "serviceType": {"enum": ["SERVICE_TYPE_UNSPECIFIED", "SERVICE", "ADDON"], "type": "string", "description": "service type", "format": "enum"}, "images": {"type": "array", "items": {"type": "string"}, "description": "images"}}, "description": "CreateServiceParams"}, "moego.enterprise.price_book.v1.CreateServiceResult": {"type": "object", "properties": {"service": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Service"}], "description": "service"}}, "description": "CreateServiceResult"}, "moego.enterprise.price_book.v1.DeleteServiceParams": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}}, "description": "DeleteServiceParams"}, "moego.enterprise.price_book.v1.DeleteServiceResult": {"type": "object", "properties": {}, "description": "DeleteServiceResult"}, "moego.enterprise.price_book.v1.GetServiceParams": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}}, "description": "GetServiceParams"}, "moego.enterprise.price_book.v1.GetServiceResult": {"type": "object", "properties": {"service": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Service"}], "description": "service"}}, "description": "GetServiceResult"}, "moego.enterprise.price_book.v1.ListPetBreedsParams": {"type": "object", "properties": {}, "description": "ListPetBreedsParams"}, "moego.enterprise.price_book.v1.ListPetBreedsResult": {"type": "object", "properties": {"petBreeds": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.PetBreed"}, "description": "pet breeds"}}, "description": "ListPetBreedsResult {"}, "moego.enterprise.price_book.v1.ListPetTypesParams": {"type": "object", "properties": {}, "description": "list pet types params"}, "moego.enterprise.price_book.v1.ListPetTypesResult": {"type": "object", "properties": {"petTypes": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.PetType"}, "description": "pet type list"}}, "description": "list pet types result"}, "moego.enterprise.price_book.v1.ListPriceBooksParams": {"type": "object", "properties": {}, "description": "ListPriceBooksParamas"}, "moego.enterprise.price_book.v1.ListPriceBooksResult": {"type": "object", "properties": {"priceBooks": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.PriceBook"}, "description": "price books"}}, "description": "ListPriceBooksResult"}, "moego.enterprise.price_book.v1.ListServiceCategoriesParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "page"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.service.enterprise.v1.ListServiceCategoriesRequest_Filter"}], "description": "filter"}}, "description": "ListServiceCategoriesParams"}, "moego.enterprise.price_book.v1.ListServiceCategoriesResult": {"type": "object", "properties": {"serviceCategories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceCategory"}, "description": "service categories"}}, "description": "ListServiceCategoriesResult"}, "moego.enterprise.price_book.v1.ListServiceChangeHistoriesParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "page"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.service.enterprise.v1.ListServiceChangeHistoriesRequest_Filter"}], "description": "filter"}, "orderBy": {"allOf": [{"$ref": "#/components/schemas/moego.service.enterprise.v1.ListServiceChangeHistoriesRequest_OrderBy"}], "description": "order by"}}, "description": "ListServiceChangeHistoriesParams"}, "moego.enterprise.price_book.v1.ListServiceChangeHistoriesResult": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "page"}, "serviceChangeHistories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceChangeHistory"}, "description": "service change history"}}, "description": "ListServiceChangeHistoriesResult"}, "moego.enterprise.price_book.v1.ListServiceChangesParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "page"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.service.enterprise.v1.ListServiceChangesRequest_Filter"}], "description": "filter"}}, "description": "ListServiceChangesParams"}, "moego.enterprise.price_book.v1.ListServiceChangesResult": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "page"}, "serviceChanges": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceChange"}, "description": "service changes"}}, "description": "ListServiceChangesResult"}, "moego.enterprise.price_book.v1.ListServicesParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "page"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.service.enterprise.v1.ListServicesRequest_Filter"}], "description": "filter"}}, "description": "ListServicesParams"}, "moego.enterprise.price_book.v1.ListServicesResult": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "page"}, "services": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.Service"}, "description": "services"}}, "description": "ListServicesResult"}, "moego.enterprise.price_book.v1.PushServiceChangesParams": {"type": "object", "properties": {"serviceIds": {"type": "array", "items": {"type": "string"}, "description": "service ids"}, "targets": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantObject"}, "description": "targets"}, "effectiveDate": {"type": "string", "description": "effective date", "format": "date-time"}, "applyToBookedServices": {"type": "boolean", "description": "apply to booked services"}}, "description": "PushServiceChangesParams"}, "moego.enterprise.price_book.v1.PushServiceChangesResult": {"type": "object", "properties": {"successCompanyIds": {"type": "array", "items": {"type": "string"}, "description": "success company ids"}, "failedCompanyIds": {"type": "array", "items": {"type": "string"}, "description": "failed company ids"}}, "description": "PushServiceChangesResult"}, "moego.enterprise.price_book.v1.SaveServiceCategoriesParams": {"type": "object", "properties": {"categories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceCategory"}, "description": "categories"}, "serviceType": {"enum": ["SERVICE_TYPE_UNSPECIFIED", "SERVICE", "ADDON"], "type": "string", "description": "service type", "format": "enum"}, "serviceItemType": {"enum": ["SERVICE_ITEM_TYPE_UNSPECIFIED", "GROOMING", "BOARDING", "DAYCARE", "EVALUATION", "DOG_WALKING", "GROUP_CLASS"], "type": "string", "description": "service item type", "format": "enum"}}, "description": "SaveServiceCategoriesParams"}, "moego.enterprise.price_book.v1.SaveServiceCategoriesResult": {"type": "object", "properties": {}, "description": "SaveServiceCategoriesResult"}, "moego.enterprise.price_book.v1.SortServicesParams": {"type": "object", "properties": {"serviceCategorySorts": {"type": "array", "items": {"$ref": "#/components/schemas/moego.service.enterprise.v1.SortServicesRequest_ServiceCategorySort"}, "description": "sorted services"}}, "description": "SortServicesParams"}, "moego.enterprise.price_book.v1.SortServicesResult": {"type": "object", "properties": {}, "description": "SortServicesResult"}, "moego.enterprise.price_book.v1.UpdateServiceParams": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "name": {"type": "string", "description": "name"}, "serviceCategory": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceCategory"}], "description": "category id"}, "description": {"type": "string", "description": "description"}, "inactive": {"type": "boolean", "description": "inactive"}, "color": {"type": "string", "description": "color"}, "price": {"allOf": [{"$ref": "#/components/schemas/google.type.Money"}], "description": "price"}, "servicePriceUnit": {"enum": ["SERVICE_PRICE_UNIT_UNSPECIFIED", "PER_SESSION", "PER_NIGHT", "PER_HOUR", "PER_DAY"], "type": "string", "description": "service price unit", "format": "enum"}, "taxRate": {"type": "string", "description": "万分位税率"}, "duration": {"allOf": [{"$ref": "#/components/schemas/google.protobuf.Duration"}], "description": "duration"}, "maxDuration": {"allOf": [{"$ref": "#/components/schemas/google.protobuf.Duration"}], "description": "max duration"}, "limitation": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Service_Limitation"}], "description": "limitation"}, "images": {"type": "array", "items": {"type": "string"}, "description": "images"}}, "description": "UpdateServiceParams"}, "moego.enterprise.price_book.v1.UpdateServiceResult": {"type": "object", "properties": {"service": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Service"}], "description": "service"}}, "description": "UpdateServiceResult"}, "moego.enterprise.report.v1.ExportReportDataRequest": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "filters"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "tenant ids"}, "currentPeriod": {"allOf": [{"$ref": "#/components/schemas/google.type.Interval"}], "description": "current period"}, "previousPeriod": {"allOf": [{"$ref": "#/components/schemas/google.type.Interval"}], "description": "previous period"}, "groupByFieldKeys": {"type": "array", "items": {"type": "string"}, "description": "group by field key"}, "orderBys": {"type": "array", "items": {"$ref": "#/components/schemas/moego.utils.v2.OrderBy"}, "description": "order by params"}, "groupByPeriod": {"enum": ["CALENDAR_PERIOD_UNSPECIFIED", "DAY", "WEEK", "FORTNIGHT", "MONTH", "QUARTER", "HALF", "YEAR"], "type": "string", "description": "group by period, could be day, week, month, year etc., default day.", "format": "enum"}, "filterGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequestGroup"}, "description": "filter groups"}, "allTenants": {"type": "boolean", "description": "is all tenants"}}, "description": "ExportReportDataRequest"}, "moego.enterprise.report.v1.ExportReportDataResponse": {"type": "object", "properties": {"fileId": {"type": "string", "description": "file id"}}, "description": "ExportReportDataResponse"}, "moego.enterprise.report.v1.FetchDashboardDataRequest": {"type": "object", "properties": {"diagramIds": {"type": "array", "items": {"type": "string"}, "description": "diagram ids"}, "currentPeriod": {"allOf": [{"$ref": "#/components/schemas/google.type.Interval"}], "description": "current interval"}, "previousPeriod": {"allOf": [{"$ref": "#/components/schemas/google.type.Interval"}], "description": "previous interval"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "The tenant id"}, "allTenants": {"type": "boolean", "description": "Whether to fetch data for all tenants"}}, "description": "Describe a request to fetch dashboard diagram data"}, "moego.enterprise.report.v1.FetchDashboardDataResponse": {"type": "object", "properties": {"diagramData": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DiagramData"}, "description": "The dashboard diagram data"}}, "description": "Describe a response to fetch dashboard diagram data"}, "moego.enterprise.report.v1.FetchReportDataRequest": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "The tenant id"}, "groupByPeriod": {"enum": ["CALENDAR_PERIOD_UNSPECIFIED", "DAY", "WEEK", "FORTNIGHT", "MONTH", "QUARTER", "HALF", "YEAR"], "type": "string", "description": "group by period, could be day, week, month, year etc., default day.", "format": "enum"}, "currentPeriod": {"allOf": [{"$ref": "#/components/schemas/google.type.Interval"}], "description": "current period"}, "previousPeriod": {"allOf": [{"$ref": "#/components/schemas/google.type.Interval"}], "description": "previous period"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "Filters"}, "groupByFieldKeys": {"type": "array", "items": {"type": "string"}, "description": "The group by field key"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "The pagination request"}, "orderBys": {"type": "array", "items": {"$ref": "#/components/schemas/moego.utils.v2.OrderBy"}, "description": "The order by config"}, "filterGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequestGroup"}, "description": "The filter groups"}, "allTenants": {"type": "boolean", "description": "All tenants"}}, "description": "Describe a request to fetch report data"}, "moego.enterprise.report.v1.FetchReportDataResponse": {"type": "object", "properties": {"tableData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TableData"}], "description": "The report data"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "The pagination response"}}, "description": "Describe a response to fetch report data"}, "moego.enterprise.report.v1.MarkReportFavoriteRequest": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "The report to mark as favorite"}, "action": {"enum": ["FAVORITE_ACTION_UNSPECIFIED", "ADD", "REMOVE"], "type": "string", "description": "The action to take", "format": "enum"}}, "description": "MarkReportAsFavoriteRequest"}, "moego.enterprise.report.v1.MarkReportFavoriteResponse": {"type": "object", "properties": {"result": {"type": "boolean", "description": "Mark result"}}, "description": "MarkReportFavoriteResponse"}, "moego.enterprise.report.v1.QueryDashboardPagesRequest": {"type": "object", "properties": {"tabs": {"type": "array", "items": {"enum": ["TAB_UNSPECIFIED", "OVERVIEW", "SALES", "PETS", "STAFF", "OPERATION", "APP_PERSONAL_PERFORMANCE", "APP_OVERVIEW", "APP_STAFF_PERFORMANCE", "APP_HISTORY", "APP_REPORT", "PAYROLL", "DAILY_REVENUE", "LEADS"], "type": "string", "format": "enum"}, "description": "The tabs of query dashboard page"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "tenant ids"}, "allTenants": {"type": "boolean", "description": "Whether to fetch data for all tenants"}}, "description": "Describe pages of dashboard"}, "moego.enterprise.report.v1.QueryDashboardPagesResponse": {"type": "object", "properties": {"dashboardPages": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DashboardPage"}, "description": "The list of dashboard pages"}, "customizedConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TableCustomizedConfig"}], "description": "The customized config"}}, "description": "Describe pages of dashboard"}, "moego.enterprise.report.v1.QueryReportMetasRequest": {"type": "object", "properties": {"diagramIds": {"type": "array", "items": {"type": "string"}, "description": "The report to query meta data, if empty, return all reports' meta data"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "tenant ids"}, "allTenants": {"type": "boolean", "description": "all tenants"}}, "description": "QueryReportsMetaRequest"}, "moego.enterprise.report.v1.QueryReportPagesRequest": {"type": "object", "properties": {"tabs": {"type": "array", "items": {"enum": ["TAB_UNSPECIFIED", "ALL", "FINANCE", "SALES", "APPOINTMENT", "EMPLOYEE", "PAYROLL", "CLIENT_INSIGHTS", "CUSTOMER", "TENANT", "CUSTOMIZED", "LEGACY_APPOINTMENT"], "type": "string", "format": "enum"}, "description": "The tabs to query reports page"}}, "description": "QueryReportsRequest"}, "moego.enterprise.report.v1.QueryReportPagesResponse": {"type": "object", "properties": {"pages": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.ReportPage"}, "description": "The list of report pages"}}, "description": "QueryReportsResponse"}, "moego.enterprise.report.v1.QueryReportsMetasResponse": {"type": "object", "properties": {"reportMetas": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.TableMeta"}, "description": "The list of report metadata"}}, "description": "QueryReportsMetaRequest"}, "moego.enterprise.report.v1.SaveReportCustomizeConfigRequest": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "The report to save customized configs"}, "customizedConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TableCustomizedConfig"}], "description": "Customize configs to save"}}, "description": "SaveReportCustomizeConfigRequest"}, "moego.enterprise.session.v1.GetSessionDataParams": {"type": "object", "properties": {}, "description": "GetSessionDataParams"}, "moego.enterprise.session.v1.GetSessionDataResult": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "session id"}, "account": {"allOf": [{"$ref": "#/components/schemas/moego.models.account.v1.AccountModel"}], "description": "account info"}, "staff": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.StaffModel"}], "description": "staff info"}, "enterprise": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.EnterpriseModel"}], "description": "enterprise info"}}, "description": "GetSessionDataResult"}, "moego.enterprise.staff.v1.CreateStaffParams": {"type": "object", "properties": {"profile": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CreateStaffProfile"}], "description": "staff profile"}, "resources": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.ResourceDef"}], "description": "resources"}, "inviteLink": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.SendInviteLinkParamsDef"}], "description": "send invite link params"}}, "description": "create staff params"}, "moego.enterprise.staff.v1.CreateStaffResult": {"type": "object", "properties": {"staff": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.StaffModel"}], "description": "staff"}, "resources": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.ResourceDef"}], "description": "resources"}, "staffEmail": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.StaffEmailDef"}], "description": "staff email"}}, "description": "create staff response"}, "moego.enterprise.staff.v1.DeleteStaffParams": {"type": "object", "properties": {"id": {"type": "string", "description": "staff id"}}, "description": "delete staff"}, "moego.enterprise.staff.v1.DeleteStaffResult": {"type": "object", "properties": {}, "description": "delete staff result"}, "moego.enterprise.staff.v1.GetStaffParams": {"type": "object", "properties": {"id": {"type": "string", "description": "staff id"}}, "description": "get staff"}, "moego.enterprise.staff.v1.GetStaffResult": {"type": "object", "properties": {"staff": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.StaffModel"}], "description": "staff profile"}, "resources": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.ResourceDef"}], "description": "resources"}, "staffEmail": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.StaffEmailDef"}], "description": "staff email"}}, "description": "get staff result"}, "moego.enterprise.staff.v1.ListStaffsParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "pagination"}, "orderBy": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.OrderBy"}], "description": "order by"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.staff.v1.ListStaffsParams_Filter"}], "description": "filter"}}, "description": "list staff params"}, "moego.enterprise.staff.v1.ListStaffsParams_Filter": {"type": "object", "properties": {"roleIds": {"type": "array", "items": {"type": "string"}, "description": "role id"}, "keyword": {"type": "string", "description": "keyword"}}, "description": "filter"}, "moego.enterprise.staff.v1.ListStaffsResult": {"type": "object", "properties": {"staffs": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.StaffModel"}, "description": "staff list"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "pagination"}, "idToStaffEmails": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/moego.models.organization.v1.StaffEmailDef"}, "description": "staff email"}, "staffToAssignTenantNums": {"type": "object", "additionalProperties": {"type": "string"}, "description": "staff assign tenant nums"}}, "description": "list staff"}, "moego.enterprise.staff.v1.SendStaffInviteLinkParams": {"type": "object", "properties": {"inviteLink": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.SendInviteLinkDef"}], "description": "send invite link def"}}, "description": "send invite link staff"}, "moego.enterprise.staff.v1.SendStaffInviteLinkResult": {"type": "object", "properties": {}, "description": "send invite link staff result"}, "moego.enterprise.staff.v1.UnlinkStaffParams": {"type": "object", "properties": {"id": {"type": "string", "description": "staff id"}}, "description": "unlink staff"}, "moego.enterprise.staff.v1.UnlinkStaffResult": {"type": "object", "properties": {}, "description": "unlink staff result"}, "moego.enterprise.staff.v1.UpdateStaffParams": {"type": "object", "properties": {"id": {"type": "string", "description": "staff id"}, "profile": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.UpdateStaffProfile"}], "description": "staff profile"}, "resources": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.ResourceDef"}], "description": "resources"}, "inviteLink": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.SendInviteLinkParamsDef"}], "description": "send invite link params"}}, "description": "update staff params"}, "moego.enterprise.staff.v1.UpdateStaffResult": {"type": "object", "properties": {"staff": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.StaffModel"}], "description": "staff profile"}, "resources": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.ResourceDef"}], "description": "resources"}, "staffEmail": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.StaffEmailDef"}], "description": "staff email"}}, "description": "update staff response"}, "moego.enterprise.subscription.v1.GetEnterpriseUnitParams": {"type": "object", "properties": {}, "description": "GetEnterpriseUnitParams"}, "moego.enterprise.subscription.v1.GetEnterpriseUnitResponse": {"type": "object", "properties": {"totalLocationNum": {"type": "string", "description": "total location num in subscription"}, "totalVanNum": {"type": "string", "description": "total van num in subscription"}, "usedLocationNum": {"type": "string", "description": "used location num"}, "usedVanNum": {"type": "string", "description": "used van num"}}, "description": "GetEnterpriseUnitResponse"}, "moego.enterprise.subscription.v1.ListCardsParams": {"type": "object", "properties": {"enterpriseId": {"type": "string", "description": "Enterprise ID"}}, "description": "ListCardsParams"}, "moego.enterprise.subscription.v1.ListCardsResponse": {"type": "object", "properties": {"cards": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.payment.v1.Card"}, "description": "Cards"}}, "description": "ListCardsResponse"}, "moego.enterprise.tenant.v1.CreateTenantParams": {"type": "object", "properties": {"accountId": {"type": "string", "description": "account id"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "tenant": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CreateTenantDef"}], "description": "tenant"}, "groupDef": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.GroupDef"}], "description": "group ids"}}, "description": "create tenant request"}, "moego.enterprise.tenant.v1.CreateTenantResult": {"type": "object", "properties": {"tenant": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantModel"}], "description": "tenant"}}, "description": "create tenant response"}, "moego.enterprise.tenant.v1.CreateTerritoryParams": {"type": "object", "properties": {"enterpriseId": {"type": "string", "description": "enterprise id"}, "zipCodes": {"type": "array", "items": {"type": "string"}, "description": "zip code"}, "type": {"enum": ["TERRITORY_TYPE_UNSPECIFIED", "ZIP_CODE", "DRAW"], "type": "string", "description": "territory type", "format": "enum"}}, "description": "create territory params"}, "moego.enterprise.tenant.v1.CreateTerritoryResult": {"type": "object", "properties": {"territory": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TerritoryModel"}], "description": "territory"}}, "description": "create territory result"}, "moego.enterprise.tenant.v1.DeleteTenantGroupParams": {"type": "object", "properties": {"groupId": {"type": "string", "description": "group id"}}, "description": "delete tenant group params"}, "moego.enterprise.tenant.v1.DeleteTenantGroupResult": {"type": "object", "properties": {}, "description": "delete tenant group result"}, "moego.enterprise.tenant.v1.DeleteTenantParams": {"type": "object", "properties": {"id": {"type": "string", "description": "tenant id"}}, "description": "delete tenant request"}, "moego.enterprise.tenant.v1.DeleteTenantResult": {"type": "object", "properties": {}, "description": "delete tenant result"}, "moego.enterprise.tenant.v1.DeleteTerritoryParams": {"type": "object", "properties": {"id": {"type": "string", "description": "territory id"}}, "description": "delete territory params"}, "moego.enterprise.tenant.v1.DeleteTerritoryResult": {"type": "object", "properties": {}, "description": "delete territory result"}, "moego.enterprise.tenant.v1.GetTenantParams": {"type": "object", "properties": {"id": {"type": "string", "description": "tenant id"}, "needTerritory": {"type": "boolean", "description": "need territory"}, "needTenantGroup": {"type": "boolean", "description": "need tenant group"}}, "description": "get tenant request"}, "moego.enterprise.tenant.v1.GetTenantResult": {"type": "object", "properties": {"tenant": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantModel"}], "description": "tenant"}, "tenantTemplate": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantTemplateModel"}], "description": "tenant template model"}, "territories": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TerritoryModel"}], "description": "territories"}, "tenantGroup": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantGroupModel"}, "description": "tenant group"}, "staff": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.StaffModel"}], "description": "tenant group def"}, "staffEmail": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.StaffEmailDef"}], "description": "staff email"}}, "description": "get tenant response"}, "moego.enterprise.tenant.v1.GetTerritoryParams": {"type": "object", "properties": {"id": {"type": "string", "description": "territory id"}}, "description": "get territory params"}, "moego.enterprise.tenant.v1.GetTerritoryResult": {"type": "object", "properties": {"territory": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TerritoryModel"}], "description": "territory"}}, "description": "get territory result"}, "moego.enterprise.tenant.v1.ListAllTenantAndGroupParams": {"type": "object", "properties": {}, "description": "list all tenant and group params"}, "moego.enterprise.tenant.v1.ListAllTenantAndGroupResult": {"type": "object", "properties": {"tenantViews": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantView"}, "description": "tenant list"}, "groupTenantRelation": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.GroupTenantDef"}, "description": "tenant group list"}, "assignTenantIds": {"type": "array", "items": {"type": "string"}, "description": "assign tenant list"}, "assignGroupIds": {"type": "array", "items": {"type": "string"}, "description": "assign group list"}}, "description": "list all tenant and group result"}, "moego.enterprise.tenant.v1.ListTenantGroupParams": {"type": "object", "properties": {"filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTenantGroupParams_Filter"}], "description": "filter"}}, "description": "list tenant group params"}, "moego.enterprise.tenant.v1.ListTenantGroupParams_Filter": {"type": "object", "properties": {"groupIds": {"type": "array", "items": {"type": "string"}, "description": "group ids"}}, "description": "filter"}, "moego.enterprise.tenant.v1.ListTenantGroupResult": {"type": "object", "properties": {"tenantGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantGroupModel"}, "description": "tenant groups"}}, "description": "list tenant group result"}, "moego.enterprise.tenant.v1.ListTenantParams": {"type": "object", "properties": {"pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "pagination"}, "needTenantGroup": {"type": "boolean", "description": "need tenant group"}, "orderBy": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.OrderBy"}], "description": "order by"}, "filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTenantParams_Filter"}], "description": "filter"}}, "description": "list tenant request"}, "moego.enterprise.tenant.v1.ListTenantParams_Filter": {"type": "object", "properties": {"groupIds": {"type": "array", "items": {"type": "string"}, "description": "group names"}, "statuses": {"type": "array", "items": {"enum": ["TENANT_STATUS_UNSPECIFIED", "INSTANTIATED", "LIVE", "DELETED"], "type": "string", "format": "enum"}, "description": "statuses"}, "keyword": {"type": "string", "description": "keyword"}, "types": {"type": "array", "items": {"enum": ["TYPE_UNSPECIFIED", "NORMAL", "TRAINING", "TEMPLATE"], "type": "string", "format": "enum"}, "description": "types"}}, "description": "filter"}, "moego.enterprise.tenant.v1.ListTenantResult": {"type": "object", "properties": {"tenants": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantModel"}, "description": "tenant list"}, "staff": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.organization.v1.StaffModel"}, "description": "account info"}, "tenantTemplate": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantTemplateModel"}, "description": "territory model"}, "territories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TerritoryModel"}, "description": "territories"}, "tenantGroup": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantGroupModel"}, "description": "tenant group"}, "tenantGroupRelation": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantGroupDef"}, "description": "tenant group def"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "pagination"}}, "description": "list tenant params"}, "moego.enterprise.tenant.v1.ListTerritoriesParams": {"type": "object", "properties": {"filter": {"allOf": [{"$ref": "#/components/schemas/moego.enterprise.tenant.v1.ListTerritoriesParams_Filter"}], "description": "filter"}}, "description": "get territory list params"}, "moego.enterprise.tenant.v1.ListTerritoriesParams_Filter": {"type": "object", "properties": {"territoryIds": {"type": "array", "items": {"type": "string"}, "description": "territory ids"}, "types": {"type": "array", "items": {"enum": ["TERRITORY_TYPE_UNSPECIFIED", "ZIP_CODE", "DRAW"], "type": "string", "format": "enum"}, "description": "type"}, "statuses": {"type": "array", "items": {"enum": ["TERRITORY_STATUS_UNSPECIFIED", "NORMAL", "DELETE"], "type": "string", "format": "enum"}, "description": "statuses"}}, "description": "filter"}, "moego.enterprise.tenant.v1.ListTerritoriesResult": {"type": "object", "properties": {"territories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.TerritoryModel"}, "description": "territories"}}, "description": "get territory list result"}, "moego.enterprise.tenant.v1.UpdateTenantParams": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "accountId": {"type": "string", "description": "account id"}, "tenant": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.UpdateTenantDef"}], "description": "tenant"}, "groupDef": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.GroupDef"}], "description": "group ids"}}, "description": "update tenant request"}, "moego.enterprise.tenant.v1.UpdateTenantResult": {"type": "object", "properties": {"tenant": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantModel"}], "description": "tenant"}}, "description": "update tenant request"}, "moego.enterprise.tenant.v1.UpdateTerritoryParams": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "zipCodes": {"type": "array", "items": {"type": "string"}, "description": "zip code"}, "type": {"enum": ["TERRITORY_TYPE_UNSPECIFIED", "ZIP_CODE", "DRAW"], "type": "string", "description": "territory type", "format": "enum"}}, "description": "update territory params"}, "moego.enterprise.tenant.v1.UpdateTerritoryResult": {"type": "object", "properties": {"territory": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TerritoryModel"}], "description": "territory"}}, "description": "update territory result"}, "moego.models.account.v1.AccountModel": {"type": "object", "properties": {"id": {"type": "string", "description": "account id"}, "email": {"type": "string", "description": "email"}, "phoneNumber": {"type": "string", "description": "phone number"}, "firstName": {"type": "string", "description": "first name"}, "lastName": {"type": "string", "description": "last name"}, "avatarPath": {"type": "string", "description": "avatar path"}, "status": {"enum": ["ACCOUNT_STATUS_UNSPECIFIED", "ACCOUNT_STATUS_ACTIVE", "ACCOUNT_STATUS_DELETED", "ACCOUNT_STATUS_FROZEN"], "type": "string", "description": "status", "format": "enum"}, "namespace": {"allOf": [{"$ref": "#/components/schemas/moego.models.account.v1.Namespace"}], "description": "namespace"}, "createdAt": {"type": "string", "description": "created at", "format": "date-time"}, "updatedAt": {"type": "string", "description": "updated at", "format": "date-time"}}, "description": "account model"}, "moego.models.account.v1.AccountSecurityModel": {"type": "object", "properties": {"passwordLastUpdateTime": {"type": "string", "description": "password last update time", "format": "date-time"}}, "description": "account security model"}, "moego.models.account.v1.Namespace": {"type": "object", "properties": {"type": {"enum": ["ACCOUNT_NAMESPACE_TYPE_UNSPECIFIED", "MOEGO", "ENTERPRISE", "COMPANY"], "type": "string", "description": "namespace type", "format": "enum"}, "id": {"type": "string", "description": "namespace id"}}, "description": "namespace"}, "moego.models.automation.v1.AddClientTagData": {"type": "object", "properties": {"customerTagIds": {"type": "array", "items": {"type": "string"}, "description": "Customer Tag ids"}}, "description": "AddClientData"}, "moego.models.automation.v1.AddCustomerTaskData": {"type": "object", "properties": {"name": {"type": "string", "description": "任务名称"}, "allocateStaffId": {"type": "string", "description": "分配员工"}}, "description": "AddCustomerTaskData"}, "moego.models.automation.v1.AddPetCodeData": {"type": "object", "properties": {"petCodeIds": {"type": "array", "items": {"type": "string"}, "description": "Pet Code ids"}}, "description": "AddPetCodeData"}, "moego.models.automation.v1.AdvancedAction": {"type": "object", "properties": {"type": {"enum": ["TYPE_UNSPECIFIED", "ADD_CLIENT_TAG", "DELETE_CLIENT_TAG", "CHANGE_CLIENT_TAG", "ADD_PET_CODE", "CHANGE_CUSTOMER_LIFE_CYCLE", "CHANGE_CUSTOMER_ACTION_STATUS", "ADD_CUSTOMER_TASK"], "type": "string", "description": "type", "format": "enum"}, "data": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.AdvancedAction_Data"}], "description": "data"}}, "description": "AdvancedAction"}, "moego.models.automation.v1.AdvancedAction_Data": {"type": "object", "properties": {"addClientTagData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.AddClientTagData"}], "description": "Add Client Tag"}, "deleteClientTagData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.DeleteClientTagData"}], "description": "Delete Client Tag"}, "changeClientTagData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.ChangeClientTagData"}], "description": "Change Client Tag"}, "addPetCodeData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.AddPetCodeData"}], "description": "Add Pet Code"}, "changeCustomerLifeCycleData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.ChangeCustomerLifeCycleData"}], "description": "Change Customer Life Cycle"}, "changeCustomerActionStatusData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.ChangeCustomerActionStatusData"}], "description": "Change Customer Action Status"}, "addCustomerTaskData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.AddCustomerTaskData"}], "description": "Add Customer Task"}}, "description": "Data"}, "moego.models.automation.v1.ChangeClientTagData": {"type": "object", "properties": {"sourceCustomerTagIds": {"type": "array", "items": {"type": "string"}, "description": "Source Customer Tag ids"}, "targetCustomerTagIds": {"type": "array", "items": {"type": "string"}, "description": "Target Customer Tag ids"}}, "description": "ChangeClientData"}, "moego.models.automation.v1.ChangeCustomerActionStatusData": {"type": "object", "properties": {"actionStatus": {"type": "integer", "description": "Action Status, definition on https://github.com/MoeGolibrary/moego/blob/main/backend/proto/customer/v1/customer_models.proto", "format": "int32"}}, "description": "ChangeCustomerActionStatus"}, "moego.models.automation.v1.ChangeCustomerLifeCycleData": {"type": "object", "properties": {"lifeCycle": {"type": "integer", "description": "Life Cycle, definition on https://github.com/MoeGolibrary/moego/blob/main/backend/proto/customer/v1/customer_models.proto", "format": "int32"}}, "description": "ChangeCustomerLifeCycle"}, "moego.models.automation.v1.Condition": {"type": "object", "properties": {"filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "Filters"}, "nextStepIdTrue": {"type": "string", "description": "Next step ID if true"}, "nextStepIdFalse": {"type": "string", "description": "Next step ID if false"}, "type": {"enum": ["TYPE_UNSPECIFIED", "FILTER", "ACTION_FILTER"], "type": "string", "description": "Type", "format": "enum"}, "actionResultFilter": {"enum": ["ACTION_FILTER_UNSPECIFIED", "INTAKE_FORM_SUBMIT_SUCCESS"], "type": "string", "description": "Action Filters", "format": "enum"}}, "description": "Condition"}, "moego.models.automation.v1.CreateStepDef": {"type": "object", "properties": {"id": {"type": "string", "description": "meta data Step ID"}, "name": {"type": "string", "description": "Step name"}, "description": {"type": "string", "description": "Step description"}, "parentId": {"type": "string", "description": "adjacency data Parent step ID"}, "type": {"enum": ["TYPE_UNSPECIFIED", "TRIGGER", "WAIT", "MESSAGE", "CONDITION", "ADVANCED_ACTION"], "type": "string", "description": "content data Step type", "format": "enum"}, "data": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Step_Data"}], "description": "Step data"}, "previewData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Step_PreviewData"}], "description": "Step data preview"}}, "description": "CreateStepDefs"}, "moego.models.automation.v1.CreateWorkflowDef": {"type": "object", "properties": {"name": {"type": "string", "description": "meta data Workflow name"}, "description": {"type": "string", "description": "Workflow description"}, "image": {"type": "string", "description": "Workflow image"}, "recommendImage": {"type": "string", "description": "Workflow recommend_image"}, "workflowTemplateId": {"type": "string", "description": "Workflow Source Template ID"}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.automation.v1.CreateStepDef"}, "description": "content data Steps definitions"}, "consumerData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Workflow_ConsumerData"}], "description": "Workflow Consumer Data"}, "category": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.automation.v1.WorkflowCategory"}, "description": "category Workflow categories"}, "recommendType": {"enum": ["RECOMMEND_TYPE_UNSPECIFIED", "HOME_PAGE"], "type": "string", "description": "Recommend type", "format": "enum"}, "workflowEnterpriseApply": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.WorkflowEnterpriseApply"}], "description": "enterprise apply to"}}, "description": "CreateWorkflowDefs"}, "moego.models.automation.v1.DeleteClientTagData": {"type": "object", "properties": {"customerTagIds": {"type": "array", "items": {"type": "string"}, "description": "Customer Tag ids"}}, "description": "DeleteClientData"}, "moego.models.automation.v1.EmailData": {"type": "object", "properties": {"content": {"type": "string", "description": "Content"}, "title": {"type": "string", "description": "Title"}}, "description": "EmailData"}, "moego.models.automation.v1.Event": {"type": "object", "properties": {"category": {"enum": ["CATEGORY_UNSPECIFIED", "ONLINE_BOOKINGS", "APPOINTMENT", "INTAKE_FROM", "PACKAGE", "MEMBERSHIP", "DISCOUNT", "CLIENT"], "type": "string", "description": "Event category", "format": "enum"}, "trigger": {"enum": ["ENTITY_UNSPECIFIED", "ONLINE_BOOKINGS_SUBMITTED", "ONLINE_BOOKINGS_ABANDONED", "ONLINE_BOOKINGS_ACCEPTED", "EVALUATION_BOOKING_SUBMITTED", "APPOINTMENT_CREATED", "APPOINTMENT_CANCELED", "APPOINTMENT_FINISHED", "APPOINTMENT_FULLY_PAID", "EVALUATION_FINISHED", "PACKAGE_PURCHASED", "PACKAGE_REDEEMED", "MEMBERSHIP_PURCHASED", "MEMBERSHIP_CANCELED", "DISCOUNT_CODE_REDEEMED", "CLIENT_CREATED"], "type": "string", "description": "Event trigger", "format": "enum"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "Event Filters"}}, "description": "Event"}, "moego.models.automation.v1.Field": {"type": "object", "properties": {"key": {"type": "string", "description": "key"}, "name": {"type": "string", "description": "name"}, "placeHolderName": {"type": "string", "description": "place_holder_name"}, "description": {"type": "string", "description": "description"}}, "description": "Field"}, "moego.models.automation.v1.Message": {"type": "object", "properties": {"type": {"enum": ["TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "description": "Action type", "format": "enum"}, "category": {"enum": ["CATEGORY_UNSPECIFIED", "NOTIFICATION", "CAMPAIGN"], "type": "string", "description": "Action category", "format": "enum"}, "data": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Message_Data"}], "description": "Action data"}}, "description": "Message"}, "moego.models.automation.v1.Message_Data": {"type": "object", "properties": {"smsData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.SMSData"}], "description": "SMS data"}, "emailData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.EmailData"}], "description": "Email data"}}, "description": "Data"}, "moego.models.automation.v1.SMSData": {"type": "object", "properties": {"content": {"type": "string", "description": "Content"}}, "description": "SMSData"}, "moego.models.automation.v1.Scheduled": {"type": "object", "properties": {"dayOfWeek": {"enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "type": "string", "description": "Day of week", "format": "enum"}, "timeOfDay": {"allOf": [{"$ref": "#/components/schemas/google.type.TimeOfDay"}], "description": "Time of day"}, "date": {"type": "string", "description": "Date", "format": "date"}, "frequency": {"enum": ["FREQUENCY_UNSPECIFIED", "DAILY", "WEEKLY", "MONTHLY"], "type": "string", "description": "Frequency", "format": "enum"}}, "description": "Scheduled"}, "moego.models.automation.v1.Step": {"type": "object", "properties": {"id": {"type": "string", "description": "meta data Step ID"}, "workflowId": {"type": "string", "description": "Workflow ID"}, "name": {"type": "string", "description": "Step name"}, "description": {"type": "string", "description": "Step description"}, "parentId": {"type": "string", "description": "adjacency data Parent step ID"}, "childrenIds": {"type": "array", "items": {"type": "string"}, "description": "Children step IDs"}, "hierarchicalPath": {"type": "string", "description": "Hierarchical path"}, "type": {"enum": ["TYPE_UNSPECIFIED", "TRIGGER", "WAIT", "MESSAGE", "CONDITION", "ADVANCED_ACTION"], "type": "string", "description": "content data Step type", "format": "enum"}, "data": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Step_Data"}], "description": "Step data"}, "previewData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Step_PreviewData"}], "description": "Step data preview"}}, "description": "Step"}, "moego.models.automation.v1.Step_Data": {"type": "object", "properties": {"trigger": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Trigger"}], "description": "<PERSON><PERSON>"}, "wait": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Wait"}], "description": "Wait"}, "message": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Message"}], "description": "Message"}, "condition": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Condition"}], "description": "Condition"}, "advancedAction": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.AdvancedAction"}], "description": "Advanced action"}}, "description": "Data"}, "moego.models.automation.v1.Step_PreviewData": {"type": "object", "properties": {"description": {"type": "string", "description": "preview description"}}, "description": "Preview Data"}, "moego.models.automation.v1.TimeDuration": {"type": "object", "properties": {"unit": {"enum": ["UNIT_UNSPECIFIED", "DAY", "WEEK", "MONTH", "HOUR"], "type": "string", "description": "unit", "format": "enum"}, "value": {"type": "string", "description": "value"}, "duration": {"allOf": [{"$ref": "#/components/schemas/google.protobuf.Duration"}], "description": "duration"}}, "description": "TimeDuration x Hour/Day/Week/Month"}, "moego.models.automation.v1.Trigger": {"type": "object", "properties": {"type": {"enum": ["TYPE_UNSPECIFIED", "SCHEDULED", "EVENT", "BEFORE"], "type": "string", "description": "Trigger type", "format": "enum"}, "data": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Trigger_Data"}], "description": "Trigger data"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "Filters"}, "name": {"type": "string", "description": "Trigger name"}}, "description": "<PERSON><PERSON>"}, "moego.models.automation.v1.Trigger_Data": {"type": "object", "properties": {"scheduled": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Scheduled"}], "description": "Scheduled"}, "event": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Event"}], "description": "Event"}}, "description": "Data"}, "moego.models.automation.v1.Wait": {"type": "object", "properties": {"type": {"enum": ["TYPE_UNSPECIFIED", "DURATION"], "type": "string", "description": "Wait type", "format": "enum"}, "timeDuration": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.TimeDuration"}], "description": "Time duration"}}, "description": "Wait"}, "moego.models.automation.v1.Workflow": {"type": "object", "properties": {"id": {"type": "string", "description": "meta data Workflow ID"}, "companyId": {"type": "string", "description": "Company ID"}, "name": {"type": "string", "description": "Workflow name"}, "description": {"type": "string", "description": "Workflow description"}, "status": {"enum": ["STATUS_UNSPECIFIED", "DRAFT", "ACTIVE", "INACTIVE", "DELETED"], "type": "string", "description": "Workflow status", "format": "enum"}, "type": {"enum": ["TYPE_UNSPECIFIED", "WORKFLOW", "TEMPLATE"], "type": "string", "description": "Workflow type", "format": "enum"}, "createdAt": {"type": "string", "description": "Created at timestamp", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Updated at timestamp", "format": "date-time"}, "createdBy": {"type": "string", "description": "Created by user ID"}, "updatedBy": {"type": "string", "description": "Updated by user ID"}, "photoGallery": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Workflow_PhotoGallery"}], "description": "Workflow image URL"}, "enterpriseId": {"type": "string", "description": "Enterprise ID"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "Tenants IDs"}, "version": {"type": "string", "description": "Version"}, "steps": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.automation.v1.Step"}, "description": "content data Steps"}, "triggerStepId": {"type": "string", "description": "Trigger step ID"}, "triggerType": {"enum": ["TYPE_UNSPECIFIED", "SCHEDULED", "EVENT", "BEFORE"], "type": "string", "description": "Trigger type", "format": "enum"}, "triggerSchedule": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Scheduled"}], "description": "Trigger schedule"}, "consumerData": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Workflow_ConsumerData"}], "description": "Consumer data"}, "lastTriggerTime": {"type": "string", "description": "record Last trigger time", "format": "date-time"}, "category": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.automation.v1.WorkflowCategory"}, "description": "category Categories"}, "recommendType": {"enum": ["RECOMMEND_TYPE_UNSPECIFIED", "HOME_PAGE"], "type": "string", "description": "Recommend type", "format": "enum"}, "reachNum": {"type": "integer", "description": "counter Number of reaches", "format": "int32"}, "newBookNum": {"type": "integer", "description": "Number of new bookings", "format": "int32"}, "applyTenantsNum": {"type": "integer", "description": "Number of apply franchisees", "format": "int32"}, "workflowEnterpriseApply": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.WorkflowEnterpriseApply"}], "description": "enterprise apply to"}}, "description": "Workflow"}, "moego.models.automation.v1.WorkflowCategory": {"type": "object", "properties": {"id": {"type": "string", "description": "Category ID"}, "name": {"type": "string", "description": "Category name"}, "desc": {"type": "string", "description": "Category description"}, "icon": {"type": "string", "description": "Category icon URL"}}, "description": "WorkflowCategory"}, "moego.models.automation.v1.WorkflowConfig": {"type": "object", "properties": {"trigger": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.Trigger"}], "description": "<PERSON><PERSON>"}, "placeHolders": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.automation.v1.Field"}, "description": "Placeholders"}, "filterGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterGroup"}, "description": "Filter groups"}, "eventFilterGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterGroup"}, "description": "Event filter groups"}}, "description": "WorkflowConfig"}, "moego.models.automation.v1.WorkflowEnterpriseApply": {"type": "object", "properties": {"type": {"enum": ["TYPE_UNSPECIFIED", "ALL_FRANCHISEES", "FRANCHISEES_GROUPS", "FRANCHISEES"], "type": "string", "description": "Apply Type", "format": "enum"}, "tenantsGroupIds": {"type": "array", "items": {"type": "string"}, "description": "Tenants Group IDs"}, "tenantsIds": {"type": "array", "items": {"type": "string"}, "description": "Tenants IDs"}}, "description": "WorkflowEnterpriseApply"}, "moego.models.automation.v1.WorkflowSetting": {"type": "object", "properties": {"type": {"enum": ["TYPE_UNSPECIFIED", "NOT_LIMIT", "TIME_FREQUENCY"], "type": "string", "description": "Setting type", "format": "enum"}, "timeFrequency": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.WorkflowSetting_TimeFrequency"}], "description": "Time frequency settings"}, "companyId": {"type": "string", "description": "Company ID"}}, "description": "WorkflowSetting"}, "moego.models.automation.v1.WorkflowSetting_TimeFrequency": {"type": "object", "properties": {"timeDuration": {"allOf": [{"$ref": "#/components/schemas/moego.models.automation.v1.TimeDuration"}], "description": "Time duration"}, "limit": {"type": "string", "description": "Frequency limit"}}, "description": "TimeFrequency"}, "moego.models.automation.v1.Workflow_ConsumerData": {"type": "object", "properties": {"effectClientNum": {"type": "integer", "description": "effect_client_num", "format": "int32"}, "costSmsTokenNum": {"type": "integer", "description": "cost_sms_token_num", "format": "int32"}}, "description": "ConsumerData"}, "moego.models.automation.v1.Workflow_PhotoGallery": {"type": "object", "properties": {"image": {"type": "string", "description": "image"}, "recommendImage": {"type": "string", "description": "recommend image"}}, "description": "PhotoGallery"}, "moego.models.business_customer.v1.BusinessCustomerInfoModel": {"type": "object", "properties": {"id": {"type": "string", "description": "customer id"}, "companyId": {"type": "string", "description": "company id"}, "preferredBusinessId": {"type": "string", "description": "preferred business id"}, "source": {"enum": ["SOURCE_UNSPECIFIED", "UNKNOWN", "SOURCE_ZERO", "SOURCE_ONE", "SOURCE_TWO", "SOURCE_THREE", "SOURCE_FOUR", "SOURCE_FIVE", "MANUAL_CREATE", "SELF_IMPORT", "DATA_IMPORT", "ONLINE_BOOKING", "INTAKE_FORM", "CALL_IN", "TEXT_IN", "BRANDED_APP"], "type": "string", "description": "source", "format": "enum"}, "avatarPath": {"type": "string", "description": "avatar path"}, "firstName": {"type": "string", "description": "first name"}, "lastName": {"type": "string", "description": "last name"}, "email": {"type": "string", "description": "email"}, "phoneNumber": {"type": "string", "description": "phone number this is the identifier for the customer, but it may not be the primary phone number"}, "clientColor": {"type": "string", "description": "client color"}, "customerCode": {"type": "string", "description": "customer code"}, "accountId": {"type": "string", "description": "account id, 0 means no binding account"}, "referralSourceId": {"type": "string", "description": "referral source id"}, "tagIds": {"type": "array", "items": {"type": "string"}, "description": "customer tag ids"}, "inactive": {"type": "boolean", "description": "inactive"}, "deleted": {"type": "boolean", "description": "if the customer is deleted"}}, "description": "info model for business customer 注意, 如果是业务相关的 preference 字段, 请优先考虑加到对应的 preference model 里, 主要是考虑到 preference model 可能在以后会收拢到对应的业务域中."}, "moego.models.enterprise.v1.AddressDef": {"type": "object", "properties": {"address1": {"type": "string", "description": "address 1"}, "address2": {"type": "string", "description": "address 2"}, "city": {"type": "string", "description": "city"}, "state": {"type": "string", "description": "state"}, "zipcode": {"type": "string", "description": "zip code"}, "country": {"type": "string", "description": "country"}, "coordinate": {"allOf": [{"$ref": "#/components/schemas/google.type.LatLng"}], "description": "latitude and longitude"}}, "description": "definition of address"}, "moego.models.enterprise.v1.CampaignTemplate": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "enterpriseId": {"type": "string", "description": "enterprise_id"}, "staffId": {"type": "string", "description": "staff_id"}, "name": {"type": "string", "description": "name"}, "type": {"enum": ["TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "description": "type", "format": "enum"}, "status": {"enum": ["STATUS_UNSPECIFIED", "DRAFT", "PUBLISHED"], "type": "string", "description": "status", "format": "enum"}, "internalTemplateId": {"type": "string", "description": "internal template id"}, "stat": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Campaign_Stat"}], "description": "stat"}, "description": {"type": "string", "description": "下面这几个都是外部模型的字段， enterprise 这边不存 description"}, "cover": {"type": "string", "description": "cover"}, "subject": {"type": "string", "description": "subject"}, "content": {"type": "string", "description": "content"}}, "description": "Campaign template"}, "moego.models.enterprise.v1.Campaign_Stat": {"type": "object", "properties": {"franchisee": {"type": "string", "description": "franchisee"}, "sent": {"type": "string", "description": "sent"}, "openRate": {"type": "string", "description": "open rate %"}, "clickRate": {"type": "string", "description": "click rate %"}, "bookings": {"type": "string", "description": "bookings"}, "revenue": {"allOf": [{"$ref": "#/components/schemas/google.type.Money"}], "description": "revenue"}}, "description": "stat"}, "moego.models.enterprise.v1.ConfigurationTemplate": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "type": {"enum": ["TYPE_UNSPECIFIED", "AGREEMENT", "INTAKE_FORM", "CLIENT_AND_PET_CLIENT_TAG", "CLIENT_AND_PET_CLIENT_REFERRAL_SOURCE", "CLIENT_AND_PET_CLIENT_GROOMING_FREQUENCY", "CLIENT_AND_PET_PET_CODE", "CLIENT_AND_PET_PET_TYPE_AND_BREED", "CLIENT_AND_PET_PET_SIZE", "CLIENT_AND_PET_PET_VACCINE", "CLIENT_AND_PET_PET_COAT_TYPE", "CLIENT_AND_PET_PET_FIXED", "CLIENT_AND_PET_PET_BEHAVIOR"], "type": "string", "description": "type", "format": "enum"}, "enterpriseId": {"type": "string", "description": "enterprise_id"}, "name": {"type": "string", "description": "name"}, "lastPublishedAt": {"type": "string", "description": "last_published_at", "format": "date-time"}, "impactedFranchisees": {"type": "string", "description": "impacted franchisees"}, "publishStatus": {"enum": ["STATUS_UNSPECIFIED", "UNPUBLISHED", "UP_TO_DATE", "OUTDATED"], "type": "string", "description": "publish status", "format": "enum"}}, "description": "configuration template"}, "moego.models.enterprise.v1.ConfigurationTemplatePublishRecord": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "templateId": {"type": "string", "description": "template_id"}, "enterpriseId": {"type": "string", "description": "enterprise_id"}, "publishedAt": {"type": "string", "description": "published_at", "format": "date-time"}, "impactedFranchiseesNum": {"type": "string", "description": "impacted franchisees"}, "publishResult": {"enum": ["UNSPECIFIED", "SUCCESS", "FAILED"], "type": "string", "description": "publish result", "format": "enum"}}, "description": "configuration template publish record"}, "moego.models.enterprise.v1.CountryDef": {"type": "object", "properties": {"name": {"type": "string", "description": "country"}, "code": {"type": "string", "description": "country code, ISO 3166-1 alpha-2"}}, "description": "Country"}, "moego.models.enterprise.v1.CreateEnterpriseDef": {"type": "object", "properties": {"name": {"type": "string", "description": "name"}, "address": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.AddressDef"}], "description": "address"}, "email": {"type": "string", "description": "email"}, "logoPath": {"type": "string", "description": "staff avatar path"}, "themeColor": {"type": "string", "description": "theme color"}, "currencyCode": {"type": "string", "description": "currency code"}, "currencySymbol": {"type": "string", "description": "currency symbol"}, "notificationSoundEnable": {"type": "boolean", "description": "whether the notification sound is on"}, "dateFormatType": {"enum": ["DATE_FORMAT_UNSPECIFIED", "MM_DD_YYYY_LINE", "DD_MM_YYYY_LINE", "DD_MM_YYYY_DOT", "YYYY_MM_DD_DOT", "YYYY_<PERSON>M_DD_LINE", "M<PERSON>_DD_YYYY_LINE"], "type": "string", "description": "date format", "format": "enum"}, "timeFormatType": {"enum": ["TIME_FORMAT_UNSPECIFIED", "HOUR_24", "HOUR_12"], "type": "string", "description": "time format", "format": "enum"}, "unitOfWeightType": {"enum": ["WEIGHT_UNIT_UNSPECIFIED", "POUND", "KILOGRAM"], "type": "string", "description": "unit of weight", "format": "enum"}, "unitOfDistanceType": {"enum": ["DISTANCE_UNIT_UNSPECIFIED", "MILE", "KILOMETER"], "type": "string", "description": "unit of distance", "format": "enum"}, "timeZone": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TimeZone"}], "description": "timezone"}, "source": {"enum": ["NORMALLY_ADD", "MANUALLY_ADD", "SPLIT_COMPANY", "DEMO_ENTERPRISE"], "type": "string", "description": "source", "format": "enum"}}, "description": "create enterprise def"}, "moego.models.enterprise.v1.CreateStaffProfile": {"type": "object", "properties": {"avatarPath": {"type": "string", "description": "avator path"}, "firstName": {"type": "string", "description": "first name"}, "lastName": {"type": "string", "description": "last name"}, "roleId": {"type": "string", "description": "role_id"}, "hireTime": {"type": "string", "description": "hired time", "format": "date-time"}, "colorCode": {"type": "string", "description": "color code"}, "note": {"type": "string", "description": "note"}, "profileEmail": {"type": "string", "description": "email"}}, "description": "staff profile"}, "moego.models.enterprise.v1.CreateTenantDef": {"type": "object", "properties": {"name": {"type": "string", "description": "name"}, "tenantInfo": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CreateTenantInfoDef"}], "description": "own type"}, "colorCode": {"type": "string", "description": "color code"}, "joinedDate": {"type": "string", "description": "joined time", "format": "date-time"}, "territoryId": {"type": "string", "description": "territory id"}, "assignedUnit": {"type": "integer", "description": "assigned uint", "format": "int32"}, "assignedVan": {"type": "integer", "description": "assigned van", "format": "int32"}, "tenantTemplateId": {"type": "string", "description": "tenant template id"}, "state": {"type": "string", "description": "state"}, "city": {"type": "string", "description": "city"}}, "description": "create tenant def"}, "moego.models.enterprise.v1.CreateTenantInfoDef": {"type": "object", "properties": {"ownType": {"enum": ["OWN_TYPE_UNSPECIFIED", "CUSTOMIZATION", "ENTERPRISE"], "type": "string", "description": "own type", "format": "enum"}, "ownFirstName": {"type": "string", "description": "own first name"}, "ownLastName": {"type": "string", "description": "own last name"}, "email": {"type": "string", "description": "account email"}, "phoneNumber": {"type": "string", "description": "phone number"}, "methodType": {"enum": ["INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "description": "color code", "format": "enum"}, "isSendInviteLink": {"type": "boolean", "description": "whether to send invite link now or not"}}, "description": "create TenantInfoDef"}, "moego.models.enterprise.v1.CreateTenantTemplateDef": {"type": "object", "properties": {"name": {"type": "string", "description": "tenant template name"}, "companyId": {"type": "string", "description": "type company"}}, "description": "create tenant template def"}, "moego.models.enterprise.v1.CurrencyDef": {"type": "object", "properties": {"currencyCode": {"type": "string", "description": "currency"}, "currencySymbol": {"type": "string", "description": "symbol"}}, "description": "label type def list"}, "moego.models.enterprise.v1.EnterpriseModel": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "name": {"type": "string", "description": "name"}, "accountId": {"type": "string", "description": "account_id"}, "email": {"type": "string", "description": "email"}, "themeColor": {"type": "string", "description": "theme color"}, "currencyCode": {"type": "string", "description": "currency code"}, "currencySymbol": {"type": "string", "description": "currency symbol"}, "logoPath": {"type": "string", "description": "logo path"}, "dateFormatType": {"enum": ["DATE_FORMAT_UNSPECIFIED", "MM_DD_YYYY_LINE", "DD_MM_YYYY_LINE", "DD_MM_YYYY_DOT", "YYYY_MM_DD_DOT", "YYYY_<PERSON>M_DD_LINE", "M<PERSON>_DD_YYYY_LINE"], "type": "string", "description": "date format", "format": "enum"}, "timeFormatType": {"enum": ["TIME_FORMAT_UNSPECIFIED", "HOUR_24", "HOUR_12"], "type": "string", "description": "time format", "format": "enum"}, "unitOfWeightType": {"enum": ["WEIGHT_UNIT_UNSPECIFIED", "POUND", "KILOGRAM"], "type": "string", "description": "unit of weight", "format": "enum"}, "unitOfDistanceType": {"enum": ["DISTANCE_UNIT_UNSPECIFIED", "MILE", "KILOMETER"], "type": "string", "description": "unit of distance", "format": "enum"}, "notificationSoundEnable": {"type": "boolean", "description": "whether the notification sound is on"}, "country": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.CountryDef"}], "description": "country"}, "timeZone": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TimeZone"}], "description": "timezone"}, "address": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.AddressDef"}], "description": "address"}, "source": {"enum": ["NORMALLY_ADD", "MANUALLY_ADD", "SPLIT_COMPANY", "DEMO_ENTERPRISE"], "type": "string", "description": "source", "format": "enum"}}, "description": "EnterpriseModel"}, "moego.models.enterprise.v1.GroupDef": {"type": "object", "properties": {"newGroupNames": {"type": "array", "items": {"type": "string"}, "description": "group name"}, "bindingGroupIds": {"type": "array", "items": {"type": "string"}, "description": "group id"}}, "description": "group def"}, "moego.models.enterprise.v1.GroupTenantDef": {"type": "object", "properties": {"groupId": {"type": "string", "description": "tenant id"}, "groupName": {"type": "string", "description": "group name"}, "tenantIds": {"type": "array", "items": {"type": "string"}, "description": "tenant ids"}}, "description": "group tenant map"}, "moego.models.enterprise.v1.LabelTypeDef": {"type": "object", "properties": {"type": {"type": "integer", "description": "type", "format": "int32"}, "label": {"type": "string", "description": "label"}}, "description": "label type def"}, "moego.models.enterprise.v1.Limitation_PetTypeBreeds": {"type": "object", "properties": {"allBreeds": {"type": "boolean", "description": "all breeds"}, "petTypeId": {"enum": ["PET_TYPE_UNSPECIFIED", "PET_TYPE_DOG", "PET_TYPE_CAT", "PET_TYPE_BIRD", "PET_TYPE_RABBIT", "PET_TYPE_GUINEA_PIG", "PET_TYPE_HORSE", "PET_TYPE_RAT", "PET_TYPE_MOUSE", "PET_TYPE_HAMSTER", "PET_TYPE_CHINCHILLA", "PET_TYPE_OTHER"], "type": "string", "description": "pet type id", "format": "enum"}, "petTypeName": {"type": "string", "description": "pet type name"}, "petBreeds": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.PetBreed"}, "description": "required breeds"}}, "description": "pet type breeds"}, "moego.models.enterprise.v1.OptionModel": {"type": "object", "properties": {"calendarFormats": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.LabelTypeDef"}, "description": "List of calendar formats"}, "currencies": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.CurrencyDef"}, "description": "List of currencies"}, "countries": {"type": "array", "items": {"type": "string"}, "description": "Array of country names"}, "dateFormats": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.LabelTypeDef"}, "description": "List of date formats"}, "numberFormats": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.LabelTypeDef"}, "description": "List of number formats"}, "timeFormats": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.LabelTypeDef"}, "description": "List of time formats"}, "unitsOfWeight": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.LabelTypeDef"}, "description": "List of units of weight"}, "unitsOfDistance": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.LabelTypeDef"}, "description": "List of units of distance"}}, "description": "OptionModel is a model for options"}, "moego.models.enterprise.v1.PetBreed": {"type": "object", "properties": {"petTypeId": {"enum": ["PET_TYPE_UNSPECIFIED", "PET_TYPE_DOG", "PET_TYPE_CAT", "PET_TYPE_BIRD", "PET_TYPE_RABBIT", "PET_TYPE_GUINEA_PIG", "PET_TYPE_HORSE", "PET_TYPE_RAT", "PET_TYPE_MOUSE", "PET_TYPE_HAMSTER", "PET_TYPE_CHINCHILLA", "PET_TYPE_OTHER"], "type": "string", "description": "pet type id", "format": "enum"}, "petTypeName": {"type": "string", "description": "pet type name"}, "name": {"type": "string", "description": "pet breed name"}}, "description": "pet breed"}, "moego.models.enterprise.v1.PetType": {"type": "object", "properties": {"id": {"type": "string", "description": "id, primary key of pet type record in database"}, "petTypeId": {"enum": ["PET_TYPE_UNSPECIFIED", "PET_TYPE_DOG", "PET_TYPE_CAT", "PET_TYPE_BIRD", "PET_TYPE_RABBIT", "PET_TYPE_GUINEA_PIG", "PET_TYPE_HORSE", "PET_TYPE_RAT", "PET_TYPE_MOUSE", "PET_TYPE_HAMSTER", "PET_TYPE_CHINCHILLA", "PET_TYPE_OTHER"], "type": "string", "description": "pet type id", "format": "enum"}, "petTypeName": {"type": "string", "description": "pet type name, e.g. Dog, Cat, etc."}, "isAvailable": {"type": "boolean", "description": "if the pet type is available"}, "sort": {"type": "integer", "description": "pet type sort. The larger the sort number, the higher the priority.", "format": "int32"}}, "description": "pet type model Pet type can not be deleted, only can be set to unavailable."}, "moego.models.enterprise.v1.PriceBook": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "name": {"type": "string", "description": "name"}}, "description": "price book"}, "moego.models.enterprise.v1.ResourceDef": {"type": "object", "properties": {"isAll": {"type": "boolean", "description": "is all"}, "tenantIds": {"type": "array", "items": {"type": "string"}, "description": "tenant ids"}, "groupIds": {"type": "array", "items": {"type": "string"}, "description": "group ids"}}, "description": "resource def"}, "moego.models.enterprise.v1.RoleModel": {"type": "object", "properties": {"id": {"type": "string", "description": "role id"}, "name": {"type": "string", "description": "role name"}, "description": {"type": "string", "description": "role description"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "type": {"enum": ["ROLE_TYPE_UNSPECIFIED", "ROLE_TYPE_COMPANY_STAFF", "ROLE_TYPE_COMPANY_OWNER", "ROLE_TYPE_ENTERPRISE_STAFF", "ROLE_TYPE_ENTERPRISE_OWNER", "ROLE_TYPE_TENANT_OWNER"], "type": "string", "description": "role type", "format": "enum"}}, "description": "model for role"}, "moego.models.enterprise.v1.Service": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "priceBook": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.PriceBook"}], "description": "price book"}, "name": {"type": "string", "description": "name"}, "serviceItemType": {"enum": ["SERVICE_ITEM_TYPE_UNSPECIFIED", "GROOMING", "BOARDING", "DAYCARE", "EVALUATION", "DOG_WALKING", "GROUP_CLASS"], "type": "string", "description": "service item type", "format": "enum"}, "category": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceCategory"}], "description": "category"}, "description": {"type": "string", "description": "description"}, "inactive": {"type": "boolean", "description": "inactive"}, "color": {"type": "string", "description": "color"}, "sort": {"type": "string", "description": "sort"}, "price": {"allOf": [{"$ref": "#/components/schemas/google.type.Money"}], "description": "price"}, "servicePriceUnit": {"enum": ["SERVICE_PRICE_UNIT_UNSPECIFIED", "PER_SESSION", "PER_NIGHT", "PER_HOUR", "PER_DAY"], "type": "string", "description": "service price unit", "format": "enum"}, "taxRate": {"type": "string", "description": "万分位税率"}, "duration": {"allOf": [{"$ref": "#/components/schemas/google.protobuf.Duration"}], "description": "duration"}, "maxDuration": {"allOf": [{"$ref": "#/components/schemas/google.protobuf.Duration"}], "description": "max duration"}, "limitation": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Service_Limitation"}], "description": "limitation"}, "serviceType": {"enum": ["SERVICE_TYPE_UNSPECIFIED", "SERVICE", "ADDON"], "type": "string", "description": "service type", "format": "enum"}, "images": {"type": "array", "items": {"type": "string"}, "description": "images"}}, "description": "service model 原有 service 模型是企业级设计，这里先简化"}, "moego.models.enterprise.v1.ServiceCategory": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "priceBook": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.PriceBook"}], "description": "price book"}, "name": {"type": "string", "description": "name"}, "serviceItemType": {"enum": ["SERVICE_ITEM_TYPE_UNSPECIFIED", "GROOMING", "BOARDING", "DAYCARE", "EVALUATION", "DOG_WALKING", "GROUP_CLASS"], "type": "string", "description": "service item type", "format": "enum"}, "sort": {"type": "string", "description": "sort"}, "serviceType": {"enum": ["SERVICE_TYPE_UNSPECIFIED", "SERVICE", "ADDON"], "type": "string", "description": "service type", "format": "enum"}}, "description": "category"}, "moego.models.enterprise.v1.ServiceChange": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "historyId": {"type": "string", "description": "history id"}, "target": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantObject"}], "description": "target"}, "serviceId": {"type": "string", "description": "service id"}, "serviceName": {"type": "string", "description": "service name"}, "detailCategories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceChange_DetailCategory"}, "description": "details"}}, "description": "service change"}, "moego.models.enterprise.v1.ServiceChangeHistory": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "serviceId": {"type": "string", "description": "service id"}, "impactedTenants": {"type": "string", "description": "impacted tenants"}, "rollOutAt": {"type": "string", "description": "roll out at", "format": "date-time"}, "updatedAt": {"type": "string", "description": "updated at", "format": "date-time"}, "impactedTenantIds": {"type": "array", "items": {"type": "string"}, "description": "impacted tenant ids"}}, "description": "service change history"}, "moego.models.enterprise.v1.ServiceChange_Detail": {"type": "object", "properties": {"fieldName": {"type": "string", "description": "field name"}, "type": {"enum": ["TYPE_UNSPECIFIED", "MONEY", "TEXT", "DATE", "TIME", "NUMBER", "CLIENT_NAME", "INVOICE_ID", "BOOKING_ID", "AVATAR_URL", "DECIMAL_NUMBER", "PERCENTAGE", "DURATION", "DATETIME", "PET_INCIDENT_ID", "COLOR", "CLIENT_ID"], "type": "string", "description": "field type", "format": "enum"}, "old": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.Value"}], "description": "old value"}, "new": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.Value"}], "description": "new value"}, "isChanged": {"type": "boolean", "description": "is changed"}}, "description": "detail"}, "moego.models.enterprise.v1.ServiceChange_DetailCategory": {"type": "object", "properties": {"name": {"type": "string", "description": "name"}, "details": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.ServiceChange_Detail"}, "description": "details"}}, "description": "detail category"}, "moego.models.enterprise.v1.Service_Limitation": {"type": "object", "properties": {"allPetTypesAndBreeds": {"type": "boolean", "description": "all pet types and breeds"}, "petTypeBreeds": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.enterprise.v1.Limitation_PetTypeBreeds"}, "description": "required pet types"}, "allPetSizes": {"type": "boolean", "description": "all pet sizes"}, "petWeightRange": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.WeightRange"}], "description": "pet weight range"}, "allCoatTypes": {"type": "boolean", "description": "all coat types"}, "requiredCoatTypes": {"type": "array", "items": {"type": "string"}, "description": "required coat types"}}, "description": "limitation"}, "moego.models.enterprise.v1.StaffModel": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "firstName": {"type": "string", "description": "first name"}, "lastName": {"type": "string", "description": "last name"}, "roleId": {"type": "string", "description": "role_id"}, "colorCode": {"type": "string", "description": "color code"}, "avatarPath": {"type": "string", "description": "avatar path"}, "profileEmail": {"type": "string", "description": "profile email"}, "employeeCategory": {"enum": ["COMPANY_STAFF", "COMPANY_OWNER", "ENTERPRISE_OWNER", "ENTERPRISE_STAFF"], "type": "string", "description": "employee category", "format": "enum"}, "hireTime": {"type": "string", "description": "hired at", "format": "date-time"}, "updateTime": {"type": "string", "description": "update at", "format": "date-time"}, "inviteCode": {"type": "string", "description": "invite code"}, "note": {"type": "string", "description": "note"}, "source": {"enum": ["STAFF_SOURCE_UNSPECIFIED", "STAFF_SOURCE_SYSTEM"], "type": "string", "description": "source", "format": "enum"}}, "description": "StaffModel"}, "moego.models.enterprise.v1.TenantGroupDef": {"type": "object", "properties": {"tenantId": {"type": "string", "description": "tenant id"}, "groupIds": {"type": "array", "items": {"type": "string"}, "description": "group ids"}}, "description": "tenant group map"}, "moego.models.enterprise.v1.TenantGroupModel": {"type": "object", "properties": {"id": {"type": "string", "description": "tenant group id"}, "name": {"type": "string", "description": "tenant name"}, "status": {"enum": ["TENANT_GROUP_STATUS_UNSPECIFIED", "NORMAL", "DELETE"], "type": "string", "description": "status", "format": "enum"}}, "description": "tenant group model"}, "moego.models.enterprise.v1.TenantModel": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "relatedCompanyId": {"type": "string", "description": "company id"}, "vanNum": {"type": "integer", "description": "van num", "format": "int32"}, "locationNum": {"type": "integer", "description": "location num", "format": "int32"}, "territoryId": {"type": "string", "description": "territory id"}, "templateId": {"type": "string", "description": "template id"}, "name": {"type": "string", "description": "name"}, "colorCode": {"type": "string", "description": "color code"}, "status": {"enum": ["TENANT_STATUS_UNSPECIFIED", "INSTANTIATED", "LIVE", "DELETED"], "type": "string", "description": "status", "format": "enum"}, "ownType": {"enum": ["OWN_TYPE_UNSPECIFIED", "CUSTOMIZATION", "ENTERPRISE"], "type": "string", "description": "own type", "format": "enum"}, "joinedTime": {"type": "string", "description": "joined time", "format": "date-time"}, "state": {"type": "string", "description": "state"}, "city": {"type": "string", "description": "city"}, "type": {"enum": ["TYPE_UNSPECIFIED", "NORMAL", "TRAINING", "TEMPLATE"], "type": "string", "description": "type", "format": "enum"}}, "description": "tenant"}, "moego.models.enterprise.v1.TenantObject": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "type": {"enum": ["TYPE_UNSPECIFIED", "ALL", "TENANT", "TENANT_GROUP"], "type": "string", "description": "type", "format": "enum"}, "name": {"type": "string", "description": "name"}}, "description": "TenantObject 作为 Enterprise Hub 中对 Tenant 和 Tenant Group 的统一抽象"}, "moego.models.enterprise.v1.TenantTemplateModel": {"type": "object", "properties": {"id": {"type": "string", "description": "tenant template id"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "name": {"type": "string", "description": "tenant template name"}, "status": {"enum": ["TENANT_TEMPLATE_STATUS_UNSPECIFIED", "NORMAL", "DELETE"], "type": "string", "description": "tenant template status", "format": "enum"}, "type": {"enum": ["TENANT_TEMPLATE_TYPE_UNSPECIFIED", "COMPANY", "CONFIG"], "type": "string", "description": "tenant template type", "format": "enum"}, "config": {"type": "string", "description": "tenant template config"}, "minVanNum": {"type": "string", "description": "min van num"}, "minLocationNum": {"type": "string", "description": "min location num"}, "companyTypeInfo": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TenantTemplateModel_CompanyTypeInfoModel"}], "description": "company type info"}}, "description": "tenant model"}, "moego.models.enterprise.v1.TenantTemplateModel_CompanyTypeInfoModel": {"type": "object", "properties": {"id": {"type": "string", "description": "template company id"}, "type": {"enum": ["MOBILE", "SALON", "HYBRID"], "type": "string", "description": "company type", "format": "enum"}}, "description": "type for company struct"}, "moego.models.enterprise.v1.TenantView": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "name": {"type": "string", "description": "name"}}, "description": "tenant view"}, "moego.models.enterprise.v1.TerritoryModel": {"type": "object", "properties": {"id": {"type": "string", "description": "territory id"}, "name": {"type": "string", "description": "enterprise id"}, "status": {"enum": ["TERRITORY_STATUS_UNSPECIFIED", "NORMAL", "DELETE"], "type": "string", "description": "territory status", "format": "enum"}, "type": {"enum": ["TERRITORY_TYPE_UNSPECIFIED", "ZIP_CODE", "DRAW"], "type": "string", "description": "territory type", "format": "enum"}, "colorCode": {"type": "string", "description": "color code"}, "zipCodes": {"type": "array", "items": {"type": "string"}, "description": "zip codes"}}, "description": "territory model"}, "moego.models.enterprise.v1.TimeZone": {"type": "object", "properties": {"name": {"type": "string", "description": "time zone name"}, "seconds": {"type": "integer", "description": "time zone offset", "format": "int32"}}, "description": "time zone"}, "moego.models.enterprise.v1.UpdateEnterpriseDef": {"type": "object", "properties": {"name": {"type": "string", "description": "name"}, "address": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.AddressDef"}], "description": "address"}, "email": {"type": "string", "description": "email"}, "logoPath": {"type": "string", "description": "staff avatar path"}, "themeColor": {"type": "string", "description": "theme color"}, "currencyCode": {"type": "string", "description": "currency code"}, "currencySymbol": {"type": "string", "description": "currency symbol"}, "notificationSoundEnable": {"type": "boolean", "description": "whether the notification sound is on"}, "dateFormatType": {"enum": ["DATE_FORMAT_UNSPECIFIED", "MM_DD_YYYY_LINE", "DD_MM_YYYY_LINE", "DD_MM_YYYY_DOT", "YYYY_MM_DD_DOT", "YYYY_<PERSON>M_DD_LINE", "M<PERSON>_DD_YYYY_LINE"], "type": "string", "description": "date format", "format": "enum"}, "timeFormatType": {"enum": ["TIME_FORMAT_UNSPECIFIED", "HOUR_24", "HOUR_12"], "type": "string", "description": "time format", "format": "enum"}, "unitOfWeightType": {"enum": ["WEIGHT_UNIT_UNSPECIFIED", "POUND", "KILOGRAM"], "type": "string", "description": "unit of weight", "format": "enum"}, "unitOfDistanceType": {"enum": ["DISTANCE_UNIT_UNSPECIFIED", "MILE", "KILOMETER"], "type": "string", "description": "unit of distance", "format": "enum"}, "timeZone": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.TimeZone"}], "description": "timezone"}}, "description": "UpdateEnterpriseDef"}, "moego.models.enterprise.v1.UpdateStaffProfile": {"type": "object", "properties": {"avatarPath": {"type": "string", "description": "avator path"}, "firstName": {"type": "string", "description": "first name"}, "lastName": {"type": "string", "description": "last name"}, "roleId": {"type": "string", "description": "role_id"}, "hireTime": {"type": "string", "description": "hired time", "format": "date-time"}, "colorCode": {"type": "string", "description": "color code"}, "note": {"type": "string", "description": "note"}, "profileEmail": {"type": "string", "description": "email"}}, "description": "update staff profile"}, "moego.models.enterprise.v1.UpdateTenantDef": {"type": "object", "properties": {"name": {"type": "string", "description": "name"}, "tenantInfo": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.UpdateTenantInfoDef"}], "description": "update tenant info def"}, "colorCode": {"type": "string", "description": "color code"}, "joinedDate": {"type": "string", "description": "joined time", "format": "date-time"}, "territoryId": {"type": "string", "description": "territory id"}, "assignedUnit": {"type": "integer", "description": "assigned uint", "format": "int32"}, "assignedVan": {"type": "integer", "description": "assigned van", "format": "int32"}, "tenantTemplateId": {"type": "string", "description": "tenant template id"}, "state": {"type": "string", "description": "state"}, "city": {"type": "string", "description": "city"}}, "description": "update tenant def"}, "moego.models.enterprise.v1.UpdateTenantInfoDef": {"type": "object", "properties": {"ownType": {"enum": ["OWN_TYPE_UNSPECIFIED", "CUSTOMIZATION", "ENTERPRISE"], "type": "string", "description": "own type", "format": "enum"}, "ownFirstName": {"type": "string", "description": "own first name"}, "ownLastName": {"type": "string", "description": "own last name"}, "email": {"type": "string", "description": "account email"}, "phoneNumber": {"type": "string", "description": "phone number"}, "methodType": {"enum": ["INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "description": "color code", "format": "enum"}, "isSendInviteLink": {"type": "boolean", "description": "whether to send invite link now or not"}}, "description": "update TenantInfoDef"}, "moego.models.enterprise.v1.UpdateTenantTemplateDef": {"type": "object", "properties": {"id": {"type": "string", "description": "tenant template id"}, "name": {"type": "string", "description": "tenant template name"}}, "description": "update tenant template def"}, "moego.models.enterprise.v1.Weight": {"type": "object", "properties": {"weight": {"type": "string", "description": "weight"}, "unit": {"enum": ["WEIGHT_UNIT_UNSPECIFIED", "POUND", "KILOGRAM"], "type": "string", "description": "unit", "format": "enum"}}, "description": "weight"}, "moego.models.enterprise.v1.WeightRange": {"type": "object", "properties": {"min": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Weight"}], "description": "min weight"}, "max": {"allOf": [{"$ref": "#/components/schemas/moego.models.enterprise.v1.Weight"}], "description": "max weight"}}, "description": "weight range"}, "moego.models.file.v2.FileModel": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "creatorId": {"type": "string", "description": "account id for the creator"}, "companyId": {"type": "string", "description": "company_id"}, "businessId": {"type": "string", "description": "business_id"}, "usage": {"type": "string", "description": "usage for the file, must be one of FileUsage"}, "fileName": {"type": "string", "description": "source file name(with extension)"}, "fileKey": {"type": "string", "description": "s3 file key"}, "fileSizeByte": {"type": "string", "description": "file size byte"}, "ownerType": {"type": "string", "description": "owner type,eg:staff,pet..."}, "ownerId": {"type": "string", "description": "owner id"}, "status": {"enum": ["FILE_STATUS_UNSPECIFIED", "FILE_STATUS_UPLOADING", "FILE_STATUS_UPLOADED", "FILE_STATUS_ERROR", "FILE_STATUS_DELETE"], "type": "string", "description": "file status", "format": "enum"}, "accessUrl": {"type": "string", "description": "access url for download"}, "bucket": {"type": "string", "description": "bucket"}, "createdAt": {"type": "string", "description": "create time", "format": "date-time"}, "updatedAt": {"type": "string", "description": "update time", "format": "date-time"}, "deletedAt": {"type": "string", "description": "delete time", "format": "date-time"}, "fileExt": {"type": "string", "description": "file extension"}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}, "description": "file metadata"}}, "description": "file model"}, "moego.models.grooming.v1.ZipCodeModel": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the place"}, "zipCode": {"type": "string", "description": "Zip code of the place"}, "placeName": {"type": "string", "description": "Name of the place"}, "state": {"type": "string", "description": "State where the place is located"}, "stateAbbreviation": {"type": "string", "description": "State abbreviation"}, "county": {"type": "string", "description": "County where the place is located"}, "countryName": {"type": "string", "description": "Country name (if applicable)"}, "latitude": {"type": "string", "description": "Latitude of the place"}, "longitude": {"type": "string", "description": "Longitude of the place"}, "placeId": {"type": "string", "description": "Unique identifier for the place (e.g. Google Places ID)"}, "status": {"type": "boolean", "description": "Status of the place (true/false)"}, "updatedAt": {"type": "string", "description": "Timestamp when the place information was last updated", "format": "date-time"}, "createdAt": {"type": "string", "description": "Timestamp when the place information was created", "format": "date-time"}}, "description": "zip code model"}, "moego.models.map.v1.AuthorAttribution": {"type": "object", "properties": {"displayName": {"type": "string", "description": "Name of the author of the [Photo][google.maps.places.v1.Photo] or [Review][google.maps.places.v1.Review]."}, "uri": {"type": "string", "description": "URI of the author of the [Photo][google.maps.places.v1.Photo] or [Review][google.maps.places.v1.Review]."}, "photoUri": {"type": "string", "description": "Profile photo URI of the author of the [Photo][google.maps.places.v1.Photo] or [Review][google.maps.places.v1.Review]."}}, "description": "Information about the author of the UGC data. Used in [Photo][google.maps.places.v1.Photo], and [Review][google.maps.places.v1.Review]."}, "moego.models.map.v1.GooglePlace": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier of a place."}, "name": {"type": "string", "description": "This Place's resource name, in `places/{place_id}` format.  Can be used to look up the Place."}, "types": {"type": "array", "items": {"type": "string"}, "description": "A set of type tags for this result. For example, \"political\" and \"locality\". For the complete list of possible values, see Table A and Table B at https://developers.google.com/maps/documentation/places/web-service/place-types"}, "formattedAddress": {"type": "string", "description": "A full, human-readable address for this place."}, "shortFormattedAddress": {"type": "string", "description": "A short, human-readable address for this place."}, "addressComponents": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.map.v1.GooglePlace_AddressComponent"}, "description": "Repeated components for each locality level. Note the following facts about the address_components[] array: - The array of address components may contain more components than the formatted_address. - The array does not necessarily include all the political entities that contain an address, apart from those included in the formatted_address. To retrieve all the political entities that contain a specific address, you should use reverse geocoding, passing the latitude/longitude of the address as a parameter to the request. - The format of the response is not guaranteed to remain the same between requests. In particular, the number of address_components varies based on the address requested and can change over time for the same address. A component can change position in the array. The type of the component can change. A particular component may be missing in a later response."}, "plusCode": {"allOf": [{"$ref": "#/components/schemas/moego.models.map.v1.GooglePlace_PlusCode"}], "description": "Plus code of the place location lat/long."}, "location": {"allOf": [{"$ref": "#/components/schemas/google.type.LatLng"}], "description": "The position of this place."}, "viewport": {"allOf": [{"$ref": "#/components/schemas/google.geo.type.Viewport"}], "description": "A viewport suitable for displaying the place on an average-sized map."}, "photos": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.map.v1.Photo"}, "description": "Information (including references) about photos of this place. A maximum of 10 photos can be returned."}, "adrFormatAddress": {"type": "string", "description": "The place's address in adr microformat: http://microformats.org/wiki/adr."}, "attributions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.map.v1.GooglePlace_Attribution"}, "description": "A set of data provider that must be shown with this result."}}, "description": "GooglePlace https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places#resource:-place"}, "moego.models.map.v1.GooglePlace_AddressComponent": {"type": "object", "properties": {"longText": {"type": "string", "description": "The full text description or name of the address component. For example, an address component for the country Australia may have a long_name of \"Australia\"."}, "shortText": {"type": "string", "description": "An abbreviated textual name for the address component, if available. For example, an address component for the country of Australia may have a short_name of \"AU\"."}, "types": {"type": "array", "items": {"type": "string"}, "description": "An array indicating the type(s) of the address component."}, "languageCode": {"type": "string", "description": "The language used to format this components, in CLDR notation."}}, "description": "The structured components that form the formatted address, if this information is available."}, "moego.models.map.v1.GooglePlace_Attribution": {"type": "object", "properties": {"provider": {"type": "string", "description": "Name of the Place's data provider."}, "providerUri": {"type": "string", "description": "URI to the Place's data provider."}}, "description": "Information about data providers of this place."}, "moego.models.map.v1.GooglePlace_PlusCode": {"type": "object", "properties": {"globalCode": {"type": "string", "description": "Place's global (full) code, such as \"9FWM33GV+HQ\", representing an 1/8000 by 1/8000 degree area (~14 by 14 meters)."}, "compoundCode": {"type": "string", "description": "Place's compound code, such as \"33GV+HQ, Ramberg, Norway\", containing the suffix of the global code and replacing the prefix with a formatted name of a reference entity."}}, "description": "Plus code (http://plus.codes) is a location reference with two formats: global code defining a 14mx14m (1/8000th of a degree) or smaller rectangle, and compound code, replacing the prefix with a reference location."}, "moego.models.map.v1.Photo": {"type": "object", "properties": {"name": {"type": "string", "description": "Identifier. A reference representing this place photo which may be used to look up this place photo again (also called the API \"resource\" name: `places/{place_id}/photos/{photo}`)."}, "widthPx": {"type": "integer", "description": "The maximum available width, in pixels.", "format": "int32"}, "heightPx": {"type": "integer", "description": "The maximum available height, in pixels.", "format": "int32"}, "authorAttributions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.map.v1.AuthorAttribution"}, "description": "This photo's authors."}}, "description": "Information about a photo of a place."}, "moego.models.message.v1.MessageTemplatePlaceholderSimpleView": {"type": "object", "properties": {"placeholderGroup": {"type": "string", "description": "placeholder group"}, "placeholderName": {"type": "string", "description": "placeholder name"}, "placeholderText": {"type": "string", "description": "placeholder content"}, "exampleText": {"type": "string", "description": "example text"}, "resultText": {"type": "string", "description": "result text"}}, "description": "message template placeholder simple view"}, "moego.models.organization.v1.AddressDef": {"type": "object", "properties": {"address1": {"type": "string", "description": "address 1"}, "address2": {"type": "string", "description": "address 2"}, "city": {"type": "string", "description": "city"}, "state": {"type": "string", "description": "state"}, "zipcode": {"type": "string", "description": "zip code"}, "country": {"type": "string", "description": "country"}, "coordinate": {"allOf": [{"$ref": "#/components/schemas/google.type.LatLng"}], "description": "latitude and longitude"}}, "description": "definition of address"}, "moego.models.organization.v1.LocationBriefView": {"type": "object", "properties": {"id": {"type": "string", "description": "location id"}, "name": {"type": "string", "description": "location name"}, "businessType": {"enum": ["MOBILE", "SALON", "HYBRID"], "type": "string", "description": "business type", "format": "enum"}, "address": {"allOf": [{"$ref": "#/components/schemas/moego.models.organization.v1.AddressDef"}], "description": "address(only for salon)"}, "numberOfVansUsed": {"type": "integer", "description": "number of vans(only for mobile)", "format": "int32"}, "avatarPath": {"type": "string", "description": "avatar path"}, "isWorkingLocation": {"type": "boolean", "description": "whether the location is the staff's working location"}, "twilioPhoneNumber": {"type": "string", "description": "assigned twilio phone number"}}, "description": "view for location list"}, "moego.models.organization.v1.SendInviteLinkDef": {"type": "object", "properties": {"inviteCode": {"type": "string", "description": "invite code"}, "email": {"type": "string", "description": "email"}, "methodType": {"enum": ["STAFF_INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "description": "method type", "format": "enum"}}, "description": "send invite link def"}, "moego.models.organization.v1.SendInviteLinkParamsDef": {"type": "object", "properties": {"email": {"type": "string", "description": "email"}, "methodType": {"enum": ["STAFF_INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "description": "send invite link method type", "format": "enum"}, "isSendInviteLink": {"type": "boolean", "description": "whether to send invite link now or not"}}, "description": "send staff invite link params"}, "moego.models.organization.v1.StaffEmailDef": {"type": "object", "properties": {"email": {"type": "string", "description": "staff profile email"}, "linkStatus": {"enum": ["STAFF_LINK_STATUS_UNSPECIFIED", "UNLINKED", "PENDING", "LINKED"], "type": "string", "description": "staff link status", "format": "enum"}}, "description": "staff email definition"}, "moego.models.organization.v1.StaffModel": {"type": "object", "properties": {"id": {"type": "string", "description": "staff id"}, "businessId": {"type": "string", "description": "business id"}, "accountId": {"type": "string", "description": "account id"}, "avatarPath": {"type": "string", "description": "avatar path"}, "isDeleted": {"type": "boolean", "description": "is deleted"}, "isBookOnlineAvailable": {"type": "boolean", "description": "is available for book online"}, "isShowOnCalendar": {"type": "boolean", "description": "is show on calendar"}, "firstName": {"type": "string", "description": "first name"}, "lastName": {"type": "string", "description": "last name"}, "companyId": {"type": "string", "description": "company id"}, "employeeCategory": {"enum": ["COMPANY_STAFF", "COMPANY_OWNER", "ENTERPRISE_OWNER", "ENTERPRISE_STAFF"], "type": "string", "description": "employee category", "format": "enum"}, "enterpriseId": {"type": "string", "description": "enterprise id"}, "roleId": {"type": "string", "description": "role id"}, "hireDate": {"type": "string", "description": "hire date"}, "note": {"type": "string", "description": "note"}, "colorCode": {"type": "string", "description": "color code"}, "lastVisitBusinessId": {"type": "string", "description": "last visit business id"}, "inviteCode": {"type": "string", "description": "invite code"}, "accessAllWorkingLocationsStaffs": {"type": "boolean", "description": "access all working locations staffs"}, "profileEmail": {"type": "string", "description": "profile email"}, "sort": {"type": "integer", "description": "sort", "format": "int32"}, "accessCode": {"type": "string", "description": "access code"}, "requireAccessCode": {"type": "boolean", "description": "require access code"}, "status": {"enum": ["STAFF_STATUS_UNSPECIFIED", "NORMAL", "DELETED", "MIGRATED", "TEMPORARY"], "type": "string", "description": "status", "format": "enum"}, "updateTime": {"type": "string", "description": "update time", "format": "date-time"}, "source": {"enum": ["STAFF_SOURCE_UNSPECIFIED", "STAFF_SOURCE_SYSTEM"], "type": "string", "description": "staff source", "format": "enum"}, "workingLocationList": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.organization.v1.LocationBriefView"}, "description": "preserved num before 50 for future use working locations"}, "phoneNumber": {"type": "string", "description": "phone number"}}, "description": "staff model"}, "moego.models.payment.v1.Card": {"type": "object", "properties": {"brand": {"type": "string", "description": "brand of the credit card, visa"}, "expMonth": {"type": "integer", "description": "expiration month of the credit card", "format": "int32"}, "expYear": {"type": "integer", "description": "expiration year of the credit card", "format": "int32"}, "last4": {"type": "string", "description": "last 4 digits of the credit card"}}, "description": "card model"}, "moego.models.permission.v1.EditCategoryPermissionDef": {"type": "object", "properties": {"categoryId": {"type": "string", "description": "category id"}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.EditPermissionDef"}, "description": "active status of permissions"}}, "description": "definition of permission edit in a category"}, "moego.models.permission.v1.EditPermissionDef": {"type": "object", "properties": {"permissionId": {"type": "string", "description": "permission id."}, "isActive": {"type": "boolean", "description": "whether permission is active."}, "selectedScopeIndex": {"type": "string", "description": "scope of permission."}, "subPermissions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.EditPermissionDef"}, "description": "active status of sub-permissions."}, "selectedSubScopeIndex": {"type": "string", "description": "sub scope of the scope."}, "scopeExtraParam": {"allOf": [{"$ref": "#/components/schemas/moego.models.permission.v1.EditPermissionScopeExtraParamDef"}], "description": "extra params for the the deepest scope. if selected a sub scope, the param is for the sub scope. if selected a scope, the param is for the scope. otherwise, the param is useless."}}, "description": "definition of permission edit."}, "moego.models.permission.v1.EditPermissionScopeExtraParamDef": {"type": "object", "properties": {"beforeDay": {"type": "integer", "description": "before days. current used to control staff message permissions.", "format": "uint32"}, "afterDay": {"type": "integer", "description": "after days. current used to control staff message permissions.", "format": "uint32"}}, "description": "definition of permission scope extra param."}, "moego.models.permission.v1.PermissionCategoryModel": {"type": "object", "properties": {"id": {"type": "string", "description": "permission category id"}, "name": {"type": "string", "description": "permission category name in backend"}, "displayName": {"type": "string", "description": "permission category name to display"}, "description": {"type": "string", "description": "permission category description"}, "permissionList": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.PermissionModel"}, "description": "permissions"}, "categoryType": {"enum": ["PERMISSION_CATEGORY_TYPE_UNSPECIFIED", "ENTERPRISE_HUB", "MOEGO_PLATFORM"], "type": "string", "description": "category type", "format": "enum"}}, "description": "model for permission category"}, "moego.models.permission.v1.PermissionModel": {"type": "object", "properties": {"id": {"type": "string", "description": "permission id"}, "name": {"type": "string", "description": "permission name in backend"}, "displayName": {"type": "string", "description": "permission name to display"}, "description": {"type": "string", "description": "permission description"}, "subPermissionList": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.PermissionModel"}, "description": "sub permissions"}, "scopeList": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.PermissionScopeModel"}, "description": "permission scopes"}, "selectedScopeIndex": {"type": "string", "description": "selected permission scope id"}, "isSelected": {"type": "boolean", "description": "is permission selected"}, "selectedSubScopeIndex": {"type": "string", "description": "selected sub scope id"}}, "description": "model for permission"}, "moego.models.permission.v1.PermissionScopeModel": {"type": "object", "properties": {"index": {"type": "string", "description": "permission scope id"}, "permissionId": {"type": "string", "description": "permission id"}, "name": {"type": "string", "description": "permission scope name in backend"}, "displayName": {"type": "string", "description": "permission scope name to display"}, "availableRule": {"enum": ["PERMISSION_SCOPE_AVAILABLE_RULE_UNSPECIFIED", "ONLY_MULTI_LOCATION", "ONLY_SINGLE_LOCATION"], "type": "string", "description": "permission scope available rule", "format": "enum"}, "extraParam": {"allOf": [{"$ref": "#/components/schemas/moego.models.permission.v1.PermissionScopeModel_ExtraParam"}], "description": "optional extra params."}, "subPermissionScopeList": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.permission.v1.PermissionScopeModel"}, "description": "sub permission scopes. the sub permission scope should not have sub permission scope."}}, "description": "model for permission scope"}, "moego.models.permission.v1.PermissionScopeModel_ExtraParam": {"type": "object", "properties": {"beforeDay": {"type": "integer", "description": "before days. current used to control staff message permissions.", "format": "uint32"}, "afterDay": {"type": "integer", "description": "after days. current used to control staff message permissions.", "format": "uint32"}}, "description": "extra params. you can insert or reuse the params inside."}, "moego.models.reporting.v2.Attribute": {"type": "object", "properties": {"type": {"enum": ["TYPE_UNSPECIFIED", "METRIC", "DIMENSION"], "type": "string", "description": "Attribute type", "format": "enum"}, "label": {"type": "string", "description": "label"}, "description": {"type": "string", "description": "description/tooltips"}, "fieldKey": {"type": "string", "description": "field key"}, "fieldType": {"enum": ["TYPE_UNSPECIFIED", "MONEY", "TEXT", "DATE", "TIME", "NUMBER", "CLIENT_NAME", "INVOICE_ID", "BOOKING_ID", "AVATAR_URL", "DECIMAL_NUMBER", "PERCENTAGE", "DURATION", "DATETIME", "PET_INCIDENT_ID", "COLOR", "CLIENT_ID"], "type": "string", "description": "field type", "format": "enum"}, "sortable": {"type": "boolean", "description": "Whether the field is sortable"}, "removable": {"type": "boolean", "description": "Whether the field is removable"}, "groupByEnable": {"type": "boolean", "description": "Whether the field can be grouped by"}, "movable": {"type": "boolean", "description": "Whether the field is movable"}, "trend": {"enum": ["TREND_UNSPECIFIED", "BENEFIT", "HARMFUL", "NEUTRAL"], "type": "string", "description": "Field trend type: BENEFIT, HARMFUL, NEUTRAL", "format": "enum"}}, "description": "Attribute model"}, "moego.models.reporting.v2.BarData": {"type": "object", "properties": {"groups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.BarGroupData"}, "description": "The groups of the bar data"}}, "description": "Bar data"}, "moego.models.reporting.v2.BarGroupData": {"type": "object", "properties": {"groupName": {"type": "string", "description": "The name of the group"}, "numbers": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "numbers"}, "style": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.StyleConfig"}], "description": "Style config of the line"}}, "description": "Bar group data"}, "moego.models.reporting.v2.CustomizeKPI": {"type": "object", "properties": {"label": {"type": "string", "description": "The label of the KPI"}, "diagramId": {"type": "string", "description": "The diagram id of the KPI"}, "selected": {"type": "boolean", "description": "The checkbox status of the KPI"}}, "description": "Customer KPI configuration"}, "moego.models.reporting.v2.DashBoardDiagram": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "The diagram id"}, "diagramType": {"enum": ["DIAGRAM_TYPE_UNSPECIFIED", "BAR", "FUNNEL", "TABLE", "PIE", "BIG_NUMBER", "STATUS_BAR", "GROUP", "RANK", "PROCEDURE_NUMBER", "NUMBER_ARRAY", "LINE", "ODOMETER", "RATING_STAR", "NUMBER_LIST", "BAR_V2", "LEGEND", "DYNAMIC_TABLE", "MULTI_DIMENSION_TABLE"], "type": "string", "description": "The diagram type", "format": "enum"}, "tableMeta": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TableMeta"}], "description": "table metas"}, "drillConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DrillConfig"}], "description": "The drill configuration"}, "permissionCode": {"type": "string", "description": "Current diagram's permission code"}, "associatedDiagramId": {"type": "string", "description": "associated diagram id"}, "defaultGroupByFieldKeys": {"type": "array", "items": {"type": "string"}, "description": "default group by field key"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Field"}, "description": "fields of the diagram"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Filter"}, "description": "filters of the diagram"}}, "description": "A dashboard diagram"}, "moego.models.reporting.v2.DashBoardGroup": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id"}, "title": {"type": "string", "description": "The title of the dashboard group"}, "description": {"type": "string", "description": "description"}, "drillConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DrillConfig"}], "description": "The drill configuration"}, "diagrams": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DashBoardDiagram"}, "description": "describe the dashboard group contents"}, "permissionCode": {"type": "string", "description": "Current diagram's required permission code"}}, "description": "A group of dashboards"}, "moego.models.reporting.v2.DashboardPage": {"type": "object", "properties": {"tab": {"enum": ["TAB_UNSPECIFIED", "OVERVIEW", "SALES", "PETS", "STAFF", "OPERATION", "APP_PERSONAL_PERFORMANCE", "APP_OVERVIEW", "APP_STAFF_PERFORMANCE", "APP_HISTORY", "APP_REPORT", "PAYROLL", "DAILY_REVENUE", "LEADS"], "type": "string", "description": "The tab of the dashboard page", "format": "enum"}, "title": {"type": "string", "description": "The title of the dashboard page"}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DashBoardGroup"}, "description": "The dashboard groups"}, "permissionCode": {"type": "string", "description": "Current page's required permission code"}}, "description": "A dashboard page"}, "moego.models.reporting.v2.DateTimeField": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "field key"}, "label": {"type": "string", "description": "label of time selector"}, "description": {"type": "string", "description": "description/tooltips of time selector"}}, "description": "Date time field"}, "moego.models.reporting.v2.DeleteCustomReportParams": {"type": "object", "properties": {"diagramIds": {"type": "array", "items": {"type": "string"}, "description": "diagram ids"}}, "description": "DeleteCustomReportParams"}, "moego.models.reporting.v2.DeleteCustomReportResult": {"type": "object", "properties": {"success": {"type": "boolean", "description": "delete success or not"}}, "description": "DeleteCustomReportResult"}, "moego.models.reporting.v2.DiagramData": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "The diagram id"}, "numberData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "The number data"}, "barData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.BarData"}], "description": "The bar data"}, "pieData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.PieData"}], "description": "The pie data"}, "rainbowTableData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TableData"}], "description": "The rainbow table data"}, "rankData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.RankData"}], "description": "The rank data"}, "procedureNumberData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.ProcedureNumberData"}], "description": "The procedure number data"}, "funnelData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.FunnelData"}], "description": "The funnel data"}, "numberArrayData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberArrayData"}], "description": "The number array data"}, "lineData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.LineData"}], "description": "Line data"}, "odometerData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.OdometerData"}], "description": "Odometer data"}, "ratingStarData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.RatingStarData"}], "description": "Rating star data"}, "numberListData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberListData"}], "description": "Number list data"}, "legendData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.LegendData"}], "description": "Legend data"}}, "description": "A dashboard diagram data"}, "moego.models.reporting.v2.DiagramMeta": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "uniq diagram id"}, "title": {"type": "string", "description": "title"}, "description": {"type": "string", "description": "description"}, "downloadEnable": {"type": "boolean", "description": "download enable"}, "permissionCode": {"type": "string", "description": "required permission code"}, "dimensions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionField"}, "description": "dimension fields"}, "metrics": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Field"}, "description": "metric fields"}, "filterGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterGroup"}, "description": "filter groups"}, "timeSelector": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TimeSelector"}], "description": "time selector"}, "customizedConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TableCustomizedConfig"}], "description": "customized configs"}, "dynamicColumnMode": {"type": "boolean", "description": "dynamic column mode, use final dimension to generate columns"}, "showScopeSelector": {"type": "boolean", "description": "Whether to display location/franchisee selector"}, "showComparePeriodSelector": {"type": "boolean", "description": "Whether to display compare period selector"}, "maxQueryDimensions": {"type": "integer", "description": "Max grouping by dimensions count", "format": "int32"}}, "description": "Common diagram meta definitions"}, "moego.models.reporting.v2.Dimension": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "field key"}, "asc": {"type": "boolean", "description": "default order by"}}, "description": "Dimension def for drill config"}, "moego.models.reporting.v2.DimensionConfig": {"type": "object", "properties": {"dimensions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionRequest"}, "description": "fields of dimension"}, "isExpandable": {"type": "boolean", "description": "if can expand for deeper dimension"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "filters for current dimension"}}, "description": "Dimension config"}, "moego.models.reporting.v2.DimensionField": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "field key"}, "fieldType": {"enum": ["TYPE_UNSPECIFIED", "MONEY", "TEXT", "DATE", "TIME", "NUMBER", "CLIENT_NAME", "INVOICE_ID", "BOOKING_ID", "AVATAR_URL", "DECIMAL_NUMBER", "PERCENTAGE", "DURATION", "DATETIME", "PET_INCIDENT_ID", "COLOR", "CLIENT_ID"], "type": "string", "description": "field type", "format": "enum"}, "label": {"type": "string", "description": "display label"}, "description": {"type": "string", "description": "description or tooltips"}, "asc": {"type": "boolean", "description": "default order by"}, "subDimensions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionField"}, "description": "sub dimensions"}}, "description": "Dimension field"}, "moego.models.reporting.v2.DimensionFilter": {"type": "object", "properties": {"dimensions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionRequest"}, "description": "group by fields"}, "dimensionConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionConfig"}], "description": "expanded dimension config, from last api call"}}, "description": "Dimension filter definition"}, "moego.models.reporting.v2.DimensionRequest": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "field key"}, "asc": {"type": "boolean", "description": "order by asc or desc"}}, "description": "Dimension request definition"}, "moego.models.reporting.v2.DrillConfig": {"type": "object", "properties": {"targetType": {"enum": ["TARGET_TYPE_UNSPECIFIED", "TARGET_TYPE_REPORT_TABLE"], "type": "string", "description": "drill type", "format": "enum"}, "targetId": {"type": "string", "description": "The target report ID"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "The filter of drill config"}, "title": {"type": "string", "description": "The title of the drill"}, "dimensions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Dimension"}, "description": "specified dimensions"}}, "description": "A report configuration"}, "moego.models.reporting.v2.DuplicateCustomReportParams": {"type": "object", "properties": {"diagramIds": {"type": "array", "items": {"type": "string"}, "description": "diagram ids"}}, "description": "DuplicateCustomReportParams"}, "moego.models.reporting.v2.DuplicateCustomReportResult": {"type": "object", "properties": {"success": {"type": "boolean", "description": "duplicate success or not"}}, "description": "DuplicateCustomReportResult"}, "moego.models.reporting.v2.ExportDataParams": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id"}, "scope": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.ScopeFilter"}], "description": "query scope: business id or all businesses"}, "timeRange": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TimeFilter"}], "description": "query time"}, "orderBys": {"type": "array", "items": {"$ref": "#/components/schemas/moego.utils.v2.OrderBy"}, "description": "order by params"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "filter params"}, "dimension": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionFilter"}], "description": "dimension filter"}}, "description": "ExportDataParams"}, "moego.models.reporting.v2.ExportDataResult": {"type": "object", "properties": {"fileId": {"type": "string", "description": "file id"}}, "description": "ExportDataResult"}, "moego.models.reporting.v2.FetchDataDef": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.RowDataDef"}, "description": "rows of fetch data result"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Field"}, "description": "fields"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationResponse"}], "description": "pagination info"}, "total": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.RowDataDef"}], "description": "total info for current query"}}, "description": "Fetch data response definition"}, "moego.models.reporting.v2.FetchDataParams": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id"}, "scope": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.ScopeFilter"}], "description": "query scope: business id or all businesses"}, "timeRange": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TimeFilter"}], "description": "query time"}, "pagination": {"allOf": [{"$ref": "#/components/schemas/moego.utils.v2.PaginationRequest"}], "description": "pagination params"}, "orderBys": {"type": "array", "items": {"$ref": "#/components/schemas/moego.utils.v2.OrderBy"}, "description": "order by params"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "filter params"}, "dimension": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionFilter"}], "description": "dimension filter"}, "metricKeys": {"type": "array", "items": {"type": "string"}, "description": "metrics field keys params"}, "dynamicColumnMode": {"type": "boolean", "description": "dynamic column mode, use final dimension to generate columns"}}, "description": "FetchDataParams"}, "moego.models.reporting.v2.FetchDataResult": {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.FetchDataDef"}], "description": "result"}, "lastSyncedTime": {"type": "string", "description": "report data last synced time", "format": "date-time"}}, "description": "FetchDataResult"}, "moego.models.reporting.v2.Field": {"type": "object", "properties": {"label": {"type": "string", "description": "The label of the field, which should be a string between 1 and 50 characters"}, "type": {"enum": ["TYPE_UNSPECIFIED", "MONEY", "TEXT", "DATE", "TIME", "NUMBER", "CLIENT_NAME", "INVOICE_ID", "BOOKING_ID", "AVATAR_URL", "DECIMAL_NUMBER", "PERCENTAGE", "DURATION", "DATETIME", "PET_INCIDENT_ID", "COLOR", "CLIENT_ID"], "type": "string", "description": "The key of the field", "format": "enum"}, "key": {"type": "string", "description": "The key of the field"}, "drillConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DrillConfig"}], "description": "The drill configuration of the field"}, "sortable": {"type": "boolean", "description": "Whether the field is sortable"}, "removable": {"type": "boolean", "description": "Whether the field is removable"}, "groupByEnable": {"type": "boolean", "description": "Whether the field can be grouped by"}, "movable": {"type": "boolean", "description": "Whether the field is movable"}, "trend": {"enum": ["TREND_UNSPECIFIED", "BENEFIT", "HARMFUL", "NEUTRAL"], "type": "string", "description": "Field trend type: BENEFIT, HARMFUL, NEUTRAL", "format": "enum"}, "permissionCode": {"type": "string", "description": "Current field's required permission code"}, "description": {"type": "string", "description": "Current field's description/tooltips"}, "linkageConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.LinkageConfig"}], "description": "linkage config to another diagram"}}, "description": "A field"}, "moego.models.reporting.v2.Filter": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "The field key of the filter parameter"}, "name": {"type": "string", "description": "The name of the filter parameter"}, "options": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterOption"}, "description": "The options of the filter"}, "componentType": {"enum": ["COMPONENT_TYPE_UNSPECIFIED", "CHECKBOX", "RADIO", "SELECT", "DATE", "RADIO_WITH_CUSTOMIZED", "RADIO_INPUT", "INPUT", "RELATIVE_DATE", "LEVEL_TREE_NODE", "RELATIVE_DATE_FUTURE", "CUSTOMIZED_CLIENT_TAG", "CUSTOMIZED_PET_TYPE_AND_BREED", "CUSTOMIZED_PET_CODE", "CUSTOMIZED_ZIPCODE", "CUSTOMIZED_PAYMENT_METHOD", "CUSTOMIZED_SERVICE_AREA", "CUSTOMIZED_WORKFLOW"], "type": "string", "description": "The component type of the filter parameter", "format": "enum"}, "permissionCode": {"type": "string", "description": "Current filter's required permission code"}, "levelOptions": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/moego.models.reporting.v2.LevelFilterOptions"}, "description": "The operator of the filter parameter, key is the [label] of parent filter options."}, "fieldType": {"enum": ["TYPE_UNSPECIFIED", "MONEY", "TEXT", "DATE", "TIME", "NUMBER", "CLIENT_NAME", "INVOICE_ID", "BOOKING_ID", "AVATAR_URL", "DECIMAL_NUMBER", "PERCENTAGE", "DURATION", "DATETIME", "PET_INCIDENT_ID", "COLOR", "CLIENT_ID"], "type": "string", "description": "Field type of the filter", "format": "enum"}, "operator": {"enum": ["OPERATOR_UNSPECIFIED", "EQUAL", "NOT_EQUAL", "IN", "NOT_IN", "LIKE_MULTI", "LIKE", "NOT_LIKE", "PREFIX_LIKE", "SUFFIX_LIKE", "GREATER_THAN", "LESS_THAN", "GREATER_THAN_OR_EQUAL", "LESS_THAN_OR_EQUAL", "AFTER", "BEFORE", "ON", "RANGE", "ARRAY_CONTAINS", "ARRAY_NOT_CONTAINS"], "type": "string", "description": "The default operator of this filter", "format": "enum"}}, "description": "A filter"}, "moego.models.reporting.v2.FilterGroup": {"type": "object", "properties": {"groupName": {"type": "string", "description": "The group name"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Filter"}, "description": "The filters"}, "groupType": {"enum": ["FILTER_GROUP_TYPE_UNSPECIFIED", "SINGLE_FILTER", "LEVEL_TREE", "DATE_INPUT", "CHECKBOX_RELATIVE_DATE", "CHECKBOX_CHECKBOX"], "type": "string", "description": "group type", "format": "enum"}, "description": {"type": "string", "description": "The description of the filter group"}, "categoryName": {"type": "string", "description": "category name"}, "categorySort": {"type": "integer", "description": "category sort", "format": "int32"}, "sort": {"type": "integer", "description": "filter group sort", "format": "int32"}}, "description": "The operator of the filter parameter"}, "moego.models.reporting.v2.FilterOption": {"type": "object", "properties": {"operator": {"enum": ["OPERATOR_UNSPECIFIED", "EQUAL", "NOT_EQUAL", "IN", "NOT_IN", "LIKE_MULTI", "LIKE", "NOT_LIKE", "PREFIX_LIKE", "SUFFIX_LIKE", "GREATER_THAN", "LESS_THAN", "GREATER_THAN_OR_EQUAL", "LESS_THAN_OR_EQUAL", "AFTER", "BEFORE", "ON", "RANGE", "ARRAY_CONTAINS", "ARRAY_NOT_CONTAINS"], "type": "string", "description": "The operator of the filter parameter", "format": "enum"}, "label": {"type": "string", "description": "The label of the filter option"}, "description": {"type": "string", "description": "The label of the filter option"}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Value"}, "description": "option values"}}, "description": "Filter option definition"}, "moego.models.reporting.v2.FilterRequest": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "The field key of the filter parameter"}, "operator": {"enum": ["OPERATOR_UNSPECIFIED", "EQUAL", "NOT_EQUAL", "IN", "NOT_IN", "LIKE_MULTI", "LIKE", "NOT_LIKE", "PREFIX_LIKE", "SUFFIX_LIKE", "GREATER_THAN", "LESS_THAN", "GREATER_THAN_OR_EQUAL", "LESS_THAN_OR_EQUAL", "AFTER", "BEFORE", "ON", "RANGE", "ARRAY_CONTAINS", "ARRAY_NOT_CONTAINS"], "type": "string", "description": "The operator of the filter parameter", "format": "enum"}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Value"}, "description": "The option value or input values"}, "label": {"type": "string", "description": "The label of the filter option"}, "valuePreview": {"type": "string", "description": "The preview value of the filter option"}, "invertSelect": {"type": "boolean", "description": "Invert select values"}}, "description": "Filter params definition"}, "moego.models.reporting.v2.FilterRequestGroup": {"type": "object", "properties": {"groupName": {"type": "string", "description": "The group name, = FilterGroup.group_name"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "The filters"}}, "description": "Filter group params definition"}, "moego.models.reporting.v2.FunnelData": {"type": "object", "properties": {"numbers": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "The title of the funnel data"}}, "description": "Funnel data"}, "moego.models.reporting.v2.GetDimensionsParams": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id to get dimensions, if not present, return all dimensions"}}, "description": "Get dimensions params"}, "moego.models.reporting.v2.GetDimensionsResult": {"type": "object", "properties": {"dimensions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionField"}, "description": "dimensions"}}, "description": "Get dimensions result"}, "moego.models.reporting.v2.GetMetricsCategoriesParams": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id to get metrics, if not present, return all metrics"}}, "description": "Get metrics params"}, "moego.models.reporting.v2.GetMetricsCategoriesResult": {"type": "object", "properties": {"categories": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.MetricsCategoryDef"}, "description": "metrics categories"}}, "description": "Get metrics result"}, "moego.models.reporting.v2.LegendData": {"type": "object", "properties": {"numbers": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "Number list"}, "total": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "Total value of legend data"}}, "description": "Legend data"}, "moego.models.reporting.v2.LevelFilterOptions": {"type": "object", "properties": {"options": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterOption"}, "description": "The operator of the filter parameter"}}, "description": "Level filter options"}, "moego.models.reporting.v2.LineData": {"type": "object", "properties": {"groups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.LineGroupData"}, "description": "Group list: One group represents a line in the line chart"}}, "description": "Line chart data"}, "moego.models.reporting.v2.LineGroupData": {"type": "object", "properties": {"groupName": {"type": "string", "description": "The name of the group"}, "numbers": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "The line data"}, "style": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.StyleConfig"}], "description": "Style config of the line"}}, "description": "Line group data, contributed to the line chart"}, "moego.models.reporting.v2.LinkageConfig": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "filters for next query"}}, "description": "linkage config to another diagram"}, "moego.models.reporting.v2.MetricsCategoryDef": {"type": "object", "properties": {"name": {"type": "string", "description": "category name"}, "description": {"type": "string", "description": "description"}, "metrics": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Attribute"}, "description": "metrics"}}, "description": "Metrics category definition"}, "moego.models.reporting.v2.ModifyCustomDiagramParams": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id, required"}, "name": {"type": "string", "description": "name of custom report, should be trimmed"}, "description": {"type": "string", "description": "description of custom report"}, "dynamicColumnMode": {"type": "boolean", "description": "dynamic column mode, use final dimension to generate columns"}}, "description": "ModifyCustomDiagramParams"}, "moego.models.reporting.v2.ModifyCustomDiagramResult": {"type": "object", "properties": {"success": {"type": "boolean", "description": "modify success or not"}}, "description": "ModifyCustomDiagramResult"}, "moego.models.reporting.v2.MoeGoInsight": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the insight"}, "descriptionPattern": {"type": "string", "description": "The description of the insight:"}, "values": {"type": "array", "items": {"type": "string"}, "description": "The values of the insight description"}}, "description": "A MoeGo insight"}, "moego.models.reporting.v2.NumberArrayData": {"type": "object", "properties": {"numbers": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "The numbers of the number array data"}, "title": {"type": "string", "description": "Title to display"}, "description": {"type": "string", "description": "Description or tips"}, "style": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.StyleConfig"}], "description": "Style config"}}, "description": "Number array data"}, "moego.models.reporting.v2.NumberData": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "The field_key of the big number"}, "fieldType": {"enum": ["TYPE_UNSPECIFIED", "MONEY", "TEXT", "DATE", "TIME", "NUMBER", "CLIENT_NAME", "INVOICE_ID", "BOOKING_ID", "AVATAR_URL", "DECIMAL_NUMBER", "PERCENTAGE", "DURATION", "DATETIME", "PET_INCIDENT_ID", "COLOR", "CLIENT_ID"], "type": "string", "description": "The type of the field", "format": "enum"}, "value": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.Value"}], "description": "The value of the big number"}, "previousValue": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.Value"}], "description": "The previous value of the big number"}, "label": {"type": "string", "description": "The label of the big number"}, "description": {"type": "string", "description": "The description of the big number"}, "trend": {"enum": ["TREND_UNSPECIFIED", "BENEFIT", "HARMFUL", "NEUTRAL"], "type": "string", "description": "The trend of the field", "format": "enum"}, "insight": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.MoeGoInsight"}], "description": "The insight information"}, "style": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.StyleConfig"}], "description": "Style config of the line"}, "drillConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DrillConfig"}], "description": "Drill config for the field"}, "linkageConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.LinkageConfig"}], "description": "Linkage config for the field"}}, "description": "Number data"}, "moego.models.reporting.v2.NumberListData": {"type": "object", "properties": {"numbers": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "Data of the number list"}}, "description": "Number list data, same as number array, display as rows"}, "moego.models.reporting.v2.OdometerData": {"type": "object", "properties": {"measureMetric": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "odometer's right value"}, "indicatorMetric": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "odometer's left value"}, "mainIcon": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "odometer's icon"}, "mainMetric": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "odometer's center value"}, "firstName": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "character's first name"}, "lastName": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "character's last name"}}, "description": "Odometer data"}, "moego.models.reporting.v2.PageMetaDef": {"type": "object", "properties": {"title": {"type": "string", "description": "page title"}, "tab": {"enum": ["INSIGHTS_TAB_UNSPECIFIED", "ALL", "OVERVIEW", "SALES", "CLIENT_INSIGHTS", "EMPLOYEE", "APPOINTMENT", "FINANCE", "TENANT", "PAYROLL", "CUSTOMIZED", "LEGACY_APPOINTMENT"], "type": "string", "description": "tab", "format": "enum"}, "diagrams": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.ReportDiagram"}, "description": "diagrams of the page"}, "permissionCode": {"type": "string", "description": "Current page's required permission code"}}, "description": "Page meta definition"}, "moego.models.reporting.v2.PieData": {"type": "object", "properties": {"numbers": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "The title of the pie data"}, "total": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "The total of the pie data"}}, "description": "Pie data"}, "moego.models.reporting.v2.ProcedureNumberData": {"type": "object", "properties": {"steps": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.StepUnitData"}, "description": "step data list"}}, "description": "Procedure number data"}, "moego.models.reporting.v2.QueryMetasParams": {"type": "object", "properties": {"diagramIds": {"type": "array", "items": {"type": "string"}, "description": "diagram_ids to query"}}, "description": "QueryMetasParams"}, "moego.models.reporting.v2.QueryMetasResult": {"type": "object", "properties": {"metas": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DiagramMeta"}, "description": "meta result"}}, "description": "QueryMetasResult"}, "moego.models.reporting.v2.QueryPageMetaParams": {"type": "object", "properties": {"tabs": {"type": "array", "items": {"enum": ["INSIGHTS_TAB_UNSPECIFIED", "ALL", "OVERVIEW", "SALES", "CLIENT_INSIGHTS", "EMPLOYEE", "APPOINTMENT", "FINANCE", "TENANT", "PAYROLL", "CUSTOMIZED", "LEGACY_APPOINTMENT"], "type": "string", "format": "enum"}, "description": "tabs to query"}, "scene": {"enum": ["REPORTING_TYPE_UNSPECIFIED", "COMMON", "ENTERPRISE_HUB", "APP"], "type": "string", "description": "reporting scene", "format": "enum"}}, "description": "Query page meta request definition"}, "moego.models.reporting.v2.QueryPageMetaResult": {"type": "object", "properties": {"pages": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.PageMetaDef"}, "description": "pages"}, "lastSyncedTime": {"type": "string", "description": "last synced time", "format": "date-time"}}, "description": "Query page meta response definition"}, "moego.models.reporting.v2.RankData": {"type": "object", "properties": {"ranks": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.RankUnit"}, "description": "The list of the rank data"}}, "description": "A rank data"}, "moego.models.reporting.v2.RankUnit": {"type": "object", "properties": {"avatarUrl": {"type": "string", "description": "avatar url"}, "label": {"type": "string", "description": "label"}, "mainMetric": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.Value"}], "description": "main_metric"}, "secondaryMetric": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.Value"}], "description": "secondary_metric"}, "style": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.StyleConfig"}], "description": "Style config of the line"}, "mainMetricData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "main metric data"}, "secondaryMetricData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "secondary metric data"}}, "description": "rank unit"}, "moego.models.reporting.v2.RatingStarData": {"type": "object", "properties": {"ratingStar": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "rating star value"}}, "description": "Rating star data"}, "moego.models.reporting.v2.ReportDiagram": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "The diagram id"}, "type": {"enum": ["DIAGRAM_TYPE_UNSPECIFIED", "BAR", "FUNNEL", "TABLE", "PIE", "BIG_NUMBER", "STATUS_BAR", "GROUP", "RANK", "PROCEDURE_NUMBER", "NUMBER_ARRAY", "LINE", "ODOMETER", "RATING_STAR", "NUMBER_LIST", "BAR_V2", "LEGEND", "DYNAMIC_TABLE", "MULTI_DIMENSION_TABLE"], "type": "string", "description": "The diagram type", "format": "enum"}, "title": {"type": "string", "description": "The title of the report"}, "description": {"type": "string", "description": "The description of the report"}, "favorite": {"type": "boolean", "description": "Whether the report is a favorite"}, "permissionCode": {"type": "string", "description": "Current diagram's required permission code"}, "isCustomized": {"type": "boolean", "description": "Whether the report is a custom report"}}, "description": "A report entry"}, "moego.models.reporting.v2.ReportPage": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the page"}, "tab": {"enum": ["TAB_UNSPECIFIED", "ALL", "FINANCE", "SALES", "APPOINTMENT", "EMPLOYEE", "PAYROLL", "CLIENT_INSIGHTS", "CUSTOMER", "TENANT", "CUSTOMIZED", "LEGACY_APPOINTMENT"], "type": "string", "description": "The tab of the report", "format": "enum"}, "diagrams": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.ReportDiagram"}, "description": "The reports of the page"}, "permissionCode": {"type": "string", "description": "Current page's required permission code"}}, "description": "A page of reports"}, "moego.models.reporting.v2.RowDataDef": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "row data"}, "subData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.FetchDataDef"}], "description": "sub dimension data"}, "dimensionConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionConfig"}], "description": "dimension config of current row"}, "rowUuid": {"type": "string", "description": "row unique id"}, "drillConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DrillConfig"}], "description": "drill config of one row"}}, "description": "Row data of Fetch data response definition"}, "moego.models.reporting.v2.SaveCustomReportParams": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "diagram id, exists when update"}, "name": {"type": "string", "description": "name of custom report, should be trimmed"}, "description": {"type": "string", "description": "description of custom report"}, "metricKeys": {"type": "array", "items": {"type": "string"}, "description": "metric field keys"}, "dimensions": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DimensionField"}, "description": "dimension fields"}, "savedFilters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "saved filters"}, "dynamicColumnMode": {"type": "boolean", "description": "dynamic column mode, use final dimension to generate columns"}}, "description": "SaveCustomReportParams"}, "moego.models.reporting.v2.SaveCustomReportResult": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "return created or updated diagram_id"}}, "description": "SaveCustomReportResult"}, "moego.models.reporting.v2.ScopeFilter": {"type": "object", "properties": {"scopeIds": {"type": "array", "items": {"type": "string"}, "description": "scope ids, could be business_ids or tenant_ids"}, "allScopes": {"type": "boolean", "description": "if query all scopes"}, "scopeType": {"enum": ["SCOPE_TYPE_UNSPECIFIED", "BUSINESS", "TENANT"], "type": "string", "description": "scope type, will set in api layer, currently can ignore in front-end", "format": "enum"}, "scopeParentId": {"type": "string", "description": "scope parent id: company_id or enterprise_id, will set in api layer, currently can ignore in front-end"}}, "description": "Scope filter definition: business or tenant"}, "moego.models.reporting.v2.StepUnitData": {"type": "object", "properties": {"leadData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "data lead to step, if it's the first one, lead data might be empty"}, "stepData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}], "description": "step data"}}, "description": "Step unit of Procedure number data"}, "moego.models.reporting.v2.StyleConfig": {"type": "object", "properties": {"color": {"type": "string", "description": "Color value"}, "lineType": {"enum": ["LINE_TYPE_UNSPECIFIED", "SOLID", "DASHED"], "type": "string", "description": "Line type only for line chart", "format": "enum"}, "stack": {"type": "string", "description": "Stack name for bar chart, same stack will overlap"}, "format": {"enum": ["FORMAT_UNSPECIFIED", "DURATION_MIN", "DURATION_HOUR", "MONEY_ABBREVIATION", "RATING_STAR", "PERCENTAGE_BAR"], "type": "string", "description": "Format of the data", "format": "enum"}, "icon": {"enum": ["ICON_UNSPECIFIED", "MINOR_REFRESH_OUTLINED", "MINOR_DOLLAR_OUTLINED", "MINOR_GIVE_DOLLAR_OUTLINED", "MINOR_PAWS_OUTLINED", "MINOR_CONTRACT_OUTLINED", "MINOR_HEART_OUTLINED", "MINOR_CLOCK_OUTLINED", "MINOR_CALENDAR_OUTLINED", "MINOR_USER_OUTLINED", "MINOR_TICKET_OUTLINED"], "type": "string", "description": "Field icon", "format": "enum"}, "subStyleMap": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/moego.models.reporting.v2.StyleConfig"}, "description": "Style map for dynamic rows"}}, "description": "Style config for diagram"}, "moego.models.reporting.v2.TableCustomizedConfig": {"type": "object", "properties": {"sortedFieldKeys": {"type": "array", "items": {"type": "string"}, "description": "The sorted field"}, "hiddenFieldKeys": {"type": "array", "items": {"type": "string"}, "description": "The hidden field keys"}, "kpis": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.CustomizeKPI"}, "description": "The group by field keys"}, "savedFilters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterRequest"}, "description": "Saved filters"}}, "description": "A table customized configuration"}, "moego.models.reporting.v2.TableData": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.TableRowData"}, "description": "The title of the table"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Field"}, "description": "Fields of the table, fields from TableMeta or Dynamic generated fields"}}, "description": "Table data"}, "moego.models.reporting.v2.TableMeta": {"type": "object", "properties": {"diagramId": {"type": "string", "description": "current report table id"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Field"}, "description": "The fields of the table"}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.Filter"}, "description": "The filters of the table, deprecated by chris, use filter_groups instead"}, "customizedConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TableCustomizedConfig"}], "description": "The customized configurations of the table"}, "title": {"type": "string", "description": "The title of the table"}, "description": {"type": "string", "description": "The description of the table"}, "downloadEnable": {"type": "boolean", "description": "The download enable of the table"}, "drillConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DrillConfig"}], "description": "The drill configuration"}, "defaultGroupByFieldKeys": {"type": "array", "items": {"type": "string"}, "description": "default group by field key"}, "permissionCode": {"type": "string", "description": "Current table's required permission code"}, "filterGroups": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.FilterGroup"}, "description": "The filter groups of the table"}, "showScopeSelector": {"type": "boolean", "description": "Whether to display location/franchisee selector"}, "showComparePeriodSelector": {"type": "boolean", "description": "Whether to display compare period selector"}, "maxQueryDimensions": {"type": "integer", "description": "Max grouping by dimensions count", "format": "int32"}}, "description": "A table meta"}, "moego.models.reporting.v2.TableRowData": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/moego.models.reporting.v2.NumberData"}, "description": "The data of one row"}, "drillConfig": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.DrillConfig"}], "description": "drill config of one row"}, "subData": {"allOf": [{"$ref": "#/components/schemas/moego.models.reporting.v2.TableData"}], "description": "sub data for multi dimension table"}}, "description": "Table row data"}, "moego.models.reporting.v2.TimeFilter": {"type": "object", "properties": {"fieldKey": {"type": "string", "description": "field key"}, "currentPeriod": {"allOf": [{"$ref": "#/components/schemas/google.type.Interval"}], "description": "current period"}, "previousPeriod": {"allOf": [{"$ref": "#/components/schemas/google.type.Interval"}], "description": "previous period"}}, "description": "Time filter definition"}, "moego.models.reporting.v2.TimeSelector": {"type": "object", "properties": {"fields": {"type": "array", "items": {"$ref": "#/components/schemas/moego.models.reporting.v2.DateTimeField"}, "description": "time selector fields"}}, "description": "Time selector config"}, "moego.models.reporting.v2.Value": {"type": "object", "properties": {"string": {"type": "string", "description": "The string value"}, "double": {"type": "number", "description": "The double value", "format": "double"}, "int64": {"type": "string", "description": "The int64 value"}, "bool": {"type": "boolean", "description": "The bool value"}, "money": {"allOf": [{"$ref": "#/components/schemas/google.type.Money"}], "description": "The money value"}, "timestamp": {"type": "string", "description": "The timestamp value", "format": "date-time"}}, "description": "A value of any type"}, "moego.service.enterprise.v1.ListServiceCategoriesRequest_Filter": {"type": "object", "properties": {"enterpriseIds": {"type": "array", "items": {"type": "string"}, "description": "enterprise ids"}, "serviceItemTypes": {"type": "array", "items": {"enum": ["SERVICE_ITEM_TYPE_UNSPECIFIED", "GROOMING", "BOARDING", "DAYCARE", "EVALUATION", "DOG_WALKING", "GROUP_CLASS"], "type": "string", "format": "enum"}, "description": "service item type"}, "priceBookIds": {"type": "array", "items": {"type": "string"}, "description": "price book ids"}, "serviceTypes": {"type": "array", "items": {"enum": ["SERVICE_TYPE_UNSPECIFIED", "SERVICE", "ADDON"], "type": "string", "format": "enum"}, "description": "service type"}}, "description": "filter"}, "moego.service.enterprise.v1.ListServiceChangeHistoriesRequest_Filter": {"type": "object", "properties": {"enterpriseIds": {"type": "array", "items": {"type": "string"}, "description": "enterprise ids"}, "serviceIds": {"type": "array", "items": {"type": "string"}, "description": "service ids"}}, "description": "filter"}, "moego.service.enterprise.v1.ListServiceChangeHistoriesRequest_OrderBy": {"type": "object", "properties": {"field": {"enum": ["FIELD_UNSPECIFIED", "UPDATED_AT"], "type": "string", "description": "field", "format": "enum"}, "asc": {"type": "boolean", "description": "asc"}}, "description": "order by"}, "moego.service.enterprise.v1.ListServiceChangesRequest_Filter": {"type": "object", "properties": {"enterpriseIds": {"type": "array", "items": {"type": "string"}, "description": "enterprise ids"}, "serviceIds": {"type": "array", "items": {"type": "string"}, "description": "service ids"}, "historyIds": {"type": "array", "items": {"type": "string"}, "description": "history ids"}}, "description": "filter"}, "moego.service.enterprise.v1.ListServicesRequest_Filter": {"type": "object", "properties": {"enterpriseIds": {"type": "array", "items": {"type": "string"}, "description": "enterprise ids"}, "serviceItemTypes": {"type": "array", "items": {"enum": ["SERVICE_ITEM_TYPE_UNSPECIFIED", "GROOMING", "BOARDING", "DAYCARE", "EVALUATION", "DOG_WALKING", "GROUP_CLASS"], "type": "string", "format": "enum"}, "description": "service item type"}, "inactive": {"type": "boolean", "description": "inactive"}, "categoryIds": {"type": "array", "items": {"type": "string"}, "description": "category ids"}, "priceBookIds": {"type": "array", "items": {"type": "string"}, "description": "price book ids"}, "serviceTypes": {"type": "array", "items": {"enum": ["SERVICE_TYPE_UNSPECIFIED", "SERVICE", "ADDON"], "type": "string", "format": "enum"}, "description": "service type"}}, "description": "filter"}, "moego.service.enterprise.v1.ListTemplatesRequest_Filter": {"type": "object", "properties": {"enterpriseIds": {"type": "array", "items": {"type": "string"}, "description": "enterprise ids"}, "name": {"type": "string", "description": "template name"}, "types": {"type": "array", "items": {"enum": ["TYPE_UNSPECIFIED", "SMS", "EMAIL"], "type": "string", "format": "enum"}, "description": "template types"}, "statuses": {"type": "array", "items": {"enum": ["STATUS_UNSPECIFIED", "DRAFT", "PUBLISHED"], "type": "string", "format": "enum"}, "description": "template statuses"}}, "description": "filter"}, "moego.service.enterprise.v1.SortServicesRequest_ServiceCategorySort": {"type": "object", "properties": {"id": {"type": "string", "description": "id"}, "serviceIds": {"type": "array", "items": {"type": "string"}, "description": "enterprise id"}}, "description": "service category sort"}, "moego.utils.v2.OrderBy": {"type": "object", "properties": {"fieldName": {"type": "string", "description": "field name"}, "asc": {"type": "boolean", "description": "asc, if not set, default is true"}}, "description": "order by"}, "moego.utils.v2.PaginationRequest": {"type": "object", "properties": {"pageSize": {"type": "integer", "description": "page size, allow 0", "format": "int32"}, "pageNum": {"type": "integer", "description": "page number, start from 1", "format": "int32"}}, "description": "offset style pagination request"}, "moego.utils.v2.PaginationResponse": {"type": "object", "properties": {"total": {"type": "integer", "description": "the total count", "format": "int32"}, "pageSize": {"type": "integer", "description": "the page size from request", "format": "int32"}, "pageNum": {"type": "integer", "description": "the page num from request", "format": "int32"}}, "description": "pagination response"}}}, "tags": [{"name": "moego.enterprise.account.v1.AccountAccessService", "description": "account access API", "x-displayName": "AccountAccessService"}, {"name": "moego.enterprise.account.v1.AccountInfoService", "description": "account info API", "x-displayName": "AccountInfoService"}, {"name": "moego.enterprise.campaign.v1.CampaignService", "description": "campaign service", "x-displayName": "CampaignService"}, {"name": "moego.enterprise.configuration.v1.ConfigurationService", "description": "configuration service", "x-displayName": "ConfigurationService"}, {"name": "moego.enterprise.report.v1.EnterpriseAttributeService", "description": "Attribute API for enterprise", "x-displayName": "EnterpriseAttributeService"}, {"name": "moego.enterprise.report.v1.EnterpriseCustomReportService", "description": "Custom report API for enterprise", "x-displayName": "EnterpriseCustomReportService"}, {"name": "moego.enterprise.report.v1.EnterpriseDashboardService", "description": "DashboardService is the service for reporting dashboards", "x-displayName": "EnterpriseDashboardService"}, {"name": "moego.enterprise.report.v1.EnterpriseReportService", "description": "ReportService is the service for reporting reports", "x-displayName": "EnterpriseReportService"}, {"name": "moego.enterprise.enterprise.v1.EnterpriseService", "description": "EnterpriseService", "x-displayName": "EnterpriseService"}, {"name": "moego.enterprise.file.v1.FileService", "description": "FileService", "x-displayName": "FileService"}, {"name": "moego.enterprise.map.v1.MapService", "description": "MapService", "x-displayName": "MapService"}, {"name": "moego.enterprise.permission.v1.PermissionService", "description": "permission service", "x-displayName": "PermissionService"}, {"name": "moego.enterprise.price_book.v1.PriceBookService", "description": "price book service", "x-displayName": "PriceBookService"}, {"name": "moego.enterprise.session.v1.SessionService", "description": "SessionService", "x-displayName": "SessionService"}, {"name": "moego.enterprise.staff.v1.StaffService", "description": "staff service", "x-displayName": "StaffService"}, {"name": "moego.enterprise.subscription.v1.SubscriptionService", "description": "SubscriptionService", "x-displayName": "SubscriptionService"}, {"name": "moego.enterprise.tenant.v1.TenantService", "description": "tenant service", "x-displayName": "TenantService"}, {"name": "moego.enterprise.tenant.v1.TerritoryService", "description": "territory service", "x-displayName": "TerritoryService"}, {"name": "moego.enterprise.automation.v1.WorkflowService", "description": "Workflow Service", "x-displayName": "WorkflowService"}], "x-tagGroups": [{"name": "account.v1", "tags": ["moego.enterprise.account.v1.AccountAccessService", "moego.enterprise.account.v1.AccountInfoService"]}, {"name": "automation.v1", "tags": ["moego.enterprise.automation.v1.WorkflowService"]}, {"name": "campaign.v1", "tags": ["moego.enterprise.campaign.v1.CampaignService"]}, {"name": "configuration.v1", "tags": ["moego.enterprise.configuration.v1.ConfigurationService"]}, {"name": "enterprise.v1", "tags": ["moego.enterprise.enterprise.v1.EnterpriseService"]}, {"name": "file.v1", "tags": ["moego.enterprise.file.v1.FileService"]}, {"name": "map.v1", "tags": ["moego.enterprise.map.v1.MapService"]}, {"name": "permission.v1", "tags": ["moego.enterprise.permission.v1.PermissionService"]}, {"name": "price_book.v1", "tags": ["moego.enterprise.price_book.v1.PriceBookService"]}, {"name": "report.v1", "tags": ["moego.enterprise.report.v1.EnterpriseAttributeService", "moego.enterprise.report.v1.EnterpriseCustomReportService", "moego.enterprise.report.v1.EnterpriseDashboardService", "moego.enterprise.report.v1.EnterpriseReportService"]}, {"name": "session.v1", "tags": ["moego.enterprise.session.v1.SessionService"]}, {"name": "staff.v1", "tags": ["moego.enterprise.staff.v1.StaffService"]}, {"name": "subscription.v1", "tags": ["moego.enterprise.subscription.v1.SubscriptionService"]}, {"name": "tenant.v1", "tags": ["moego.enterprise.tenant.v1.TenantService", "moego.enterprise.tenant.v1.TerritoryService"]}]}