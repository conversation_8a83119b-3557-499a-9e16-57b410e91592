ALTER TABLE moe_grooming.`moe_book_online_abandon_record`
  ADD COLUMN `appointment_id` int DEFAULT NULL COMMENT 'related appointment id' AFTER `staff_id`;

ALTER TABLE moe_grooming.`moe_book_online_abandon_record`
  ADD COLUMN `lead_type` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT 'lead type, new_visitor, existing_client' AFTER `appointment_id`;

ALTER TABLE moe_grooming.`moe_book_online_deposit`
  ADD INDEX idx_business_create_time (`business_id`, `create_time`);

CREATE TABLE moe_grooming.`moe_book_online_metrics`
(
  `id`               int unsigned                                                      NOT NULL AUTO_INCREMENT,
  `business_id`      int unsigned                                                      NOT NULL COMMENT 'moe_business.id',
  `book_online_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'business online booking name',
  `metric_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'metric name',
  `metric_value`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'metric value',
  `date_range_alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'date range alias name',
  `start_date`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'start date',
  `end_date`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'end date',
  `create_time`      datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time`      datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `business_metric_date` (`business_id`, `metric_name`, `date_range_alias`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='Online booking metrics';
