package com.moego.server.grooming.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum StaffAvailableTypeEnum {
    /**
     * 0 - Not selected staff
     * 1 - Selected staff available
     * 2 - Some staffs available
     * 3 - Other staffs available
     * 4 - No staff available
     */
    NOT_SELECTED(0),
    SELECTED_AVAILABLE(1),
    SOME_AVAILABLE(2),
    OTHER_AVAILABLE(3),
    NO_AVAILABLE(4);

    private final int value;
}
