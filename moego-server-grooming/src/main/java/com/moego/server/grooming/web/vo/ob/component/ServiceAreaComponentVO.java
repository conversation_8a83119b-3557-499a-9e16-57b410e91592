package com.moego.server.grooming.web.vo.ob.component;

import com.moego.server.business.dto.GeoAreaDTO;
import com.moego.server.grooming.enums.LandingPageComponentEnum;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Service area component.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServiceAreaComponentVO extends BaseComponentVO {

    private List<Area> result;

    @Override
    public String getComponent() {
        return LandingPageComponentEnum.SERVICE_AREA.getComponent();
    }

    @Data
    public static class Area {
        private Integer areaId;
        private String areaName;
        private String colorCode;
        private List<List<Double>> polygon;
        /**
         * @see GeoAreaDTO.AreaType
         */
        private Byte serviceAreaType;

        private List<String> zipcodes;

        public static Area from(GeoAreaDTO geoAreaDTO) {
            Area area = new Area();
            area.setAreaId(geoAreaDTO.getId());
            area.setAreaName(geoAreaDTO.getAreaName());
            area.setColorCode(geoAreaDTO.getColorCode());
            area.setPolygon(geoAreaDTO.getAreaPolygon());
            area.setServiceAreaType(geoAreaDTO.getAreaType());
            area.setZipcodes(geoAreaDTO.getZipCodes());
            return area;
        }
    }
}
