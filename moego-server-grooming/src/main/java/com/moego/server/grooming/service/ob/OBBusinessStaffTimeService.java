package com.moego.server.grooming.service.ob;

import com.moego.common.enums.OnlineBookingConst;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.grooming.config.SmartScheduleConfiguration;
import com.moego.server.grooming.config.SmartScheduleProperties;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ob.PetApplicableDTO;
import com.moego.server.grooming.dto.ob.SelectedPetServiceDTO;
import com.moego.server.grooming.dto.ob.StaffFirstAvailableDateDTO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.dto.OBAvailableTimeDto;
import com.moego.server.grooming.web.dto.ob.OBBusinessStaffDTO;
import com.moego.server.grooming.web.dto.ob.OBTimeSlotSimpleDTO;
import com.moego.server.grooming.web.vo.ob.OBBusinessStaffVO;
import jakarta.annotation.Nonnull;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBBusinessStaffTimeService {

    private final OBBusinessService businessService;

    private final OBBusinessStaffService businessStaffService;

    private final OBClientTimeSlotService obClientTimeSlotService;

    private final SmartScheduleProperties smartScheduleProperties;

    @Autowired
    @Qualifier(SmartScheduleConfiguration.SMART_SCHEDULE_EXECUTOR_SERVICE)
    private ExecutorService executorService;

    private final FeatureFlagApi featureFlagApi;

    public List<OBBusinessStaffVO> getOBAvailableStaffListByService(OBBusinessStaffDTO obBusinessStaffDTO) {
        Integer businessId = obBusinessStaffDTO.getBusinessId();
        List<SelectedPetServiceDTO> selectedPetServiceList = obBusinessStaffDTO.getPetServiceList();

        // OB settings
        MoeBusinessBookOnline businessBookOnline = businessService.getSettingInfoByBusinessId(businessId);
        byte availabilityType = businessBookOnline.getAvailableTimeType();

        // available staffs (show on calendar, 在指定 availabilityType 下 enable, 且 not deleted)
        List<MoeStaffDto> staffList = businessStaffService.getOBAvailableStaffList(businessId, availabilityType);
        List<Integer> allStaffIdList =
                staffList.stream().map(MoeStaffDto::getId).collect(Collectors.toList());

        List<Integer> allServiceIdList = selectedPetServiceList.stream()
                .flatMap(list -> list.getServiceIdList().stream())
                .distinct()
                .collect(Collectors.toList());

        // service -> staff list
        Map<Integer, List<Integer>> serviceToStaffMap =
                businessStaffService.getServiceToStaffMap(businessId, allServiceIdList, allStaffIdList);

        // applicable (staffId -> list of applicable for each pet)
        Map<Integer, List<PetApplicableDTO>> applicableMap =
                OBBusinessStaffService.buildApplicableMap(allStaffIdList, selectedPetServiceList, serviceToStaffMap);

        // 筛选所有 pet 都 applicable 的 staff
        var applicableStaffIdList = applicableMap.entrySet().stream()
                .filter(entry -> entry.getValue().stream().allMatch(PetApplicableDTO::getApplicable))
                .map(Map.Entry::getKey)
                .toList();

        // first available date (staffId -> first available date)
        Map<Integer, String> staffFirstAvailableDate = new HashMap<>();
        if (OnlineBookingConst.AVAILABLE_TIME_TYPE_DISABLE != availabilityType) {
            staffFirstAvailableDate =
                    getFirstAvailableDateOfApplicableStaffNew(obBusinessStaffDTO, applicableStaffIdList);
        }

        boolean isFilterStaffByService = featureFlagApi.isOn(
                FeatureFlags.FILTER_STAFF_BY_SERVICE,
                FeatureFlagContext.builder().business(businessId.longValue()).build());
        if (isFilterStaffByService) {
            var applicableStaffIdSet = new HashSet<>(applicableStaffIdList);
            staffList = staffList.stream()
                    .filter(staff -> applicableStaffIdSet.contains(staff.getId()))
                    .toList();
        }

        return buildResult(staffList, applicableMap, staffFirstAvailableDate);
    }

    @Nonnull
    private static List<OBBusinessStaffVO> buildResult(
            final List<MoeStaffDto> staffList,
            final Map<Integer, List<PetApplicableDTO>> applicableMap,
            final Map<Integer, String> staffFirstAvailableDate) {
        List<OBBusinessStaffVO> staffVOList = new ArrayList<>();
        for (MoeStaffDto staff : staffList) {
            Integer staffId = staff.getId();

            OBBusinessStaffVO staffVO = new OBBusinessStaffVO();
            staffVO.setId(staffId);
            staffVO.setFirstName(staff.getFirstName());
            staffVO.setLastName(staff.getLastName());
            staffVO.setAvatarPath(staff.getAvatarPath());
            staffVO.setPetApplicableDTOList(applicableMap.getOrDefault(staffId, List.of()));
            staffVO.setFirstAvailableDate(staffFirstAvailableDate.getOrDefault(staffId, ""));
            staffVOList.add(staffVO);
        }

        return staffVOList;
    }

    /**
     * 如果某个 staff 对所有 pet 都 applicable，那么查询该 staff 最近可服务的日期
     * 返回的 map 中，key 只包含对所有 pet 都 applicable 的 staffId
     * @param obBusinessStaffDTO
     * @param applicableStaffIdList
     * @return Map: staffId -> first available date
     */
    private Map<Integer, String> getFirstAvailableDateOfApplicableStaffNew(
            OBBusinessStaffDTO obBusinessStaffDTO, List<Integer> applicableStaffIdList) {

        if (CollectionUtils.isEmpty(applicableStaffIdList)) {
            return Map.of();
        }

        return getStaffFirstAvailableDateNew(
                        buildOBTimeSlotDTOForApplicableStaffNew(obBusinessStaffDTO, applicableStaffIdList))
                .stream()
                .collect(Collectors.toMap(
                        StaffFirstAvailableDateDTO::getStaffId,
                        StaffFirstAvailableDateDTO::getFirstAvailableDate,
                        (a, b) -> a));
    }

    /**
     * 如果某个 staff 对所有 pet 都 applicable，构造该 staff 的 OBTimeSlotDTO
     * 返回的 map 中，key 只包含对所有 pet 都 applicable 的 staffId
     */
    private static OBTimeSlotDTO buildOBTimeSlotDTOForApplicableStaffNew(
            OBBusinessStaffDTO obBusinessStaffDTO, List<Integer> applicableStaffIdList) {
        OBTimeSlotSimpleDTO timeSlotParam = obBusinessStaffDTO.getTimeSlotParam();
        List<SelectedPetServiceDTO> petServiceList = obBusinessStaffDTO.getPetServiceList();

        // note: allServiceIdList 不能去重，因为需要累加 duration
        List<Integer> allServiceIdList = petServiceList.stream()
                .flatMap(list -> list.getServiceIdList().stream())
                .collect(Collectors.toList());

        Map<Integer /* pet index */, List<Integer>> petServices = petServiceList.stream()
                .filter(service -> service.getPetDataDTO().getPetIndex() != null)
                .collect(Collectors.toMap(
                        selectedPetServiceDTO ->
                                selectedPetServiceDTO.getPetDataDTO().getPetIndex(),
                        SelectedPetServiceDTO::getServiceIdList));

        OBTimeSlotDTO dto = new OBTimeSlotDTO();

        dto.setServiceIds(allServiceIdList);
        dto.setPetServices(petServices);
        dto.setStaffIdList(applicableStaffIdList);

        dto.setCustomerId(obBusinessStaffDTO.getCustomerId());
        dto.setBusinessId(obBusinessStaffDTO.getBusinessId());
        dto.setObName(obBusinessStaffDTO.getObName());
        dto.setDate(timeSlotParam.getStartDate());
        dto.setCount(1); // 只查可用的第一天
        dto.setLat(timeSlotParam.getLat());
        dto.setLng(timeSlotParam.getLng());
        dto.setZipcode(timeSlotParam.getZipcode());
        dto.setQueryPerHalfDay(null);
        dto.setQueryEndOfTheMonth(null);
        dto.setQueryLimitDays(60);
        dto.setOverLimitDaysSeconds(10);
        dto.setQueryCountPerStaff(1);
        List<OBPetDataDTO> petParamList = obBusinessStaffDTO.getPetServiceList().stream()
                .map(SelectedPetServiceDTO::getPetDataDTO)
                .collect(Collectors.toList());
        dto.setPetParamList(petParamList);

        return dto;
    }

    public List<StaffFirstAvailableDateDTO> getStaffFirstAvailableDateNew(OBTimeSlotDTO obTimeSlotDTO) {
        if (CollectionUtils.isEmpty(obTimeSlotDTO.getStaffIdList())) {
            MoeBusinessBookOnline setting = businessService.getSettingInfoByBusinessId(obTimeSlotDTO.getBusinessId());
            List<MoeStaffDto> staffList = businessStaffService.getOBAvailableStaffList(
                    obTimeSlotDTO.getBusinessId(), setting.getAvailableTimeType());
            if (CollectionUtils.isEmpty(staffList)) {
                return List.of();
            }
            obTimeSlotDTO.setStaffIdList(
                    staffList.stream().map(MoeStaffDto::getId).toList());
        }

        Map<String, OBAvailableTimeDto> timeSlotList;
        if (obClientTimeSlotService.isMultiPetNewFlow(obTimeSlotDTO.getBusinessId())) {
            timeSlotList =
                    obClientTimeSlotService.getTimeSlotListV2(obTimeSlotDTO).getAvailableDateTimes();
        } else {
            timeSlotList = obClientTimeSlotService.getTimeSlotList(obTimeSlotDTO);
        }
        if (CollectionUtils.isEmpty(timeSlotList)) {
            return List.of();
        }
        Map<String, OBAvailableTimeDto> map = timeSlotList.entrySet().stream()
                .filter(entry -> {
                    Boolean[] available = entry.getValue().getAvailable();
                    return BooleanUtils.isTrue(available[0]) || BooleanUtils.isTrue(available[1]);
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (CollectionUtils.isEmpty(map)) {
            return List.of();
        }

        return convertToStaffFirstAvailableDateList(map);
    }

    @NotNull
    private static List<StaffFirstAvailableDateDTO> convertToStaffFirstAvailableDateList(
            final Map<String, OBAvailableTimeDto> map) {
        Map<Integer, LocalDate> staffToFirstAvailableDate = new HashMap<>(16);

        map.forEach((dateStr, availableTimeDto) -> {
            LocalDate currentDate = LocalDate.parse(dateStr);
            availableTimeDto
                    .getStaffList()
                    .forEach(staffDto -> staffToFirstAvailableDate.compute(
                            staffDto.getId(),
                            (id, existingDate) -> existingDate == null || currentDate.isBefore(existingDate)
                                    ? currentDate
                                    : existingDate));
        });

        return staffToFirstAvailableDate.entrySet().stream()
                .map(entry -> {
                    var dto = new StaffFirstAvailableDateDTO();
                    dto.setStaffId(entry.getKey());
                    dto.setFirstAvailableDate(entry.getValue().toString());
                    return dto;
                })
                .toList();
    }
}
