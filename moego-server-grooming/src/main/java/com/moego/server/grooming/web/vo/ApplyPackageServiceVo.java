package com.moego.server.grooming.web.vo;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ApplyPackageServiceVo {

    @NotNull
    private Integer invoiceId;

    private Integer packageServiceId;
    private Integer quantity;

    private Boolean checkRefund = false;

    /**
     * 现在一个 package service 可能有多个 service id，这个接口只删除一个 apply 记录，所以需要指定 service id
     */
    @Nullable
    private Integer serviceId;
}
