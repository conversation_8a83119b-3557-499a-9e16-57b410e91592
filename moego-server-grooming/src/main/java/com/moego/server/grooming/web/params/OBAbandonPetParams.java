package com.moego.server.grooming.web.params;

import com.moego.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import org.hibernate.validator.constraints.URL;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @since 2023/5/10
 */
public record OBAbandonPetParams(
        @Schema(description = "existing pet id") @Min(0) Integer petId,
        @Schema(description = "pet name") @Size(max = 50) String petName,
        @Schema(description = "1-dog, 2-cat, 11-other") Integer petTypeId,
        @Schema(description = "pet breed name") @Size(max = 50) String breed,
        @Schema(description = "pet weight") @Size(max = 50) String weight,
        @Schema(description = "pet image") @URL @Size(max = 255) String avatarPath,
        @Schema(description = "fixed name") @Size(max = 50) String fixed,
        @Schema(description = "1-male, 2-female") Byte gender,
        @Schema(description = "hair length name") @Size(max = 50) String hairLength,
        @Schema(description = "behavior name") @Size(max = 50) String behavior,
        @Schema(description = "pet birthday date, yyyy-MM-dd") @DateTimeFormat(pattern = DateUtil.STANDARD_DATE)
                String birthday,
        @Schema(description = "vaccine list") @Valid List<OBAbandonPetVaccineParams> vaccineList,
        @Schema(description = "selected service id") @Min(0) Integer serviceId,
        @Schema(description = "selected add-on id list") @Valid List<@NotNull Integer> addOnIds,
        @Schema(description = "vet address") @Size(max = 255) String vetAddress,
        @Schema(description = "vet name") @Size(max = 255) String vetName,
        @Schema(description = "vet phone number") @Size(max = 255) String vetPhone,
        @Schema(description = "emergency contact name") @Size(max = 255) String emergencyContactName,
        @Schema(description = "emergency contact phone number") @Size(max = 255) String emergencyContactPhone,
        @Schema(description = "health issues") String healthIssues,
        @Schema(description = "custom question answers, key:custom_questionID, value: answer")
                Map<String, String> petQuestionAnswers) {
    public record OBAbandonPetVaccineParams(
            @Schema(description = "vaccine id") @NotNull Integer vaccineId,
            @Schema(description = "upload vaccine document url") @URL String vaccineDocument,
            @Valid @Schema(description = "upload multi vaccine document url") List<@NotNull @URL String> documentUrls,
            @Schema(description = "vaccine expiration date") @DateTimeFormat(pattern = DateUtil.STANDARD_DATE)
                    String expirationDate) {}
}
