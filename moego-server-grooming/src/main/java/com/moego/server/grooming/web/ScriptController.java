package com.moego.server.grooming.web;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static java.util.Comparator.comparing;

import com.google.common.collect.Lists;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.AppointmentTaskServiceGrpc;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderRequest;
import com.moego.idl.service.appointment.v1.SyncAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.UpdatePetDetailRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.BookingCareTypeServiceGrpc;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.InitBookingCareTypesBatchRequest;
import com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc;
import com.moego.idl.service.online_booking.v1.UpdateAcceptedCustomerSettingRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.dto.CustomerFilterResult;
import com.moego.server.customer.dto.CustomerSearchVo;
import com.moego.server.customer.dto.PhoneNumberEmailDto;
import com.moego.server.customer.params.CustomerSearchStatusVo;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeBusinessBookOnlineMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.service.BookOnlineAcceptPetTypeService;
import com.moego.server.grooming.service.MoeGroomingBookOnlineService;
import com.moego.server.grooming.service.MoeGroomingQuestionService;
import com.moego.server.grooming.service.ob.OBLandingPageConfigService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RequestMapping("/scripts")
@RestController
@RequiredArgsConstructor
@Hidden
public class ScriptController {

    private static final Logger log = LoggerFactory.getLogger(ScriptController.class);

    private final AppointmentMapperProxy appointmentMapper;
    private final PetDetailMapperProxy petDetailMapper;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessStub;
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailStub;

    private final MoeBusinessBookOnlineMapper moeBusinessBookOnlineMapper;
    private final ICustomerCustomerService iCustomerCustomerClient;
    private final CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;
    private final OBAvailabilitySettingServiceGrpc.OBAvailabilitySettingServiceBlockingStub
            obAvailabilitySettingServiceBlockingStub;
    private final AppointmentTaskServiceGrpc.AppointmentTaskServiceBlockingStub appointmentTaskStub;

    private final JdbcTemplate jdbcTemplate;

    private final BookingCareTypeServiceGrpc.BookingCareTypeServiceBlockingStub bookingCareTypeServiceStub;

    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final MoeGroomingBookOnlineService moeGroomingBookOnlineService;
    private final MoeGroomingQuestionService moeGroomingQuestionService;
    private final OBLandingPageConfigService landingPageConfigService;
    private final BookOnlineAcceptPetTypeService bookOnlineAcceptPetTypeService;

    @Resource(name = "developScriptExecutorService")
    private ExecutorService executorService;

    private static final List<Integer> needFixedAppointmentIds = getNeedFixedAppointmentIds();

    /**
     * 修复 service by staff 数据
     */
    @GetMapping("/fix-sbs-data/{appointmentId}")
    @Auth(AuthType.ANONYMOUS)
    public void fixServiceByStaffData(@PathVariable("appointmentId") int appointmentId) {

        if (appointmentId > 0) {
            if (!needFixedAppointmentIds.contains(appointmentId)) {
                throw bizException(Code.CODE_PARAMS_ERROR, "this appointmentId does not need to be fixed");
            }

            fixAppointment(appointmentId);

            return;
        }

        for (int apptId : needFixedAppointmentIds) {
            try {
                fixAppointment(apptId);
            } catch (Exception e) {
                log.error("fix sbs error, appointmentId: {}", apptId, e);
            }
        }
    }

    private void fixAppointment(int apptId) {

        var appointment = appointmentMapper.selectByPrimaryKey(apptId);

        log.info("fix sbs, appointment: {}", JsonUtil.toJson(appointment));

        var e = new MoeGroomingPetDetailExample();
        e.createCriteria().andGroomingIdEqualTo(apptId).andStatusEqualTo((byte) 1);

        var petDetails = petDetailMapper.selectByExample(e);

        var servicePetDetails = petDetails.stream()
                .filter(p -> p.getServiceType() == 1)
                .sorted(comparing(MoeGroomingPetDetail::getStartTime))
                .toList();

        log.info("fix sbs, servicePetDetails: {}", JsonUtil.toJson(servicePetDetails));

        var updatePetDetailRequestList = new ArrayList<UpdatePetDetailRequest>();

        for (int i = 1; i < servicePetDetails.size(); i++) {
            var prev = servicePetDetails.get(i - 1);
            var curr = servicePetDetails.get(i);

            var startTime = prev.getEndTime().intValue();
            var endTime = startTime + curr.getServiceTime();

            // service 向后偏移
            curr.setStartTime((long) startTime);
            curr.setEndTime((long) endTime);

            updatePetDetailRequestList.add(UpdatePetDetailRequest.newBuilder()
                    .setId(curr.getId())
                    .setStartTime(startTime)
                    .setEndTime(endTime)
                    .build());
        }

        var addonPetDetails = petDetails.stream()
                .filter(p -> p.getServiceType() == 2)
                .sorted(comparing(MoeGroomingPetDetail::getStartTime))
                .toList();

        log.info("fix sbs, addonPetDetails: {}", JsonUtil.toJson(addonPetDetails));

        var customizedAddonList = listCustomizedAddon(appointment, addonPetDetails);

        for (int i = 0; i < addonPetDetails.size(); i++) {
            var prev = i == 0 ? servicePetDetails.get(servicePetDetails.size() - 1) : addonPetDetails.get(i - 1);
            var curr = addonPetDetails.get(i);

            var customizedAddon =
                    findCustomizedService(customizedAddonList, curr.getServiceId(), curr.getPetId(), curr.getStaffId());
            if (customizedAddon == null) {
                continue;
            }

            var startTime = prev.getEndTime().intValue();
            var endTime = startTime + customizedAddon.getDuration();

            // addon 向后偏移
            curr.setStartTime((long) startTime);
            curr.setEndTime((long) endTime);

            updatePetDetailRequestList.add(UpdatePetDetailRequest.newBuilder()
                    .setId(curr.getId())
                    .setStartTime(startTime)
                    .setServiceTime(customizedAddon.getDuration())
                    .setServicePrice(customizedAddon.getPrice())
                    .setEndTime(endTime)
                    .build());
        }

        for (var updatePetDetailRequest : updatePetDetailRequestList) {

            petDetailStub.updatePetDetail(updatePetDetailRequest);

            log.info("fix sbs, updated petDetail: {}", JsonUtil.toJson(updatePetDetailRequest));
        }
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedAddon(
            MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> addonPetDetails) {
        var builder = BatchGetCustomizedServiceRequest.newBuilder();
        builder.setCompanyId(appointment.getCompanyId());
        for (var addonPetDetail : addonPetDetails) {
            var b = CustomizedServiceQueryCondition.newBuilder();
            b.setServiceId(addonPetDetail.getServiceId());
            b.setBusinessId(appointment.getBusinessId());
            if (isNormal(addonPetDetail.getStaffId())) {
                b.setStaffId(addonPetDetail.getStaffId());
            }
            if (isNormal(addonPetDetail.getPetId())) {
                b.setPetId(addonPetDetail.getPetId());
            }
            builder.addQueryConditionList(b.build());
        }
        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    private static CustomizedServiceView findCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            int serviceId,
            @Nullable Integer petId,
            @Nullable Integer staffId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();
                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId())
                            && (!isNormal(staffId) && !isNormal(cond.getStaffId())
                                    || isNormal(staffId) && staffId == cond.getStaffId());
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    private static List<Integer> getNeedFixedAppointmentIds() {
        return List.of(
                32321819, 32321820, 32321821, 32322216, 32322217, 32322218, 32322219, 32322220, 32700632, 32700633,
                32700634, 32700669, 32700670, 32700671, 32700672, 32700830, 32700831, 32700879, 32700880, 32700881,
                32700882, 35078997, 35078998, 35078999, 35079000, 35079001, 35079529, 35079530, 35079531, 35079532,
                35079533, 35092670, 35106475, 35108758, 35108759, 35108760, 35108762, 35399677, 35399678, 35399679,
                35399680, 35819300, 35819301, 35819302, 35819303, 35884862, 35884863, 35884864, 35884865, 35884896,
                35884897, 35884898, 35884899, 35898428, 35898429, 35898430, 35898431, 35936376, 35936377, 35936455,
                35936456, 35936457, 35942448, 35942449, 35942450, 35942537, 35942539, 36129604, 36129605, 36129606,
                36129983, 36129984, 36129985, 36129986, 36131662, 36131663, 36131664, 36131867, 36131869, 36131870,
                36131871, 36254917, 36254918, 36254919, 36254920, 36535465, 36535515, 36535536, 36755549, 36755550,
                36755551, 37411029, 37411030, 40548371, 41277319, 41277320, 41277321, 41277322, 43554773, 43554774,
                43554775, 43554776, 43604235, 44311856, 44311857, 44311858, 44311859, 46108750, 46108757, 46108759,
                46220956, 46220957, 46220958, 47995685, 50772008, 50772009, 50772010, 50772011, 50880849, 50880850,
                50880851, 50914818, 50914819, 50914820, 50914821, 50914822, 50915841, 50915843, 50915844, 50915845,
                50915846, 50916023, 50916025, 50916026, 50916027, 51689098, 51850562, 52659141, 52721359, 52768244,
                52805243, 52948495, 53113105, 53172270);
    }

    /**
     * 1. 迁移 moe_grooming.moe_business_book_online 中的 accept_client 数据到 moego_online_booking.accept_customer_setting 表
     * 2. 迁移 moe_customer.moe_business_customer 中的 is_block_online_booking 数据到 moego_online_booking.block_customer 表
     */
    @GetMapping("/migrateOnlineBookingSetting")
    @Auth(AuthType.ANONYMOUS)
    public void migrateOnlineBookingSetting(@RequestParam(required = false) Integer businessId) {
        if (Objects.nonNull(businessId)) {
            MoeBusinessBookOnline moeBusinessBookOnline = moeBusinessBookOnlineMapper.selectByBusinessId(businessId);
            if (Objects.nonNull(moeBusinessBookOnline)) {
                migrateAcceptClient(moeBusinessBookOnline);
                migrateBlockClient(moeBusinessBookOnline);
            }
            return;
        }

        List<MoeBusinessBookOnline> businessBookOnlineList = moeBusinessBookOnlineMapper.listAll();
        businessBookOnlineList.forEach(bookOnline -> executorService.execute(() -> migrateAcceptClient(bookOnline)));

        businessBookOnlineList.stream()
                .collect(Collectors.groupingBy(MoeBusinessBookOnline::getCompanyId))
                .forEach((key, value) -> {
                    if (CollectionUtils.isEmpty(value)) {
                        return;
                    }
                    executorService.execute(() -> migrateBlockClient(value.get(0)));
                });
    }

    private void migrateAcceptClient(MoeBusinessBookOnline bookOnline) {
        // accept_client
        AcceptCustomerType acceptCustomerType =
                AcceptCustomerType.forNumber(bookOnline.getAcceptClient().intValue());
        obAvailabilitySettingServiceBlockingStub.updateAcceptedCustomerSetting(
                UpdateAcceptedCustomerSettingRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(bookOnline.getCompanyId())
                                .setBusinessId(bookOnline.getBusinessId())
                                .build())
                        .setAcceptCustomerType(acceptCustomerType)
                        .addAllServiceItemTypes(
                                List.of(ServiceItemType.GROOMING, ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                        .build());
    }

    private void migrateBlockClient(MoeBusinessBookOnline bookOnline) {
        // is_block_online_booking
        // FilterParams filterParams = FilterParams.builder()
        //         .type(TypeEnum.TYPE_AND)
        //         .filters(List.of(FilterParams.builder()
        //                 .property(PropertyEnum.client_status)
        //                 .operator(OperatorEnum.OPERATOR_EQUAL)
        //                 .value("blocked_from_ob")
        //                 .build()))
        //         .build();
        // ClientListParams clientListParams = ClientListParams.builder()
        //         .filters(filterParams)
        //         .pageNum(1)
        //         .pageSize(1000)
        //         .build();
        // CustomerFilterResult filterResult = iCustomerCustomerClient.getSmartClientListV2(
        //         new ClientListRequest(bookOnline.getCompanyId(), bookOnline.getBusinessId(), null,
        // clientListParams));

        Integer pageSize = 5000;
        CustomerSearchVo searchVo = new CustomerSearchVo();
        searchVo.setPageNum(1);
        searchVo.setPageSize(pageSize);
        CustomerSearchStatusVo customerSearchStatusVo = new CustomerSearchStatusVo();
        customerSearchStatusVo.setBlockOnlineBooking(true);
        searchVo.setStatusSetting(customerSearchStatusVo);
        CustomerFilterResult filterResult = iCustomerCustomerClient.getClientList(bookOnline.getBusinessId(), searchVo);

        List<Long> blockedCustomerIds = filterResult.getPhoneNumberMap().values().stream()
                .map(PhoneNumberEmailDto::getCustomerId)
                .distinct()
                .map(Integer::longValue)
                .toList();
        if (blockedCustomerIds.isEmpty()) {
            return;
        }
        customerAvailabilityServiceBlockingStub.setCustomerBlockStatus(
                com.moego.idl.service.online_booking.v1.SetCustomerBlockStatusRequest.newBuilder()
                        .setCompanyId(bookOnline.getCompanyId())
                        .addAllServiceItemTypes(
                                List.of(ServiceItemType.GROOMING, ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                        .addAllCustomerIds(blockedCustomerIds)
                        .setNeedBlock(true)
                        .build());
    }

    @GetMapping("/updateDateType")
    @Auth(AuthType.ANONYMOUS)
    public void updateDateType(@RequestParam(required = false) Integer companyId) {
        Set<Integer> companyIds = new HashSet<>();
        if (Objects.nonNull(companyId)) {
            companyIds.add(companyId);
        } else {
            companyIds = Set.of(
                    119002, 118810, 119040, 119026, 117069, 110981, 108786, 115846, 656, 103982, 109556, 111774, 101683,
                    113531, 101014, 105869, 1727, 116659, 116340, 6048, 118511, 103106, 109380, 103440, 115602, 106186,
                    7546, 119332, 6609, 119546, 119550, 116921, 105058, 110874, 118282, 119562, 101741, 115271, 111610,
                    7044, 119396, 112966, 102632, 116257, 111642, 118612, 116083, 116732, 6631, 111449, 118949, 118828,
                    100449, 117386, 112713, 118950, 118163, 118466, 103465, 118719, 110309, 111732, 118997, 7026,
                    112714, 118939, 108926, 107012, 5379, 116889, 109612, 118904, 112619, 1260, 109893, 110764, 118787,
                    118521, 117133, 113388, 111480, 111463, 101064, 118608, 102976, 102888, 108495, 110239, 112970,
                    108467, 119102, 119376, 119105, 119446, 10096, 105809, 103851, 119162, 9219, 119220, 119221, 119236,
                    100000, 114602, 119255, 119259, 100539, 111195, 112175, 119397, 6613, 119646, 119398, 119705,
                    119623, 119753, 119036, 119037, 119038, 115687, 119035, 115482, 119795, 113061, 119473, 117934,
                    119874, 117097, 119883, 109280, 119856, 119855, 113322, 104507, 119915, 119111, 119919, 117789,
                    120045, 120039);
        }

        String sql =
                """
                select id
                from moe_grooming.moe_grooming_appointment mga
                where appointment_date >= '2024-03-01'
                  and company_id in (:companyIds)
                  and service_type_include in (2, 3, 4, 5, 6, 7)
                  and is_waiting_list = 0
                  and is_block = 2
                """;

        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("companyIds", companyIds);

        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
        List<Integer> appointmentIds = namedParameterJdbcTemplate.query(sql, params, (rs, rowNum) -> rs.getInt("id"));

        if (CollectionUtils.isEmpty(appointmentIds)) {
            return;
        }

        String updateSql =
                """
                update moe_grooming.moe_grooming_pet_detail
                set date_type = case
                    when start_time = 0 and specific_dates = '[]' then 1
                    when specific_dates != '[]' then 2
                    else date_type
                    end
                where grooming_id = :appointmentId
                  and status = 1
                  and start_date = ''
                """;

        Lists.partition(appointmentIds, 100).forEach(ids -> {
            MapSqlParameterSource[] batchParams = ids.stream()
                    .map(id -> new MapSqlParameterSource("appointmentId", id))
                    .toArray(MapSqlParameterSource[]::new);

            executorService.execute(() -> {
                // Execute batch update
                namedParameterJdbcTemplate.batchUpdate(updateSql, batchParams);
                log.info("updateDateType, appointmentIds: {}", ids);
            });
        });
    }

    @GetMapping("/syncAppointmentTask")
    @Auth(AuthType.ANONYMOUS)
    public void syncAppointmentTask(
            @RequestParam Integer startAppointmentId,
            @RequestParam Integer endAppointmentId,
            @RequestParam(required = false) Integer threadCount,
            @RequestParam(required = false) String startDate) {
        var builder = SyncAppointmentTaskRequest.newBuilder()
                .setStartAppointmentId(startAppointmentId)
                .setEndAppointmentId(endAppointmentId);
        if (threadCount != null) {
            builder.setThreadCount(threadCount);
        }
        if (startDate != null) {
            builder.setSyncStartDate(startDate);
        }
        appointmentTaskStub.syncAppointmentTask(builder.build());
    }

    @GetMapping("/initBookingCareTypesBatch")
    @Auth(AuthType.ANONYMOUS)
    public void initBookingCareTypesBatch() {
        // 获取 company id 和 business ids
        List<BusinessCompanyPO> businessCompanyPOS = moeBusinessBookOnlineMapper.selectAllCompanyIdBusinessId();

        if (CollectionUtils.isEmpty(businessCompanyPOS)) {
            log.info("initBookingCareTypesBatch: No enabled online booking businesses found");
            return;
        }

        Map<Long, List<Long>> companyBusinessMap = businessCompanyPOS.stream()
                .collect(Collectors.groupingBy(
                        BusinessCompanyPO::getCompanyId,
                        Collectors.mapping(po -> po.getBusinessId().longValue(), Collectors.toList())));

        log.info(
                "initBookingCareTypesBatch: Processing {} companies with {} total businesses",
                companyBusinessMap.size(),
                businessCompanyPOS.size());

        var requestBuilder = InitBookingCareTypesBatchRequest.newBuilder();

        companyBusinessMap.forEach((companyId, businessIds) -> {
            var businessIdsBuilder =
                    InitBookingCareTypesBatchRequest.BusinessIds.newBuilder().addAllBusinessIds(businessIds);
            requestBuilder.putCompanyIdBusinessIds(companyId, businessIdsBuilder.build());
        });

        bookingCareTypeServiceStub.initBookingCareTypesBatch(requestBuilder.build());
        log.info(
                "initBookingCareTypesBatch: Successfully initiated batch initialization for {} companies",
                companyBusinessMap.size());
    }

    @GetMapping("/previewEstimateOrder")
    @Auth(AuthType.ANONYMOUS)
    public void previewEstimateOrder() {
        var idToBusinessId = new HashMap<Integer, Integer>();
        idToBusinessId.put(80193904, 101112);
        idToBusinessId.put(81069698, 102976);
        idToBusinessId.put(83143473, 102976);
        idToBusinessId.put(69848709, 109883);
        idToBusinessId.put(74652117, 111737);
        idToBusinessId.put(79572641, 111737);
        idToBusinessId.put(76717146, 113097);
        idToBusinessId.put(77247652, 116085);
        idToBusinessId.put(81780656, 116596);
        idToBusinessId.put(79339667, 117192);
        idToBusinessId.put(76984790, 117192);
        idToBusinessId.put(70877620, 117192);
        idToBusinessId.put(78670035, 117192);
        idToBusinessId.put(76585072, 117192);
        idToBusinessId.put(69596039, 117192);
        idToBusinessId.put(80613404, 117192);
        idToBusinessId.put(74572119, 121214);
        idToBusinessId.put(78821429, 121214);
        idToBusinessId.put(74840616, 121214);
        idToBusinessId.put(82815530, 121214);
        idToBusinessId.put(81711455, 121214);
        idToBusinessId.put(82721285, 121214);
        idToBusinessId.put(67995872, 121214);
        idToBusinessId.put(80163936, 121214);
        idToBusinessId.put(66412184, 121214);
        idToBusinessId.put(66404779, 121214);
        idToBusinessId.put(81547626, 121214);
        idToBusinessId.put(74843243, 121214);
        idToBusinessId.put(78128956, 121214);
        idToBusinessId.put(80961934, 121214);
        idToBusinessId.put(75546989, 121214);
        idToBusinessId.put(76695761, 121214);
        idToBusinessId.put(80849856, 121214);
        idToBusinessId.put(81791017, 121214);
        idToBusinessId.put(76868204, 121299);
        idToBusinessId.put(81818198, 121387);
        idToBusinessId.put(73920765, 121425);
        idToBusinessId.put(75467166, 121425);
        idToBusinessId.put(77703469, 121425);
        idToBusinessId.put(76887747, 121425);
        idToBusinessId.put(75430221, 121425);
        idToBusinessId.put(80237723, 121425);
        idToBusinessId.put(79366010, 121643);
        idToBusinessId.put(81988324, 122030);
        idToBusinessId.put(79448137, 122087);
        idToBusinessId.put(80938831, 122087);
        idToBusinessId.put(79449992, 122138);
        idToBusinessId.put(79449945, 122138);
        idToBusinessId.put(79449955, 122138);
        idToBusinessId.put(80240271, 122138);
        idToBusinessId.put(76190932, 122397);
        idToBusinessId.put(72068775, 122397);
        idToBusinessId.put(75036442, 122397);
        idToBusinessId.put(75036499, 122397);
        idToBusinessId.put(78233665, 122600);
        idToBusinessId.put(78824811, 122600);
        idToBusinessId.put(80126450, 122600);
        idToBusinessId.put(76875854, 122600);
        idToBusinessId.put(73000623, 122600);
        idToBusinessId.put(82588355, 122600);
        idToBusinessId.put(83114006, 122726);
        idToBusinessId.put(73822720, 122726);
        idToBusinessId.put(76826058, 122726);
        idToBusinessId.put(76150016, 122726);
        idToBusinessId.put(74839026, 122726);
        idToBusinessId.put(76232606, 122726);
        idToBusinessId.put(79795796, 122726);
        idToBusinessId.put(77376318, 122726);
        idToBusinessId.put(80612094, 122726);
        idToBusinessId.put(78194737, 122726);
        idToBusinessId.put(80945554, 122726);
        idToBusinessId.put(77194018, 122726);
        idToBusinessId.put(76243259, 122726);
        idToBusinessId.put(76236587, 122726);
        idToBusinessId.put(73822759, 122726);
        idToBusinessId.put(75556940, 122726);
        idToBusinessId.put(78540968, 122726);
        idToBusinessId.put(77369476, 122726);
        idToBusinessId.put(73822744, 122726);
        idToBusinessId.put(76177426, 122726);
        idToBusinessId.put(77287688, 122726);
        idToBusinessId.put(75450091, 122726);
        idToBusinessId.put(77654991, 122726);
        idToBusinessId.put(81222620, 122726);
        idToBusinessId.put(74827674, 122726);
        idToBusinessId.put(78657744, 122726);
        idToBusinessId.put(80328140, 122726);
        idToBusinessId.put(78203529, 122726);
        idToBusinessId.put(78645603, 122726);
        idToBusinessId.put(75435376, 122726);
        idToBusinessId.put(74823472, 122726);
        idToBusinessId.put(79254779, 122726);
        idToBusinessId.put(74839053, 122726);
        idToBusinessId.put(74839023, 122726);
        idToBusinessId.put(79558906, 122726);
        idToBusinessId.put(74822627, 122726);
        idToBusinessId.put(77416215, 122726);
        idToBusinessId.put(80121871, 122726);
        idToBusinessId.put(79557814, 122726);
        idToBusinessId.put(79062740, 123074);
        idToBusinessId.put(78155298, 123468);
        idToBusinessId.put(78216804, 123468);
        idToBusinessId.put(78631925, 123569);

        Map<Long, List<Long>> map = new HashMap<>();

        for (var entry : idToBusinessId.entrySet()) {
            var appointmentId = entry.getKey();
            var businessId = entry.getValue();
            map.computeIfAbsent(businessId.longValue(), k -> new ArrayList<>()).add(appointmentId.longValue());
        }

        map.forEach((businessId, appointmentIds) -> {
            var companyId = businessStub
                    .getCompanyId(GetCompanyIdRequest.newBuilder()
                            .setBusinessId(businessId)
                            .build())
                    .getCompanyId();

            var response = appointmentStub.previewEstimateOrder(PreviewEstimateOrderRequest.newBuilder()
                    .setCompanyId(companyId)
                    .setBusinessId(businessId)
                    .addAllAppointmentIds(appointmentIds)
                    .build());

            for (var estimatedOrder : response.getEstimatedOrdersList()) {
                log.info(
                        "previewEstimatedOrder, businessId: {}, appointmentId: {}, estimatedOrder: {}",
                        businessId,
                        estimatedOrder.getAppointmentId(),
                        estimatedOrder);
            }
        });
    }

    @GetMapping("/fixObSettingData/{businessId}")
    @Auth(AuthType.ANONYMOUS)
    public void fixOBSettingDataByBusinessId(@PathVariable("businessId") int businessId) {
        if (businessId > 0) {
            initPartialOBSettingData(businessId);
            return;
        }

        List<Integer> needFixOBSettingBusinessIds = List.of(
                101577, 118495, 123987, 124058, 124101, 124106, 124150, 124190, 124200, 124309, 124316, 124346, 124392,
                124443, 124484, 124511, 124541, 124544, 124567);

        for (int bizId : needFixOBSettingBusinessIds) {
            try {
                initPartialOBSettingData(bizId);
            } catch (Exception e) {
                log.error("fix ob setting error, businessId: {}", bizId, e);
            }
        }
    }

    private void initPartialOBSettingData(int businessId) {
        var companyId = businessStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build())
                .getCompanyId();

        var infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(businessId);
        var businessDto = iBusinessBusinessClient.getBusinessInfoWithOwnerEmail(infoIdParams);
        var bookOnlineName = moeGroomingBookOnlineService.generateBookOnlineName(businessDto);

        // 支持重复初始化
        executeInitializationStep(
                "Booking question",
                () -> moeGroomingQuestionService.initBookingQuestionForBusiness(businessId, companyId));

        executeInitializationStep(
                "Business profile",
                () -> moeGroomingBookOnlineService.initBusinessProfile(businessId, companyId, businessDto));

        executeInitializationStep(
                "Landing page config",
                () -> landingPageConfigService.initializeLandingPageConfig(businessDto, bookOnlineName));

        executeInitializationStep(
                "Business notification", () -> moeGroomingBookOnlineService.initBusinessNotification(businessId));

        executeInitializationStep(
                "Accept pet type", () -> bookOnlineAcceptPetTypeService.initAcceptPetTypeList(companyId, businessId));
    }

    private void executeInitializationStep(String stepName, Runnable initStep) {
        try {
            initStep.run();
        } catch (Exception e) {
            log.error("OB init [{}] failed", stepName, e);
        }
    }
}
