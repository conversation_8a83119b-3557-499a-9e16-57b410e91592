package com.moego.server.grooming.helper;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyModel;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.service.organization.v1.BatchGetCompanyIdRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.DateTimeServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyCurrentDayAndTimeRequest;
import com.moego.idl.service.organization.v1.GetCompanyCurrentDayAndTimeResponse;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.idl.service.organization.v1.ListCompanyRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdListParams;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class CompanyHelper {
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;
    private final DateTimeServiceGrpc.DateTimeServiceBlockingStub dateTimeService;
    private final IBusinessStaffClient iBusinessStaffClient;

    private final LoadingCache<Long, Long> businessIdToCompanyId = Caffeine.newBuilder()
            .maximumSize(10_000)
            .expireAfterWrite(Duration.ofMinutes(1))
            .build(this::fetchCompanyId);

    public boolean isMultiLocation(Long companyId) {
        return businessServiceBlockingStub
                        .getLocationList(GetLocationListRequest.newBuilder()
                                .setTokenCompanyId(companyId)
                                .build())
                        .getLocationCount()
                > 1;
    }

    public GetCompanyCurrentDayAndTimeResponse getCompanyDateTime(Long companyId) {
        return dateTimeService.getCompanyCurrentDayAndTime(GetCompanyCurrentDayAndTimeRequest.newBuilder()
                .setCompanyId(companyId)
                .build());
    }

    CompanyPreferenceSettingModel getCompanyPreferenceSetting(long companyId) {
        return companyService
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting();
    }

    public String getCompanyTimeZoneName(Long companyId) {
        return getCompanyPreferenceSetting(companyId).getTimeZone().getName();
    }

    public Map<Integer, MoeStaffDto> getBusinessStaff(Integer businessId) {
        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(businessId);
        return iBusinessStaffClient.getStaffList(staffIdListParams).stream()
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity(), (a, b) -> a));
    }

    public Map<Integer, MoeStaffDto> getStaffInfoMap(Integer businessId, List<Integer> staffIds) {
        staffIds = staffIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(staffIds)) {
            return Map.of();
        }

        StaffIdListParams staffIdListParams = new StaffIdListParams();
        staffIdListParams.setBusinessId(businessId);
        staffIdListParams.setStaffIdList(staffIds);
        return iBusinessStaffClient.getStaffList(staffIdListParams).stream()
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity(), (k1, k2) -> k1));
    }

    Long fetchCompanyId(long businessId) {
        return businessServiceBlockingStub
                .batchGetCompanyId(BatchGetCompanyIdRequest.newBuilder()
                        .addBusinessIds(businessId)
                        .build())
                .getBusinessCompanyIdMapMap()
                .get(businessId);
    }

    /**
     * Get company id by business id, throw BizException if not found
     *
     * @param businessId business id
     * @return company id
     */
    public long mustGetCompanyId(long businessId) {
        var companyId = businessIdToCompanyId.get(businessId);
        if (companyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company not found for businessId: " + businessId);
        }

        return companyId;
    }

    /**
     * Must get company by id, throw exception if not found
     *
     * @param companyId company id
     * @return company
     */
    public CompanyModel mustGetCompany(long companyId) {
        var companies = companyService
                .listCompany(ListCompanyRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1)
                                .build())
                        .build())
                .getCompaniesList();
        if (companies.isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company not found: " + companyId);
        }
        return companies.get(0);
    }
}
