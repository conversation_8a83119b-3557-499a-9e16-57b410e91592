package com.moego.server.grooming.service;

import static com.moego.common.enums.groomingreport.GroomingReportConst.BODY_VIEW_LEFT_CHOICE_PREFIX;
import static com.moego.common.enums.groomingreport.GroomingReportConst.BODY_VIEW_RIGHT_CHOICE_PREFIX;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SAMPLE_APPOINTMENT_START_TIME;
import static com.moego.common.enums.groomingreport.GroomingReportConst.SHARE_UUID_PREFIX;
import static com.moego.server.grooming.service.utils.GroomingReportConverter.buildBusinessInfo;
import static com.moego.server.grooming.service.utils.GroomingReportConverter.buildGroomingInfo;
import static com.moego.server.grooming.service.utils.GroomingReportConverter.buildPetInfo;
import static com.moego.server.grooming.service.utils.GroomingReportConverter.buildReportInfo;
import static com.moego.server.grooming.service.utils.GroomingReportConverter.buildReviewBoosterConfig;
import static com.moego.server.grooming.service.utils.GroomingReportConverter.buildReviewBoosterRecord;
import static com.moego.server.grooming.service.utils.GroomingReportConverter.convertQuestionDTO;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.getSendingMethodForDB;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.toDate;
import static com.moego.server.grooming.service.utils.GroomingReportUtil.toTimestamp;

import com.moego.backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsRequest;
import com.moego.backend.proto.fulfillment.v1.BatchSyncFulfillmentReportQuestionsResponse;
import com.moego.backend.proto.fulfillment.v1.CareType;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportQuestionSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplate;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplateUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetFulfillmentReportTemplateRequest;
import com.moego.backend.proto.fulfillment.v1.GetFulfillmentReportTemplateResponse;
import com.moego.backend.proto.fulfillment.v1.GetTemplatesByUniqueKeysRequest;
import com.moego.backend.proto.fulfillment.v1.QuestionTemplateIdentifier;
import com.moego.backend.proto.fulfillment.v1.SyncFulfillmentReportRequest;
import com.moego.backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateRequest;
import com.moego.backend.proto.fulfillment.v1.SyncFulfillmentReportTemplateResponse;
import com.moego.backend.proto.fulfillment.v1.SyncOperation;
import com.moego.common.distributed.LockManager;
import com.moego.common.dto.FeatureQuotaDto;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.FeatureConst;
import com.moego.common.enums.PetTypeEnum;
import com.moego.common.enums.ReviewBoosterConst;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.groomingreport.GroomingReportCategoryEnum;
import com.moego.common.enums.groomingreport.GroomingReportConst;
import com.moego.common.enums.groomingreport.GroomingReportQuestionTypeEnum;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.common.security.Des3Util;
import com.moego.common.utils.AccountUtil;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.RandomUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.dto.AppointmentServiceInfo;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.groomingreport.BodyViewUrl;
import com.moego.server.grooming.dto.groomingreport.GroomingRecommendation;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoForInputDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportQuestionDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportResourceDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportTemplateDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportThemeConfigDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.MoeGroomingReportMapper;
import com.moego.server.grooming.mapper.MoeGroomingReportQuestionMapper;
import com.moego.server.grooming.mapper.MoeGroomingReportResourceMapper;
import com.moego.server.grooming.mapper.MoeGroomingReportSettingMapper;
import com.moego.server.grooming.mapper.MoeGroomingReportTemplateMapper;
import com.moego.server.grooming.mapper.MoeGroomingReportThemeConfigMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingReport;
import com.moego.server.grooming.mapperbean.MoeGroomingReportExample;
import com.moego.server.grooming.mapperbean.MoeGroomingReportQuestion;
import com.moego.server.grooming.mapperbean.MoeGroomingReportResourceExample;
import com.moego.server.grooming.mapperbean.MoeGroomingReportSetting;
import com.moego.server.grooming.mapperbean.MoeGroomingReportTemplate;
import com.moego.server.grooming.mapperbean.MoeGroomingReportThemeConfigExample;
import com.moego.server.grooming.mapstruct.GroomingReportMapper;
import com.moego.server.grooming.params.MoeBusinessBookOnlineDto;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportCardListParams;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportSummaryInfoParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportContentParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportInfoParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportSettingParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportViewCountParams;
import com.moego.server.grooming.params.groomingreport.UpdateGroomingReportStatusParams;
import com.moego.server.grooming.service.dto.ArrivalWindowTime;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.service.utils.GroomingReportConverter;
import com.moego.server.grooming.utils.BodyViewUtil;
import com.moego.server.grooming.web.vo.client.GroomingReportRecordVO;
import com.moego.server.message.client.IBoosterClient;
import com.moego.server.message.client.IGroomingReportSendClient;
import com.moego.server.message.client.IMessageClient;
import com.moego.server.message.dto.ArrivalWindowSettingDto;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import com.moego.server.message.dto.ReviewBoosterDTO;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import com.moego.server.message.params.MoeBusinessReviewBoosterParams;
import com.moego.server.payment.client.IPaymentPlanClient;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * Grooming report 功能相关
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MoeGroomingReportService {

    private final MoeGroomingReportSettingMapper groomingReportSettingMapper;
    private final MoeGroomingReportTemplateMapper groomingReportTemplateMapper;
    private final MoeGroomingReportQuestionMapper groomingReportQuestionMapper;
    private final MoeGroomingReportMapper groomingReportMapper;
    private final MoeGroomingReportResourceMapper groomingReportResourceMapper;
    private final MoeGroomingReportThemeConfigMapper groomingReportThemeConfigMapper;
    private final MoeGroomingAppointmentService appointmentService;
    private final MoeAppointmentQueryService appointmentQueryService;
    private final MoeGroomingBookOnlineService bookOnlineService;
    private final IBoosterClient iBoosterClient;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final BusinessInfoHelper businessInfoHelper;
    private final IBusinessStaffClient iBusinessStaffClient;
    private final ICustomerCustomerClient iCustomerCustomerClient;
    private final IGroomingReportSendClient iGroomingReportSendClient;
    private final IMessageClient iMessageClient;
    private final IPetClient iPetClient;
    private final IPaymentPlanClient iPaymentPlanClient;
    private final LockManager lockManager;
    private final BodyViewUtil bodyViewUtil;
    private final MigrateHelper migrateHelper;
    private final AppointmentServiceDetailService appointmentServiceDetailService;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentReportService;

    @Value("${moego.grooming-report.share-key}")
    private String groomingReportShareKey;

    /**
     * 初始化 Grooming report setting、template、questions
     * setting 和 template 都有 businessId 唯一索引，可以重复初始化
     * questions 无唯一索引，初始化时先查询是否已有记录，有则不再初始化
     *
     * @param businessId     businessId
     * @param queryLatestDTO 是否返回最新的 settingDTO
     * @return Grooming report setting
     */
    public GroomingReportSettingDTO initGroomingReportSetting(
            Integer businessId, Long companyId, boolean queryLatestDTO) {
        // setting 初始化
        groomingReportSettingMapper.initGroomingReportSetting(businessId, companyId);
        // template 初始化
        groomingReportTemplateMapper.initGroomingReportTemplate(businessId, companyId);
        // 数据双写 -> fulfillment_report_template
            try {
                SyncFulfillmentReportTemplateResponse response =
                    initFulfillmentReportTemplate(businessId, companyId);
                log.info("Sync fulfillment report template when init, status: {}, templateId: {}, errorMessage: {}",
                    response.getStatus(), response.getTemplateId(), response.getErrorMessage());
            } catch (Exception e) {
                log.error("Failed to sync fulfillment report template when init, businessId: {}", businessId, e);
            }
        // 如果Question表有记录了，就不再重复初始化
        if (groomingReportQuestionMapper.countByBusinessId(businessId) == 0) {
            String resourceKey = lockManager.getResourceKey(LockManager.GROOMING_REPORT, businessId);
            String value = CommonUtil.getUuid();
            try {
                // 加锁
                if (lockManager.lockWithRetry(resourceKey, value)) {
                    // 再次 count 判断，避免重复初始化
                    if (groomingReportQuestionMapper.countByBusinessId(businessId) == 0) {
                        // 数据库查询 businessId = 0 的记录，为默认数据
                        List<MoeGroomingReportQuestion> defaultQuestions =
                                groomingReportQuestionMapper.selectByBusinessId(0);
                        if (!CollectionUtils.isEmpty(defaultQuestions)) {
                            // 抹掉id，设置成当前 businessId
                            defaultQuestions.forEach(q -> {
                                q.setId(null);
                                q.setBusinessId(businessId);
                                q.setCompanyId(companyId);
                            });
                            groomingReportQuestionMapper.initGroomingReportQuestions(defaultQuestions);
                            // 数据双写 -> fulfillment_report_question
                            CompletableFuture.runAsync(() -> {
                                try {
                                    BatchSyncFulfillmentReportQuestionsResponse response =
                                        initFulfillmentReportQuestions(businessId, companyId, defaultQuestions);
                                    log.info("Sync fulfillment report question when init, status: {}, templateId: {}, errorMessage: {}",
                                        response.getStatus(), response.getTemplateId(), response.getErrorMessage());
                                } catch (Exception e) {
                                    log.error("Failed to sync fulfillment report question when init, businessId: {}", businessId, e);
                                }
                            });
                        }
                    }
                }
            } finally {
                lockManager.unlock(resourceKey, value);
            }
        }
        return queryLatestDTO ? getGroomingReportSetting(businessId, false) : null;
    }

    /**
     * 查询 Grooming report setting
     *
     * @param businessId     businessId
     * @param checkAvailable 是否检查当前套餐可用 Grooming report
     * @return
     */
    public GroomingReportSettingDTO getGroomingReportSetting(Integer businessId, boolean checkAvailable) {
        if (checkAvailable) {
            // 检查当前套餐是否可用
            checkGroomingReportAvailable(businessId);
        }

        MoeGroomingReportSetting groomingReportSetting = groomingReportSettingMapper.selectByBusinessId(businessId);
        if (Objects.isNull(groomingReportSetting)) {
            var migrateInfo = migrateHelper.getMigrationInfo(businessId);
            return initGroomingReportSetting(businessId, migrateInfo.companyId(), true);
        }
        return GroomingReportConverter.convertSettingDTO(groomingReportSetting);
    }

    /**
     * 更新 Grooming report setting
     *
     * @param params 更新字段
     * @return
     */
    public GroomingReportSettingDTO saveGroomingReportSetting(GroomingReportSettingParams params) {
        // 检查当前套餐是否可用
        checkGroomingReportAvailable(params.getBusinessId());

        if (params.getSendingType() == null && params.getSendingMethodList() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        MoeGroomingReportSetting setting = groomingReportSettingMapper.selectByBusinessId(params.getBusinessId());
        if (setting == null) {
            return null;
        }

        MoeGroomingReportSetting update = new MoeGroomingReportSetting();
        BeanUtils.copyProperties(params, update);
        update.setId(setting.getId()); // 设置主键
        if (params.getSendingMethodList() != null) {
            update.setSendingMethod(getSendingMethodForDB(params.getSendingMethodList()));
        }
        groomingReportSettingMapper.updateByPrimaryKeySelective(update);
        // 返回时，不再重复检查
        return getGroomingReportSetting(params.getBusinessId(), false);
    }

    /**
     * 查询 Grooming report template, question list
     *
     * @param businessId businessId
     * @return
     */
    public GroomingReportTemplateDTO getGroomingReportTemplate(Integer businessId, boolean checkAvailable) {
        if (checkAvailable) {
            // 检查当前套餐是否可用
            checkGroomingReportAvailable(businessId);
        }

        MoeGroomingReportTemplate template = groomingReportTemplateMapper.selectByBusinessId(businessId);
        if (Objects.isNull(template)) {
            var migrateInfo = migrateHelper.getMigrationInfo(businessId);
            initGroomingReportSetting(businessId, migrateInfo.companyId(), true);
            template = groomingReportTemplateMapper.selectByBusinessId(businessId);
        }
        List<MoeGroomingReportQuestion> questions = groomingReportQuestionMapper.selectByBusinessId(businessId);

        // 查询 review booster link
        ReviewBoosterDTO reviewBoosterConfig = iBoosterClient.getReviewBoosterConfig(businessId);
        return checkAndConvertTemplate(template, reviewBoosterConfig, questions);
    }

    /**
     * 更新 template, question list
     *
     * @param params 更新参数
     * @return
     */
    public GroomingReportTemplateDTO saveGroomingReportTemplate(GroomingReportTemplateParams params) {
        // 检查套餐权限
        checkPlanAndResetTemplateParams(params);
        // 检查参数
        checkTemplateQuestionParams(params);

        MoeGroomingReportTemplate existRecord = groomingReportTemplateMapper.selectByBusinessId(params.getBusinessId());
        if (Objects.isNull(existRecord)) {
            return null;
        }
        // 更新 review booster link
        updateReviewBoosterLink(params);

        MoeGroomingReportTemplate updateRecord = new MoeGroomingReportTemplate();
        BeanUtils.copyProperties(params, updateRecord);
        updateRecord.setId(existRecord.getId());
        updateRecord.setLastPublishTime(new Date()); // 更新 publish time 为当前时间
        groomingReportTemplateMapper.updateByPrimaryKeySelective(updateRecord);
        // 数据双写 -> fulfillment_report_template
        CompletableFuture.runAsync(() -> {
            try {
                MoeGroomingReportTemplate existTemplate =
                    groomingReportTemplateMapper.selectByBusinessId(params.getBusinessId());

                fulfillmentReportService.syncFulfillmentReportTemplate(
                    SyncFulfillmentReportTemplateRequest.newBuilder()
                        .setOperation(SyncOperation.UPDATE)
                        .setUniqueKey(FulfillmentReportTemplateUniqueKey.newBuilder()
                            .setBusinessId(existTemplate.getBusinessId())
                            .setCompanyId(existTemplate.getCompanyId())
                            .setCareType(CareType.CARE_TYPE_GROOMING)
                            .build())
                        .setTemplate(GroomingReportConverter.buildFulfillmentReportTemplateSync(existTemplate))
                        .build());
                log.info("Sync fulfillment report question, status: {}, templateId: {}, errorMessage: {}",
                    response.getStatus(), response.getTemplateId(), response.getErrorMessage());
            } catch (Exception e) {
                log.error("Failed to sync fulfillment report question for businessId: {}, questionId: {}",
                    businessId, question.getId(), e);
            }
        });
        if (Objects.nonNull(params.getQuestions())) {
            // 插入或更新 questions, feedback question 是系统默认，不需要重新排序
            saveQuestions(
                    params.getQuestions().getFeedbacks(),
                    params.getBusinessId(),
                    params.getCompanyId(),
                    GroomingReportCategoryEnum.CATEGORY_FEEDBACK.getType());
            saveQuestions(
                    params.getQuestions().getPetConditions(),
                    params.getBusinessId(),
                    params.getCompanyId(),
                    GroomingReportCategoryEnum.CATEGORY_PET_CONDITION.getType());
        }
        // 更新到 setting
        groomingReportSettingMapper.updatePublishTimeByBusinessId(
                params.getBusinessId(), updateRecord.getLastPublishTime());
        return getGroomingReportTemplate(params.getBusinessId(), false);
    }

    public List<GroomingReportThemeConfigDTO> getGroomingReportThemeConfigList(Integer businessId) {
        // 根据 business 级别取不同的 themeConfig
        CompanyDto companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        Integer level = companyDto.getLevel();

        MoeGroomingReportThemeConfigExample example = new MoeGroomingReportThemeConfigExample();
        example.createCriteria()
                .andStatusNotEqualTo(GroomingReportConst.THEME_STATUS_HIDE)
                .andVisibleLevelLessThanOrEqualTo(level);
        example.setOrderByClause("sort desc"); // 按 sort 值倒序

        return groomingReportThemeConfigMapper.selectByExample(example).stream()
                .map(themeConfig -> {
                    var dto = GroomingReportMapper.INSTANCE.entity2ThemeConfigDTO(themeConfig);
                    if (Objects.equals(themeConfig.getFlag(), GroomingReportConst.THEME_FLAG_GROWTH_ONLY)
                            && level < CompanyFunctionControlConst.TIER_SECOND_DEFAULT_LEVEL) {
                        dto.setTag(GroomingReportConst.THEME_TAG_NEED_UPDATE_TO_GROWTH);
                    } else {
                        dto.setTag(GroomingReportConst.THEME_TAG_NORMAL);
                    }
                    return dto;
                })
                .toList();
    }

    /**
     * 更新 Review booster link
     *
     * @param params save params
     */
    private void updateReviewBoosterLink(GroomingReportTemplateParams params) {
        if (params.getYelpReviewLink() == null
                || params.getGoogleReviewLink() == null
                || params.getFacebookReviewLink() == null) {
            return;
        }

        // 更新 review booster link
        MoeBusinessReviewBoosterParams boosterParams = new MoeBusinessReviewBoosterParams();
        boosterParams.setBusinessId(params.getBusinessId());
        boosterParams.setPositiveYelp(params.getYelpReviewLink());
        boosterParams.setPositiveGoogle(params.getGoogleReviewLink());
        boosterParams.setPositiveFacebook(params.getFacebookReviewLink());
        iBoosterClient.updateReviewBoosterConfig(boosterParams);
    }

    /**
     * 新增、编辑、删除 question，同时更新 sort 值
     *
     * @param questionParamsList question 更新参数列表
     * @param businessId         businessId
     * @param category           category
     */
    private void saveQuestions(
            List<GroomingReportQuestionParams> questionParamsList, Integer businessId, Long companyId, byte category) {
        if (CollectionUtils.isEmpty(questionParamsList)) {
            return;
        }
        // feedback questions 不需要排序
        boolean needSort = category != GroomingReportCategoryEnum.CATEGORY_FEEDBACK.getType();
        AtomicInteger sortValue = new AtomicInteger(questionParamsList.size());
        questionParamsList.forEach(questionParams -> {
            if (needSort) {
                // 更新 sort 值，这里不考虑删除掉的，删除了的加入排序，不影响顺序
                questionParams.setSort(sortValue.getAndDecrement());
            }

            questionParams.setBusinessId(businessId);
            questionParams.setCompanyId(companyId);
            MoeGroomingReportQuestion question =
                    GroomingReportConverter.convertParamsToQuestion(questionParams, category);
            if (question.getId() == null) {
                // create
                groomingReportQuestionMapper.insertSelective(question);
            } else {
                // update, update 时直接根据 id 更新，前面已经做数据合法性校验
                groomingReportQuestionMapper.updateByPrimaryKeySelective(question);
            }
            // 数据双写
            CompletableFuture.runAsync(() -> {
                try {
                    BatchSyncFulfillmentReportQuestionsResponse response =
                        sycnFulfillmentReportQuestions(businessId, companyId, question);
                    log.info("Sync fulfillment report question, status: {}, templateId: {}, errorMessage: {}",
                        response.getStatus(), response.getTemplateId(), response.getErrorMessage());
                } catch (Exception e) {
                    log.error("Failed to sync fulfillment report question for businessId: {}, questionId: {}",
                        businessId, question.getId(), e);
                }
            });
        });
    }

    /**
     * 获取 grooming report preview data，用于 setting 页面 preview
     * 代码中每次根据商家的 template 生成，不落库
     *
     * @param businessId businessId
     * @return
     */
    public GroomingReportPreviewDataDTO getPreviewData(Integer businessId, Integer reportId, String themeCode) {
        // 检查当前套餐是否可用
        checkGroomingReportAvailable(businessId);

        GroomingReportTemplateDTO template = getGroomingReportTemplate(businessId, false);
        MoeGroomingReport previewReport;
        AppointmentWithPetDetailsDto appointment;
        // reportId 不为空时，根据 reportId 查找 appointment 信息，生成 reportInfo
        if (reportId == null) {
            previewReport = GroomingReportConverter.getSampleGroomingReport(businessId, template);
            appointment = GroomingReportConverter.getSampleAppointment(null);
        } else {
            previewReport = groomingReportMapper.selectByPrimaryKey(reportId);
            if (!Objects.equals(previewReport.getBusinessId(), businessId)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "report not found");
            }

            if (previewReport.getGroomingId() != 0) {
                appointment = appointmentQueryService.getAppointmentWithPetDetails(previewReport.getGroomingId(), true);
                verifyGroomingReportAppointment(appointment, previewReport.getPetId());
            } else {
                appointment = GroomingReportConverter.getSampleAppointment(null);
            }
        }
        // 预览时设置临时的 themeCode
        if (StringUtils.hasText(themeCode)) {
            previewReport.setThemeCode(themeCode);
        }

        GroomingReportSummaryInfoDTO reportSummaryInfo = buildReportSummaryInfoDTO(
                        previewReport.getCompanyId(),
                        businessId,
                        List.of(previewReport),
                        Map.of(appointment.getAppointmentId(), appointment))
                .get(0);
        return new GroomingReportPreviewDataDTO(
                reportSummaryInfo, GroomingReportConverter.getGroomingReportSampleValue());
    }

    /**
     * 获取 grooming report preview data，用于发送邮件
     *
     * @param businessId businessId
     * @param params     实时预览 template 和 content 参数
     * @return preview 对象
     */
    public GroomingReportPreviewDataDTO getPreviewDataForSendingEmail(
            Integer businessId, GroomingReportPreviewParams params) {
        GroomingReportPreviewDataDTO previewDataDTO =
                getPreviewData(businessId, params.getReportId(), params.getThemeCode());

        GroomingReportSummaryInfoDTO reportSummaryInfo = previewDataDTO.getReportSummary();
        // update template with preview params
        GroomingReportTemplateParams templateParams = params.getTemplate();
        if (Objects.nonNull(templateParams)) {
            GroomingReportInfoDTO.GroomingReportTemplate template = new GroomingReportInfoDTO.GroomingReportTemplate();
            BeanUtils.copyProperties(templateParams, template);
            reportSummaryInfo.getReportInfo().setTemplate(template);
        }

        GroomingReportContentParams contentParams = params.getContent();
        if (Objects.nonNull(contentParams)) {
            Integer petTypeId = previewDataDTO.getReportSummary().getPetInfo().getPetTypeId();
            // 标记并上传 Body view 图片
            handleBodyView(petTypeId, contentParams);
            GroomingReportInfoDTO.GroomingReportContent content =
                    GroomingReportConverter.convertParamsToBeanForPreview(petTypeId, contentParams);
            reportSummaryInfo.getReportInfo().setContent(content);
        }

        return previewDataDTO;
    }

    /**
     * 保存 grooming report preview data，用于 C preview link 访问
     *
     * @param params preview 参数
     * @return uuid, 用于生成 c preview url
     */
    public String savePreviewData(GroomingReportPreviewParams params) {
        Integer businessId = params.getBusinessId();
        Long companyId = params.getCompanyId();
        // 检查当前套餐是否可用
        checkGroomingReportAvailable(businessId);

        MoeGroomingReport existRecord = groomingReportMapper.selectBusinessDefaultReport(businessId);
        MoeGroomingReport updateRecord = new MoeGroomingReport();
        if (existRecord == null) {
            updateRecord.setBusinessId(businessId);
            updateRecord.setCompanyId(companyId);
            updateRecord.setGroomingId(0);
            updateRecord.setPetId(0);
            updateRecord.setPetTypeId(PetTypeEnum.DOG.getType());
        } else {
            updateRecord.setId(existRecord.getId());
            updateRecord.setUuid(existRecord.getUuid());
            updateRecord.setPetTypeId(existRecord.getPetTypeId());
        }
        updateRecord.setStatus(GroomingReportStatusEnum.submitted.name()); // preview 数据默认 submitted 状态, ready to send
        updateRecord.setSubmittedTime(new Date());
        // update template and content by params
        fillTemplateAndContent(updateRecord, params);
        // themeCode 设置
        if (StringUtils.hasText(params.getThemeCode())) {
            updateRecord.setThemeCode(params.getThemeCode());
        }

        if (updateRecord.getUuid() == null) {
            updateRecord.setUuid(buildUniqueIdForGroomingReport());
        }

        if (updateRecord.getId() != null) {
            groomingReportMapper.updateByPrimaryKeySelective(updateRecord);
        } else {
            groomingReportMapper.insertSelective(updateRecord);
        }
        return updateRecord.getUuid();
    }

    private void fillTemplateAndContent(MoeGroomingReport record, GroomingReportPreviewParams params) {
        GroomingReportTemplateParams templateParams = params.getTemplate();
        GroomingReportContentParams contentParams = params.getContent();

        GroomingReportTemplateDTO savedTemplate = getGroomingReportTemplate(params.getBusinessId(), false);
        GroomingReportInfoDTO.GroomingReportTemplate template = new GroomingReportInfoDTO.GroomingReportTemplate();
        // templateParams 或 contentParams 不传时，补充设置中的 template 和 content
        if (templateParams == null) {
            BeanUtils.copyProperties(savedTemplate, template);
            record.setTemplateJson(JsonUtil.toJson(template));
            record.setTemplatePublishTime(toDate(savedTemplate.getLastPublishTime()));
        } else {
            BeanUtils.copyProperties(templateParams, template);
            record.setTemplateJson(JsonUtil.toJson(template));
            // 实时预览的数据 template 是临时的，使用当前时间作为 template publish time
            record.setTemplatePublishTime(new Date());
        }

        GroomingReportInfoDTO.GroomingReportContent content;
        if (contentParams == null) {
            content = GroomingReportConverter.buildSampleGroomingReportContent(savedTemplate);
        } else {
            handleBodyView(record.getPetTypeId(), contentParams);
            content = GroomingReportConverter.convertParamsToBeanForPreview(record.getPetTypeId(), contentParams);
        }
        record.setContentJson(JsonUtil.toJson(content));
    }

    /**
     * 查询 appointment 每个 pet 的 grooming report 状态
     *
     * @param businessId businessId
     * @param groomingId 预约id
     * @return 当前预约所有 pet 的 grooming report 状态
     */
    public List<GroomingReportRecordDTO> getGroomingReportRecords(
            boolean migrated, Long companyId, Integer businessId, Integer groomingId) {
        MoeGroomingAppointment appointment = appointmentQueryService.getAppointmentById(groomingId);
        if (appointment == null
                || (!migrated && !Objects.equals(appointment.getBusinessId(), businessId))
                || (migrated && !Objects.equals(appointment.getCompanyId(), companyId))) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "could not find appointment");
        }
        // cancelled 的预约返回空数组
        if (Objects.equals(appointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            return List.of();
        }
        // 查询 grooming report setting
        GroomingReportSettingDTO setting = getGroomingReportSetting(businessId, false);
        // 查询 grooming report 记录
        Map<Integer, MoeGroomingReport> groomingReportMap =
                groomingReportMapper.selectByGroomingId(businessId, groomingId).stream()
                        .collect(Collectors.toMap(MoeGroomingReport::getPetId, Function.identity()));
        // 查询 grooming report 发送记录
        Map<Integer, List<GroomingReportSendLogDTO>> sendLogMap =
                iGroomingReportSendClient.getGroomingLastReportSendLogs(businessId, groomingId).stream()
                        .collect(Collectors.groupingBy(GroomingReportSendLogDTO::getPetId));
        var allPetDetailsPair = appointmentServiceDetailService.getAppointmentServiceDetails(List.of(groomingId));
        // 查询 pet 信息
        List<Integer> petIds =
                appointmentServiceDetailService.getPetIds(allPetDetailsPair.key(), allPetDetailsPair.value());
        List<CustomerPetDetailDTO> petDTOs = iPetClient.getCustomerPetListByIdList(petIds);
        // 构建返回对象
        return petDTOs.stream()
                .map(pet -> GroomingReportConverter.convertGroomingReportRecordDTO(
                        groomingReportMap.get(pet.getPetId()), pet, setting, sendLogMap.get(pet.getPetId())))
                .toList();
    }

    /**
     * C portal 查询 grooming report 记录
     *
     * @param appointment 预约
     * @return grooming report 记录列表
     */
    public List<GroomingReportRecordVO> getGroomingReportRecordsForClient(MoeGroomingAppointment appointment) {
        // cancelled 的预约返回空数组
        if (appointment == null || Objects.equals(appointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            return List.of();
        }
        MoeBusinessDto businessInfo =
                iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(appointment.getBusinessId()));
        // 查询 grooming report 记录
        return groomingReportMapper.selectByGroomingId(appointment.getBusinessId(), appointment.getId()).stream()
                .filter(report -> Objects.equals(report.getStatus(), GroomingReportStatusEnum.submitted.name())
                        || Objects.equals(report.getStatus(), GroomingReportStatusEnum.sent.name()))
                .map(report -> {
                    GroomingReportInfoDTO reportInfo = GroomingReportConverter.convertGroomingReportInfoDTO(
                            report, appointment.getAppointmentDate(), businessInfo);
                    String title = reportInfo.getTemplate().getTitle();
                    return new GroomingReportRecordVO()
                            .setPetId(report.getPetId())
                            .setReportId(report.getId())
                            .setReportUuid(report.getUuid())
                            .setTitle(StringUtils.hasText(title) ? title : GroomingReportConst.DEFAULT_TITLE);
                })
                .toList();
    }

    /**
     * Get grooming report for client app
     *
     * @param appointmentId appointment id
     * @return grooming report list
     */
    public List<GroomingReportDTO> getGroomingReportRecordsForClientApp(Integer appointmentId) {
        MoeGroomingAppointment appointment = appointmentService.getAppointment(appointmentId);
        if (appointment == null || Objects.equals(appointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())) {
            return List.of();
        }
        return groomingReportMapper.selectByGroomingId(appointment.getBusinessId(), appointment.getId()).stream()
                .filter(report -> Objects.equals(report.getStatus(), GroomingReportStatusEnum.sent.name()))
                .map(GroomingReportMapper.INSTANCE::entity2DTO)
                .toList();
    }

    public GroomingReportInfoForInputDTO getGroomingReportInfoForInput(
            boolean migrated, Integer businessId, Integer groomingId, Integer petId) {
        GroomingReportInfoDTO reportInfo = getGroomingReportInfo(migrated, businessId, groomingId, petId);
        MoeGroomingReportTemplate template = groomingReportTemplateMapper.selectByBusinessId(businessId);

        return new GroomingReportInfoForInputDTO(
                isTemplateNeedRefresh(reportInfo.getTemplatePublishTime(), toTimestamp(template.getLastPublishTime())),
                reportInfo);
    }

    /**
     * B端根据 groomingId, petId 查找 grooming report
     * 如无记录，则根据 template setting 生成 default grooming report
     *
     * @param businessId businessId
     * @param groomingId groomingId
     * @param petId      petId
     * @return grooming report 对象
     */
    public GroomingReportInfoDTO getGroomingReportInfo(
            boolean migrated, Integer businessId, Integer groomingId, Integer petId) {
        AppointmentWithPetDetailsDto appointment =
                appointmentQueryService.getAppointmentWithPetDetails(groomingId, true);
        verifyGroomingReportAppointment(appointment, petId);

        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(businessId));
        List<CustomerPetDetailDTO> petList = iPetClient.getCustomerPetListByIdList(List.of(petId));
        if (CollectionUtils.isEmpty(petList)
                || (!migrated && !Objects.equals(petList.get(0).getBusinessId(), businessId))
                || (migrated
                        && !Objects.equals(petList.get(0).getCompanyId().intValue(), businessInfo.getCompanyId()))) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "pet not found");
        }

        MoeGroomingReport groomingReport =
                groomingReportMapper.selectByGroomingIdAndPetId(businessId, groomingId, petId);
        GroomingReportTemplateDTO templateDTO = getGroomingReportTemplate(businessId, false);
        if (groomingReport == null) {
            // 首次查询初始化
            groomingReport = initGroomingReport(businessId, templateDTO, appointment, petList.get(0));
        } else if (Objects.equals(groomingReport.getStatus(), GroomingReportStatusEnum.created.name())) {
            // status = created 时, 检查 template 是否过期，过期则更新
            if (isTemplateNeedRefresh(
                    toTimestamp(groomingReport.getTemplatePublishTime()), templateDTO.getLastPublishTime())) {
                groomingReport = initGroomingReport(businessId, templateDTO, appointment, petList.get(0));
            }
        }

        GroomingReportInfoDTO groomingReportInfoDTO = GroomingReportConverter.convertGroomingReportInfoDTO(
                groomingReport, appointment.getAppointmentDate(), businessInfo);

        // 这里由于兼容性导致产生一些 id = null / category = null 的 feedback 脏数据需要特殊处理
        return handlerGroomingReportDateFix(groomingReportInfoDTO, templateDTO);
    }

    // 脏数据特殊处理
    private GroomingReportInfoDTO handlerGroomingReportDateFix(
            GroomingReportInfoDTO groomingReportInfoDTO, GroomingReportTemplateDTO templateDTO) {
        List<GroomingReportQuestionDTO> templateFeedbackQuestions =
                templateDTO.getQuestions().feedbacks();
        List<GroomingReportInfoDTO.GroomingReportQuestion> feedbacks =
                groomingReportInfoDTO.getContent().getFeedbacks();

        if (CollectionUtils.isEmpty(feedbacks)) {
            return groomingReportInfoDTO;
        }

        long idNullCount = feedbacks.stream()
                .filter(question -> !CommonUtil.isNormal(question.getId()))
                .count();
        if (idNullCount > 0) {
            feedbacks = GroomingReportConverter.convertGroomingReportQuestionList(templateFeedbackQuestions);
            // 初始化 choice question 选中
            feedbacks.forEach(GroomingReportConverter::initChoiceQuestionChoice);
        }

        // 填充 category
        Map<Integer, GroomingReportQuestionDTO> templateFeedbackMap = templateFeedbackQuestions.stream()
                .collect(Collectors.toMap(GroomingReportQuestionDTO::getId, question -> question));
        feedbacks = feedbacks.stream()
                .peek(question -> fillQuestionCategory(question, templateFeedbackMap))
                .toList();

        groomingReportInfoDTO.getContent().setFeedbacks(feedbacks);
        return groomingReportInfoDTO;
    }

    /**
     * 填充问题的 category 字段，避免前端根据 category 分组报错
     */
    private void fillQuestionCategory(
            GroomingReportInfoDTO.GroomingReportQuestion question,
            Map<Integer, GroomingReportQuestionDTO> templateFeedbackMap) {
        if (CommonUtil.isNormal(question.getCategory())) {
            return;
        }

        if (templateFeedbackMap.containsKey(question.getId())) {
            question.setCategory(templateFeedbackMap.get(question.getId()).getCategory());
        } else {
            question.setCategory(GroomingReportCategoryEnum.CATEGORY_FEEDBACK.getType());
        }
    }

    public List<GroomingReportDTO> getGroomingReportListByGroomingIdList(
            Integer businessId, List<Integer> groomingIdList) {
        if (CollectionUtils.isEmpty(groomingIdList)) {
            return List.of();
        }
        MoeGroomingReportExample example = new MoeGroomingReportExample();
        example.createCriteria().andBusinessIdEqualTo(businessId).andGroomingIdIn(groomingIdList);
        List<MoeGroomingReport> groomingReportList = groomingReportMapper.selectByExample(example);
        return groomingReportList.stream()
                .map(GroomingReportMapper.INSTANCE::entity2DTO)
                .toList();
    }

    /**
     * OB 来源根据 reportId 查询 grooming report 信息
     *
     * @param customerId customerId
     * @param reportId   reportId
     * @return grooming report 对象
     */
    public GroomingReportInfoDTO getGroomingReportInfoForOB(Integer customerId, Integer reportId) {
        MoeGroomingReport groomingReport = groomingReportMapper.selectByPrimaryKey(reportId);
        if (groomingReport == null || !Objects.equals(groomingReport.getCustomerId(), customerId)) {
            return null;
        }
        MoeBusinessDto businessInfo =
                iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(groomingReport.getBusinessId()));
        MoeGroomingAppointment appointment = appointmentQueryService.getAppointmentById(
                groomingReport.getBusinessId(), groomingReport.getGroomingId());
        return GroomingReportConverter.convertGroomingReportInfoDTO(
                groomingReport, appointment.getAppointmentDate(), businessInfo);
    }

    /**
     * 生成 grooming report 默认值
     *
     * @param businessId  businessId
     * @param template    template
     * @param appointment appointment
     * @param pet         pet
     * @return grooming report 对象
     */
    private MoeGroomingReport initGroomingReport(
            Integer businessId,
            GroomingReportTemplateDTO template,
            AppointmentWithPetDetailsDto appointment,
            CustomerPetDetailDTO pet) {
        // 根据模板生成默认数据
        MoeBusinessCustomerDTO customerDTO =
                iCustomerCustomerClient.getCustomerWithDeleted(appointment.getCustomerId());
        GroomingReportInfoDTO.GroomingReportContent content =
                GroomingReportConverter.buildGroomingReportContent(template, customerDTO);
        MoeGroomingReport groomingReport = new MoeGroomingReport();
        groomingReport.setBusinessId(businessId);
        groomingReport.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
        groomingReport.setGroomingId(appointment.getAppointmentId());
        groomingReport.setCustomerId(appointment.getCustomerId());
        groomingReport.setPetId(pet.getPetId());
        groomingReport.setPetTypeId(pet.getPetTypeId());
        groomingReport.setStatus(GroomingReportStatusEnum.created.name());
        groomingReport.setTemplatePublishTime(toDate(template.getLastPublishTime()));
        groomingReport.setTemplateJson(JsonUtil.toJson(template));
        groomingReport.setContentJson(JsonUtil.toJson(content));
        groomingReport.setThemeCode(template.getThemeCode()); // 使用 template 指定的 themeCode
        // 初始化，落库
        groomingReportMapper.initGroomingReports(List.of(groomingReport));
        return groomingReportMapper.selectByGroomingIdAndPetId(
                businessId, appointment.getAppointmentId(), pet.getPetId());
    }

    /**
     * save/submit grooming report
     * 1.检查必传参数
     * 2.检查预约是否存在
     * 3.检查 template 是否过期
     * 4.检查 question 参数
     * 5.生成 uuid
     * 6.保存到数据库
     * 7.自动发送逻辑（暂时不需要）
     *
     * @param params   save/submit 参数
     * @param isSubmit 是否 submit
     * @return
     */
    public GroomingReportInfoForInputDTO saveOrSubmitGroomingReportInfo(
            boolean migrated, GroomingReportInfoParams params, boolean isSubmit) {
        // 检查当前套餐是否可用
        checkGroomingReportAvailable(params.getBusinessId());

        // id 或 groomingId + petId 不能同时为空
        if (params.getId() == null && (params.getGroomingId() == null || params.getPetId() == null)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }

        MoeGroomingReport existRecord = null;
        AppointmentWithPetDetailsDto appointment;
        if (params.getId() != null) {
            existRecord = getExistingGroomingReportRecord(params);
            params.setPetId(existRecord.getPetId());
            params.setCustomerId(existRecord.getCustomerId());
            // 如果已经 submitted 或 sent 了，不能再 save，只能 submit
            if (!isSubmit
                    && (Objects.equals(existRecord.getStatus(), GroomingReportStatusEnum.submitted.name())
                            || Objects.equals(existRecord.getStatus(), GroomingReportStatusEnum.sent.name()))) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "grooming report has been submitted, cannot save as draft again.");
            }
            appointment = appointmentQueryService.getAppointmentWithPetDetails(existRecord.getGroomingId(), true);
        } else {
            appointment = appointmentQueryService.getAppointmentWithPetDetails(params.getGroomingId(), true);
        }
        // check appointment
        verifyGroomingReportAppointment(appointment, params.getPetId());

        GroomingReportTemplateDTO template = getGroomingReportTemplate(params.getBusinessId(), false);
        if (isTemplateNeedRefresh(params.getTemplatePublishTime(), template.getLastPublishTime())) {
            GroomingReportInfoDTO reportInfoDTO =
                    getGroomingReportInfo(migrated, params.getBusinessId(), params.getGroomingId(), params.getPetId());
            MoeBusinessDto businessDto =
                    iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(params.getBusinessId()));
            MoeBusinessCustomerDTO customerDTO =
                    iCustomerCustomerClient.getCustomerWithDeleted(appointment.getCustomerId());
            GroomingReportConverter.mergeGroomingReportContent(
                    reportInfoDTO, params, template, businessDto, customerDTO, appointment.getAppointmentDate());
            return new GroomingReportInfoForInputDTO(true, reportInfoDTO);
        }

        if (isSubmit) {
            // submit 时检查 content params
            checkGroomingReportContentParams(params.getContent(), template);
            if (existRecord == null || existRecord.getUuid() == null) {
                // submit 时如 existing record 没有 uuid 则生成一个新的 uuid
                params.setUuid(buildUniqueIdForGroomingReport());
            }
            params.setStatus(GroomingReportStatusEnum.submitted.name());
            // 如果已经 sent 过，再次 submit 时，状态仍为 sent
            if (existRecord != null && Objects.equals(existRecord.getStatus(), GroomingReportStatusEnum.sent.name())) {
                params.setStatus(GroomingReportStatusEnum.sent.name());
            }
        } else {
            // save 状态为 draft
            params.setStatus(GroomingReportStatusEnum.draft.name());
        }
        // 处理 bodyView 图片，并上传到 s3
        handleBodyView(params.getPetTypeId(), params.getContent());

        saveGroomingReportToDB(params, template);
        return new GroomingReportInfoForInputDTO(
                false,
                getGroomingReportInfo(migrated, params.getBusinessId(), params.getGroomingId(), params.getPetId()));
    }

    private MoeGroomingReport getExistingGroomingReportRecord(GroomingReportInfoParams params) {
        MoeGroomingReport existRecord = groomingReportMapper.selectByPrimaryKey(params.getId());
        if (existRecord == null || !existRecord.getBusinessId().equals(params.getBusinessId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "grooming report record not exist");
        }
        return existRecord;
    }

    private boolean isTemplateNeedRefresh(Long groomingReportTemplateTime, Long templatePublishTime) {
        return ((groomingReportTemplateTime != null
                        && templatePublishTime != null
                        && groomingReportTemplateTime < templatePublishTime)
                || (groomingReportTemplateTime == null && templatePublishTime != null));
    }

    public String buildUniqueIdForGroomingReport() {
        MoeGroomingReport existingRecord;
        String uuid;
        // 12位随机字符串，如果已经存在则重新生成
        do {
            uuid = RandomUtil.randomLowerCharAndNumString(12);
            existingRecord = groomingReportMapper.selectByUuid(uuid);
        } while (existingRecord != null);
        return uuid;
    }

    private void saveGroomingReportToDB(GroomingReportInfoParams params, GroomingReportTemplateDTO templateDTO) {
        MoeGroomingReport saveRecord = new MoeGroomingReport();
        BeanUtils.copyProperties(params, saveRecord);
        GroomingReportInfoDTO.GroomingReportTemplate template = new GroomingReportInfoDTO.GroomingReportTemplate();
        BeanUtils.copyProperties(templateDTO, template);
        saveRecord.setTemplateJson(JsonUtil.toJson(template));
        saveRecord.setContentJson(JsonUtil.toJson(params.getContent()));
        saveRecord.setTemplatePublishTime(toDate(params.getTemplatePublishTime()));

        if (params.getId() != null) {
            groomingReportMapper.updateByPrimaryKeySelective(saveRecord);
        } else {
            groomingReportMapper.insertSelective(saveRecord);
        }
    }

    /**
     * 根据 reportId 列表查找 grooming report
     *
     * @param params 查询参数
     * @return grooming report 列表
     */
    public List<GroomingReportSummaryInfoDTO> getGroomingReportSummaryInfoList(
            GetGroomingReportSummaryInfoParams params) {
        Integer businessId = params.businessId();
        List<Integer> reportIds = params.groomingReportIdList();

        List<MoeGroomingReport> groomingReportList =
                groomingReportMapper.selectByBusinessIdAndIds(businessId, reportIds);
        if (CollectionUtils.isEmpty(groomingReportList)) {
            return Collections.emptyList();
        }
        Set<Integer> groomingIds = groomingReportList.stream()
                .filter(report -> report.getGroomingId() != null && report.getGroomingId() > 0)
                .map(MoeGroomingReport::getGroomingId)
                .collect(Collectors.toSet());

        Map<Integer, AppointmentWithPetDetailsDto> appointmentMap =
                appointmentQueryService.getAppointmentListWithPetDetails(groomingIds, false).stream()
                        .collect(Collectors.toMap(AppointmentWithPetDetailsDto::getAppointmentId, Function.identity()));

        return buildReportSummaryInfoDTO(
                groomingReportList.get(0).getCompanyId(), businessId, groomingReportList, appointmentMap);
    }

    public GroomingReportSummaryInfoDTO getGroomingReportSummaryInfo(String uuid, Integer reportId) {
        if (uuid == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "uuid is null");
        }
        boolean isForShare = false;
        if (uuid.startsWith(SHARE_UUID_PREFIX)) {
            isForShare = true;
            uuid = uuid.substring(uuid.indexOf(SHARE_UUID_PREFIX) + SHARE_UUID_PREFIX.length());
            uuid = Des3Util.decode(groomingReportShareKey, uuid);
            if (uuid == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "grooming report not found");
            }
        }

        MoeGroomingReport groomingReport = groomingReportMapper.selectByUuid(uuid);
        if (groomingReport == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "grooming report not found");
        }
        // 状态检查
        if (!Objects.equals(groomingReport.getStatus(), GroomingReportStatusEnum.submitted.name())
                && !Objects.equals(groomingReport.getStatus(), GroomingReportStatusEnum.sent.name())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Grooming report are not ready");
        }

        // sample 数据，需要单独构造
        boolean isPreview = groomingReport.getGroomingId() == 0 || groomingReport.getPetId() == 0;
        boolean previewAppointment = reportId != null && reportId > 0;
        AppointmentWithPetDetailsDto appointment;
        if (isPreview) {
            appointment = GroomingReportConverter.getSampleAppointment(null);
            if (previewAppointment) {
                MoeGroomingReport previewReport = groomingReportMapper.selectByPrimaryKey(reportId);
                if (previewReport != null
                        && Objects.equals(previewReport.getBusinessId(), groomingReport.getBusinessId())
                        && previewReport.getGroomingId() > 0) {
                    appointment =
                            appointmentQueryService.getAppointmentWithPetDetails(previewReport.getGroomingId(), true);
                    groomingReport.setGroomingId(previewReport.getGroomingId());
                    groomingReport.setCustomerId(previewReport.getCustomerId());
                    groomingReport.setPetId(previewReport.getPetId());
                }
            }
        } else {
            appointment = appointmentQueryService.getAppointmentWithPetDetails(groomingReport.getGroomingId(), true);
        }
        verifyGroomingReportAppointment(appointment, groomingReport.getPetId());

        GroomingReportSummaryInfoDTO reportSummaryInfoDTO = buildReportSummaryInfoDTO(
                        groomingReport.getCompanyId(),
                        groomingReport.getBusinessId(),
                        List.of(groomingReport),
                        Map.of(appointment.getAppointmentId(), appointment))
                .get(0);
        // 生成 uuidForShare 供C端分享，生成规则，原uuid 加密，再加前缀 s_
        reportSummaryInfoDTO
                .getReportInfo()
                .setUuidForShare(SHARE_UUID_PREFIX + Des3Util.encode(groomingReportShareKey, groomingReport.getUuid()));
        if (isForShare) {
            reportSummaryInfoDTO.getReportInfo().setUuid(null);
            reportSummaryInfoDTO.getReportInfo().getContent().setRecommendation(null);
        }

        return reportSummaryInfoDTO;
    }

    private List<GroomingReportSummaryInfoDTO> buildReportSummaryInfoDTO(
            Long companyId,
            Integer businessId,
            List<MoeGroomingReport> groomingReports,
            Map<Integer, AppointmentWithPetDetailsDto> appointmentMap) {
        // 查询 business, obSetting, review booster 配置
        MoeBusinessDto businessInfo = iBusinessBusinessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId).build());
        MoeBusinessBookOnlineDto obSettings = bookOnlineService.getBusinessBookOnlineSetting(businessId);
        ReviewBoosterDTO reviewBoosterConfig = iBoosterClient.getReviewBoosterConfig(businessId);
        ArrivalWindowSettingDto arrivalWindowSetting = iMessageClient.getArrivalWindow(businessId);

        Set<Integer> petIds = new HashSet<>();
        Set<Integer> staffIds = new HashSet<>();
        Set<Integer> customerIds = new HashSet<>();

        groomingReports.stream().map(MoeGroomingReport::getPetId).forEach(petIds::add);
        addPetAndStaffIdsFromAppointment(petIds, staffIds, customerIds, appointmentMap.values());

        // 查询 next appointment
        Map<Integer, AppointmentWithPetDetailsDto> nextAppointmentMap = new HashMap<>();
        Map<Integer, Integer> groomingIdToReportIdMap = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        String date = DateUtil.convertLocalDateToDateString(now, businessInfo.getTimezoneName(), "yyyy-MM-dd");
        Integer nowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());
        groomingReports.stream()
                .filter(groomingReport -> groomingReport.getCustomerId() > 0 && groomingReport.getPetId() > 0)
                .forEach(groomingReport -> {
                    CustomerGroomingAppointmentDTO nextAppointment =
                            appointmentQueryService.getCustomerPetNextAppointment(
                                    companyId,
                                    groomingReport.getCustomerId(),
                                    groomingReport.getPetId(),
                                    groomingReport.getGroomingId(),
                                    date,
                                    nowMinutes);
                    if (nextAppointment != null) {
                        groomingIdToReportIdMap.put(nextAppointment.getId(), groomingReport.getId());
                    }
                });
        if (!CollectionUtils.isEmpty(groomingIdToReportIdMap)) {
            List<AppointmentWithPetDetailsDto> appointments =
                    appointmentQueryService.getAppointmentListWithPetDetails(groomingIdToReportIdMap.keySet(), false);
            // 设置 arrival window
            appointmentQueryService.setArrivalWindowForAppointmentDetails(
                    businessId, arrivalWindowSetting, appointments);
            appointments.forEach(appointment -> {
                if (!groomingIdToReportIdMap.containsKey(appointment.getAppointmentId())) {
                    return;
                }
                nextAppointmentMap.put(groomingIdToReportIdMap.get(appointment.getAppointmentId()), appointment);
            });

            // 把 next appointment 的 petId 和 staffId 加入查询列表
            addPetAndStaffIdsFromAppointment(petIds, staffIds, customerIds, nextAppointmentMap.values());
        }

        List<Integer> appointmentIds = appointmentMap.keySet().stream()
                .filter(id -> id != null && id > 0)
                .toList();
        Map<Integer, List<ReviewBoosterRecordDTO>> reviewRecordMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(appointmentIds)) {
            reviewRecordMap.putAll(iBoosterClient
                    .getAppointmentReviewRecords(
                            businessId, ReviewBoosterConst.REVIEW_SOURCE_GROOMING_REPORT, appointmentIds)
                    .stream()
                    .collect(Collectors.groupingBy(ReviewBoosterRecordDTO::getAppointmentId)));
        }
        Map<Integer, CustomerPetDetailDTO> petMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(petIds)) {
            petMap.putAll(iPetClient.getCustomerPetListByIdList(new ArrayList<>(petIds)).stream()
                    .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity())));
        }
        Map<Integer, MoeStaffDto> staffMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(staffIds)) {
            staffMap.putAll(
                    iBusinessStaffClient
                            .getStaffList(new StaffIdListParams(businessId, new ArrayList<>(staffIds)))
                            .stream()
                            .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity())));
        }

        ArrivalWindowTime previewArrivalWindowTime = appointmentQueryService.getStartTimeWithArrivalWindow(
                SAMPLE_APPOINTMENT_START_TIME, arrivalWindowSetting, null);

        Map<String, GroomingReportThemeConfigDTO> themeConfigMap = getGroomingReportThemeConfigMap(groomingReports);
        Map<Integer, List<GroomingReportResourceDTO>> resourceMap =
                getGroomingReportResourceMap(businessId, groomingReports);

        return groomingReports.stream()
                .map(groomingReport -> {
                    AppointmentWithPetDetailsDto curAppointment;
                    AppointmentWithPetDetailsDto nextAppointment;
                    if (groomingReport.getGroomingId() == 0 || groomingReport.getPetId() == 0) {
                        // groomingId = 0 或 petId = 0 是预览数据，取 sample data
                        curAppointment = GroomingReportConverter.getSampleAppointment(null);
                        nextAppointment = GroomingReportConverter.getSampleAppointment(
                                GroomingReportConverter.getSampleCustomer().getPreferredFrequencyDay());
                        if (previewArrivalWindowTime != null) {
                            nextAppointment.setArrivalBeforeStartTime(
                                    previewArrivalWindowTime.getArrivalBeforeStartTime());
                            nextAppointment.setArrivalAfterStartTime(
                                    previewArrivalWindowTime.getArrivalAfterStartTime());
                        }

                        CustomerPetDetailDTO petDTO = GroomingReportConverter.getSamplePet();
                        MoeStaffDto staff = GroomingReportConverter.getSampleStaff();
                        petMap.put(petDTO.getPetId(), petDTO);
                        staffMap.put(staff.getId(), staff);
                    } else {
                        curAppointment = appointmentMap.get(groomingReport.getGroomingId());
                        nextAppointment = nextAppointmentMap.get(groomingReport.getId());
                    }
                    GroomingReportInfoDTO groomingReportInfo =
                            buildReportInfo(groomingReport, curAppointment, businessInfo, reviewBoosterConfig);
                    boolean showDateOnly = Objects.equals(
                            groomingReportInfo.getTemplate().getNextAppointmentDateFormatType(),
                            GroomingReportConst.APPOINTMENT_SHOW_ONLY_DATE);

                    return new GroomingReportSummaryInfoDTO()
                            .setBusinessInfo(buildBusinessInfo(businessInfo, obSettings))
                            .setPetInfo(buildPetInfo(businessInfo, petMap.get(groomingReport.getPetId())))
                            .setGroomingInfo(buildGroomingInfo(
                                    businessInfo,
                                    Map.of(groomingReport.getPetId(), petMap.get(groomingReport.getPetId())),
                                    curAppointment,
                                    staffMap,
                                    false))
                            .setNextGroomingInfo(
                                    buildGroomingInfo(businessInfo, petMap, nextAppointment, staffMap, showDateOnly))
                            .setReportInfo(groomingReportInfo)
                            .setReviewBoosterConfig(
                                    buildReviewBoosterConfig(groomingReportInfo.getTemplate(), reviewBoosterConfig))
                            .setReviewBoosterRecord(buildReviewBoosterRecord(
                                    groomingReport,
                                    curAppointment,
                                    reviewRecordMap.get(groomingReport.getGroomingId())))
                            .setThemeConfig(themeConfigMap.get(groomingReport.getThemeCode()))
                            .setResourceList(resourceMap.getOrDefault(groomingReport.getId(), List.of()));
                })
                .toList();
    }

    private Map<String, GroomingReportThemeConfigDTO> getGroomingReportThemeConfigMap(
            List<MoeGroomingReport> groomingReports) {
        // 查询 theme 相关资源
        List<String> themeCodes = groomingReports.stream()
                .map(MoeGroomingReport::getThemeCode)
                .distinct()
                .toList();
        MoeGroomingReportThemeConfigExample queryThemeExample = new MoeGroomingReportThemeConfigExample();
        queryThemeExample.createCriteria().andCodeIn(themeCodes);
        return groomingReportThemeConfigMapper.selectByExample(queryThemeExample).stream()
                .map(GroomingReportMapper.INSTANCE::entity2ThemeConfigDTO)
                .collect(Collectors.toMap(GroomingReportThemeConfigDTO::getCode, Function.identity()));
    }

    private Map<Integer, List<GroomingReportResourceDTO>> getGroomingReportResourceMap(
            Integer businessId, List<MoeGroomingReport> groomingReports) {
        List<Integer> groomingReportIds = groomingReports.stream()
                .map(MoeGroomingReport::getId)
                .distinct()
                .toList();
        MoeGroomingReportResourceExample example = new MoeGroomingReportResourceExample();
        example.createCriteria().andBusinessIdEqualTo(businessId).andReportIdIn(groomingReportIds);
        return groomingReportResourceMapper.selectByExample(example).stream()
                .map(GroomingReportMapper.INSTANCE::entity2ResourceDTO)
                .collect(Collectors.groupingBy(GroomingReportResourceDTO::getReportId));
    }

    private void addPetAndStaffIdsFromAppointment(
            Set<Integer> petIds,
            Set<Integer> staffIds,
            Set<Integer> customerIds,
            Collection<AppointmentWithPetDetailsDto> appointmentList) {
        for (AppointmentWithPetDetailsDto appointment : appointmentList) {
            for (AppointmentServiceInfo service : appointment.getServices()) {
                // only support grooming service
                if (!Objects.equals(service.getServiceItemType(), ServiceItemEnum.GROOMING.getServiceItem())) {
                    continue;
                }
                if (service.getPetId() != null && service.getPetId() > 0) {
                    petIds.add(service.getPetId());
                }
                if (service.getStaffId() != null && service.getStaffId() > 0) {
                    staffIds.add(service.getStaffId());
                }
                if (appointment.getCustomerId() != null && appointment.getCustomerId() > 0) {
                    customerIds.add(appointment.getCustomerId());
                }
            }
        }
    }

    /**
     * 更新 grooming report 为 sent 状态
     *
     * @param params
     * @return
     */
    public Boolean updateGroomingReportSentStatus(UpdateGroomingReportStatusParams params) {
        Integer businessId = params.businessId();
        Integer updateBy = params.updateBy();
        List<Integer> groomingReportIdList = params.groomingReportIdList();

        List<MoeGroomingReport> groomingReportList =
                groomingReportMapper.selectByBusinessIdAndIds(businessId, groomingReportIdList);
        // 只能从 draft/submitted/sent 状态更新为 sent
        groomingReportList = groomingReportList.stream()
                .filter(report -> Objects.equals(report.getStatus(), GroomingReportStatusEnum.submitted.name())
                        || Objects.equals(report.getStatus(), GroomingReportStatusEnum.sent.name())
                        || Objects.equals(report.getStatus(), GroomingReportStatusEnum.draft.name()))
                .toList();
        if (CollectionUtils.isEmpty(groomingReportList)) {
            return false;
        }
        groomingReportMapper.batchUpdateGroomingReportStatus(
                businessId, groomingReportIdList, GroomingReportStatusEnum.sent.name(), updateBy);
        return true;
    }

    private void checkTemplateQuestionParams(GroomingReportTemplateParams params) {
        if (params.getQuestions() == null) {
            return;
        }
        // 如果设置了其它主题，清空 themeColor/lightThemeColor
        if (!Objects.equals(params.getThemeCode(), GroomingReportConst.DEFAULT_THEME_CODE)) {
            params.setThemeColor(null);
            params.setLightThemeColor(null);
        }

        checkQuestionParams(
                params.getBusinessId(),
                params.getQuestions().getFeedbacks(),
                GroomingReportCategoryEnum.CATEGORY_FEEDBACK.getType());
        checkQuestionParams(
                params.getBusinessId(),
                params.getQuestions().getPetConditions(),
                GroomingReportCategoryEnum.CATEGORY_PET_CONDITION.getType());
    }

    /**
     * 检查 question 参数
     *
     * @param businessId businessId
     * @param questionParams question 参数列表
     * @param category question category: 1 - feedback, 2 - pet condition
     */
    private void checkQuestionParams(
            Integer businessId, List<GroomingReportQuestionParams> questionParams, Byte category) {
        if (CollectionUtils.isEmpty(questionParams)) {
            return;
        }

        List<MoeGroomingReportQuestion> existRecords =
                groomingReportQuestionMapper.selectByBusinessIdAndCategory(businessId, category);
        Map<Integer, String> existTitles = new HashMap<>();
        Map<Integer, GroomingReportQuestionDTO> existQuestionMap = new HashMap<>();
        for (MoeGroomingReportQuestion existRecord : existRecords) {
            existTitles.put(existRecord.getId(), existRecord.getTitle());
            existQuestionMap.put(existRecord.getId(), convertQuestionDTO(existRecord));
        }

        // 移除不合法的更新参数
        questionParams.removeIf(params -> params.getId() != null && !existTitles.containsKey(params.getId()));
        int newQuestionIndex = -1;
        for (GroomingReportQuestionParams params : questionParams) {
            if (params.getId() == null || !existQuestionMap.containsKey(params.getId())) {
                // 新增 question，检查 type, title 是否合法
                if (!GroomingReportQuestionTypeEnum.checkTypeValid(params.getType())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "question type error");
                }
                if (!StringUtils.hasText(params.getTitle())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "question title is empty");
                }
            } else {
                // 更新/删除，type 和 title 可不传，因此只有在传的时候需要检查
                if (params.getType() != null && !GroomingReportQuestionTypeEnum.checkTypeValid(params.getType())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "question type error");
                }
                if (params.getTitle() != null && !StringUtils.hasText(params.getTitle())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "question title is empty");
                }
                if (GroomingReportQuestionTypeEnum.isChoiceQuestion(params.getType())) {
                    if (CollectionUtils.isEmpty(params.getOptions())) {
                        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "question options is empty");
                    }
                    // build in options 不支持更改，options 必须包含全部 build in options，否则报错
                    GroomingReportQuestionDTO existQuestion = existQuestionMap.get(params.getId());
                    Set<String> allOptions = new HashSet<>(params.getOptions());
                    List<String> buildInOptions = existQuestion.getBuildInOptions();
                    if (Boolean.TRUE.equals(existQuestion.getIsDefault())
                            && !CollectionUtils.isEmpty(buildInOptions)
                            && !allOptions.containsAll(buildInOptions)) {
                        throw ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR, "default options are not allowed to modify");
                    }
                    params.setBuildInOptions(buildInOptions); // buildInOptions是用来校验的，不能更改，重新回填数据库的值
                }
            }
            // 选择题 options 个数检查：单选题8个，多选题10个
            if (GroomingReportQuestionTypeEnum.single_choice.name().equals(params.getType())
                    && (CollectionUtils.isEmpty(params.getOptions())
                            || params.getOptions().size() > 8)) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "the number of pet condition options must be more than 1 and less than 8");
            }
            if ((GroomingReportQuestionTypeEnum.multi_choice.name().equals(params.getType())
                            || GroomingReportQuestionTypeEnum.tag_choice.name().equals(params.getType()))
                    && (CollectionUtils.isEmpty(params.getOptions())
                            || params.getOptions().size() > 10)) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "the number of mood options must be more than 1 and less than 10");
            }

            // 检查非删除 question 的 title 是否重复
            if (!Objects.equals(params.getStatus(), (byte) 1)) {
                if (existTitles.containsKey(params.getId())) {
                    existTitles.put(params.getId(), params.getTitle());
                } else {
                    existTitles.put(newQuestionIndex--, params.getTitle());
                }
            } else {
                existTitles.remove(params.getId());
            }
        }
        if (existTitles.size() != new HashSet<>(existTitles.values()).size()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "question title is duplicated");
        }

        // body view 题型只能有一个
        List<MoeGroomingReportQuestion> existBodyViewQuestion = existRecords.stream()
                .filter(r -> GroomingReportQuestionTypeEnum.isBodyViewQuestion(r.getType()))
                .toList();
        List<GroomingReportQuestionParams> bodyViewQuestionParam = questionParams.stream()
                .filter(p ->
                        // 非删除的 body view
                        Objects.equals(p.getStatus(), (byte) 0)
                                && GroomingReportQuestionTypeEnum.isBodyViewQuestion(p.getType()))
                .toList();
        if (
        // 新增/更新的 body view 个数大于 1
        bodyViewQuestionParam.size() > 1
                || ( // 更新的 body view 与已存在的 body view 不是同一个
                bodyViewQuestionParam.size() == 1
                        && existBodyViewQuestion.size() > 0
                        && !Objects.equals(
                                bodyViewQuestionParam.get(0).getId(),
                                existBodyViewQuestion.get(0).getId()))) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "body view question must be only one");
        }
    }

    private void checkGroomingReportContentParams(
            GroomingReportContentParams content, GroomingReportTemplateDTO template) {
        // showcase check
        if (Boolean.TRUE.equals(template.getShowShowcase())) {
            if (CollectionUtils.isEmpty(content.getShowcase())
                    || content.getShowcase().size() != 2) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Showcase question missing");
            }
        }

        // Feedbacks check
        if (Boolean.TRUE.equals(template.getShowOverallFeedback())) {
            if (CollectionUtils.isEmpty(content.getFeedbacks())
                    && !CollectionUtils.isEmpty(template.getQuestions().feedbacks())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Feedback question missing");
            } else {
                checkGroomingReportQuestionList(
                        content.getFeedbacks(), template.getQuestions().feedbacks());
            }
        }

        // PetConditions check
        if (Boolean.TRUE.equals(template.getShowPetCondition())) {
            if (CollectionUtils.isEmpty(content.getPetConditions())
                    && !CollectionUtils.isEmpty(template.getQuestions().petConditions())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet conditions question missing");
            } else {
                checkGroomingReportQuestionList(
                        content.getPetConditions(), template.getQuestions().petConditions());
            }
        }

        // Recommendation check and calculate next appointment date
        GroomingRecommendation recommendation = content.getRecommendation();
        if (Boolean.TRUE.equals(template.getShowNextAppointment())) {
            if (recommendation == null
                    || recommendation.getFrequencyDay() == null
                    || recommendation.getFrequencyType() == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Grooming frequency missing");
            }
        }
    }

    private void checkGroomingReportQuestionList(
            List<GroomingReportQuestionParams> questionList, List<GroomingReportQuestionDTO> templateQuestionList) {
        Map<Integer, GroomingReportQuestionParams> questionMap = questionList.stream()
                .collect(Collectors.toMap(GroomingReportQuestionParams::getId, Function.identity()));

        templateQuestionList.forEach(
                question -> checkGroomingReportQuestion(questionMap.get(question.getId()), question));
    }

    private void checkGroomingReportQuestion(
            GroomingReportQuestionParams questionParams, GroomingReportQuestionDTO templateQuestion) {
        // required check
        if (questionParams == null) {
            if (templateQuestion.getRequired()) {
                log.error("required question not fill in: " + JsonUtil.toJson(templateQuestion));
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "required question not fill in");
            } else {
                // 非 required 且没有传则跳过后面的检查
                return;
            }
        }
        // show check，如果没有赋值，则设置为 required
        if (questionParams.getShow() == null) {
            questionParams.setShow(templateQuestion.getRequired());
        }
        if (!questionParams.getShow()) {
            return;
        }
        // type
        if (GroomingReportQuestionTypeEnum.isChoiceQuestion(templateQuestion.getType())) {
            boolean hasCustomOptions = !CollectionUtils.isEmpty(questionParams.getCustomOptions());
            Set<String> allOptions = new HashSet<>(templateQuestion.getOptions());
            Set<String> allChoices = new HashSet<>(
                    Optional.ofNullable(questionParams.getChoices()).orElseGet(List::of));
            if (hasCustomOptions) {
                allOptions.addAll(questionParams.getCustomOptions());
                allChoices.addAll(questionParams.getCustomOptions());
            }

            // choice 数量检查：只检查单选，多选可以不选
            boolean choiceSizeCheck = true;
            if (Objects.equals(templateQuestion.getType(), GroomingReportQuestionTypeEnum.single_choice.name())
                    && !CollectionUtils.isEmpty(allChoices)) {
                choiceSizeCheck = allChoices.size() == 1;
            }
            // 检查 choice 是否在 options 和 customOptions 中
            boolean choiceCheck = allOptions.containsAll(allChoices);
            if (!choiceSizeCheck || !choiceCheck) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, String.format("question %s params error", templateQuestion.getTitle()));
            }
        }
        // text input 和 body view 暂时不用做校验
    }

    /**
     * 检查当前账号 Grooming Report 一级权限，基础功能使用
     *
     * @param businessId businessId
     */
    private void checkGroomingReportAvailable(Integer businessId) {
        if (!Boolean.TRUE.equals(
                iPaymentPlanClient.checkFeatureCodeIsEnableByBid(businessId, FeatureConst.FC_GROOMING_REPORT_LV1))) {
            throw ExceptionUtil.bizException(Code.CODE_GROOMING_REPORT_NOT_AVAILABLE, "Grooming report not available");
        }
    }

    /**
     * 检查当前账号 Grooming Report 是否有 Lv2 和 Lv3 权限，如果没有则重置部分参数
     *
     * @param params save 参数
     */
    private void checkPlanAndResetTemplateParams(GroomingReportTemplateParams params) {
        Map<String, FeatureQuotaDto> featureMap =
                iPaymentPlanClient.queryCompanyPlanFeatureByBid(params.getBusinessId());
        if (featureMap == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        // LV3 Grooming report: 暂时无使用，先注释掉
        //        if (AccountUtil.checkFeatureCodeIsEnable(FeatureConst.FC_GROOMING_REPORT_LV3, featureMap)) {
        //            return;
        //        }
        if (AccountUtil.checkFeatureCodeIsEnable(FeatureConst.FC_GROOMING_REPORT_LV2, featureMap)) {
            return;
        }
        // LV2 Grooming report 功能清空
        params.setThemeColor(null);
        params.setLightThemeColor(null);
        params.setThankYouMessage(null);
        params.setShowReviewBooster(null);
        params.setShowYelpReview(null);
        params.setYelpReviewLink(null);
        params.setShowGoogleReview(null);
        params.setGoogleReviewLink(null);
        params.setShowFacebookReview(null);
        params.setFacebookReviewLink(null);
        params.setQuestions(new GroomingReportTemplateParams.TemplateQuestionParams());
        if (AccountUtil.checkFeatureCodeIsEnable(FeatureConst.FC_GROOMING_REPORT_LV1, featureMap)) {
            return;
        }
        throw ExceptionUtil.bizException(Code.CODE_GROOMING_REPORT_NOT_AVAILABLE, "Grooming report not available");
    }

    /**
     * 检查当前账号 Grooming Report 是否有 Lv2 和 Lv3 权限，如果没有则重置部分返回字段
     *
     * @param template template
     * @param reviewBoosterConfig reviewBooster 配置
     * @param questions question 列表
     * @return 重置部分字段的 template 对象
     */
    private GroomingReportTemplateDTO checkAndConvertTemplate(
            MoeGroomingReportTemplate template,
            ReviewBoosterDTO reviewBoosterConfig,
            List<MoeGroomingReportQuestion> questions) {
        Map<String, FeatureQuotaDto> featureMap =
                iPaymentPlanClient.queryCompanyPlanFeatureByBid(template.getBusinessId());
        if (featureMap == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        // LV3 Grooming report: 暂无使用，先注释掉
        //        if (AccountUtil.checkFeatureCodeIsEnable(FeatureConst.FC_GROOMING_REPORT_LV3, featureMap)) {
        //            return GroomingReportConverter.convertTemplateAndQuestionsDTO(template, questions,
        // reviewBoosterConfig);
        //        }
        if (AccountUtil.checkFeatureCodeIsEnable(FeatureConst.FC_GROOMING_REPORT_LV2, featureMap)) {
            return GroomingReportConverter.convertTemplateAndQuestionsDTO(template, questions, reviewBoosterConfig);
        }
        // LV2 Grooming report 功能重置
        template.setThemeColor(GroomingReportConst.DEFAULT_THEME_COLOR);
        template.setLightThemeColor(GroomingReportConst.DEFAULT_LIGHT_THEME_COLOR);
        template.setThemeCode(GroomingReportConst.DEFAULT_THEME_CODE);
        template.setThankYouMessage(GroomingReportConst.DEFAULT_THANK_YOU_MESSAGE);
        template.setTitle(GroomingReportConst.DEFAULT_TITLE);
        template.setShowReviewBooster(false);
        template.setShowYelpReview(false);
        template.setShowGoogleReview(false);
        template.setShowFacebookReview(false);
        List<MoeGroomingReportQuestion> defaultQuestions = groomingReportQuestionMapper.selectByBusinessId(0);
        // 设置成当前 businessId
        defaultQuestions.forEach(q -> q.setBusinessId(template.getBusinessId()));
        // 返回最基础的设置项，不抛异常
        return GroomingReportConverter.convertTemplateAndQuestionsDTO(template, defaultQuestions, null);
    }

    /**
     * 检查 appointment 状态和 pet 是否还在 appointment 中
     *
     * @param appointment appointment 信息
     * @param groomingReportPetId grooming report pet id
     */
    private void verifyGroomingReportAppointment(
            AppointmentWithPetDetailsDto appointment, Integer groomingReportPetId) {
        if (
        // 预约不存在、被 cancelled、预约的 pet 被移除 都无法再访问这个 report
        appointment == null
                || Objects.equals(appointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())
                || appointment.getServices().stream()
                        .noneMatch(service -> Objects.equals(service.getPetId(), groomingReportPetId))) {
            throw ExceptionUtil.bizException(
                    Code.CODE_GROOMING_REPORT_NOT_AVAILABLE, "Corresponding appointment not available");
        }
    }

    /**
     * 后端标记并上传 Body view 图片
     *
     * @param petTypeId pet type id: 1-dog, 2-cat
     * @param params
     */
    private void handleBodyView(Integer petTypeId, GroomingReportContentParams params) {
        if (params == null) {
            return;
        }
        List<GroomingReportQuestionParams> questionParams = params.getPetConditions();
        if (CollectionUtils.isEmpty(questionParams)) {
            return;
        }

        GroomingReportQuestionParams bodyViewQuestion = questionParams.stream()
                .filter(Objects::nonNull)
                .filter(question -> Objects.equals(question.getType(), GroomingReportQuestionTypeEnum.body_view.name()))
                .findFirst()
                .orElse(null);

        if (bodyViewQuestion == null || CollectionUtils.isEmpty(bodyViewQuestion.getChoices())) {
            return;
        }

        BodyViewUrl bodyViewUrl = bodyViewQuestion.getUrls();
        if (bodyViewUrl == null) {
            bodyViewUrl = new BodyViewUrl();
        }

        List<String> leftChoices = new ArrayList<>();
        List<String> rightChoices = new ArrayList<>();
        bodyViewQuestion.getChoices().forEach(choice -> {
            if (choice.startsWith(BODY_VIEW_LEFT_CHOICE_PREFIX)) {
                leftChoices.add(choice.substring(
                        choice.indexOf(BODY_VIEW_LEFT_CHOICE_PREFIX) + BODY_VIEW_LEFT_CHOICE_PREFIX.length()));
            } else if (choice.startsWith(BODY_VIEW_RIGHT_CHOICE_PREFIX)) {
                rightChoices.add(choice.substring(
                        choice.indexOf(BODY_VIEW_RIGHT_CHOICE_PREFIX) + BODY_VIEW_RIGHT_CHOICE_PREFIX.length()));
            }
        });
        // 如果左边的 body view 为空，且左边的选项不为空，则上传左边的 body view
        String leftUrl = StringUtils.hasText(bodyViewUrl.getLeft()) ? bodyViewUrl.getLeft() : "";
        if (!CollectionUtils.isEmpty(leftChoices) && !StringUtils.hasText(leftUrl)) {
            try {
                leftUrl = bodyViewUtil.markedAndUploadBodyView(petTypeId, leftChoices, true);
            } catch (Exception e) {
                log.error("upload left body view error", e);
            }
        }
        // 如果右边的 body view url 为空，且右边的选项不为空，则上传右边的 body view
        String rightUrl = StringUtils.hasText(bodyViewUrl.getRight()) ? bodyViewUrl.getRight() : "";
        if (!CollectionUtils.isEmpty(rightChoices) && !StringUtils.hasText(rightUrl)) {
            try {
                rightUrl = bodyViewUtil.markedAndUploadBodyView(petTypeId, rightChoices, false);
            } catch (Exception e) {
                log.error("upload right body view error", e);
            }
        }
        bodyViewQuestion.setUrls(new BodyViewUrl(leftUrl, rightUrl));
    }

    public Boolean addUpGroomingReportOpenedCount(GroomingReportViewCountParams params) {
        if (params == null || !StringUtils.hasText(params.uuid())) {
            return false;
        }
        // share 模式暂时不统计 opened count
        if (params.uuid().startsWith(SHARE_UUID_PREFIX)) {
            return false;
        }
        return groomingReportMapper.addUpGroomingReportOpenedCount(params.uuid()) > 0;
    }

    public Boolean batchDeleteGroomingReportCard(Integer businessId, List<Integer> reportCardIds) {
        if (CollectionUtils.isEmpty(reportCardIds)) {
            return false;
        }
        MoeGroomingReportExample example = new MoeGroomingReportExample();
        example.createCriteria().andBusinessIdEqualTo(businessId).andIdIn(reportCardIds);
        groomingReportMapper.deleteByExample(example);
        return true;
    }

    public List<GroomingReportDTO> listGroomingReportCardByFilter(GetGroomingReportCardListParams params) {
        if (params.getCompanyId() == null || params.getBusinessId() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Company ID and Business ID cannot be null");
        }
        MoeGroomingReportExample example = new MoeGroomingReportExample();
        MoeGroomingReportExample.Criteria criteria = example.createCriteria();
        criteria.andCompanyIdEqualTo(params.getCompanyId())
                .andBusinessIdEqualTo(params.getBusinessId().intValue());
        if (params.getPetId() != null) {
            criteria.andPetIdEqualTo(params.getPetId().intValue());
        }
        if (params.getStatus() != null) {
            criteria.andStatusEqualTo(params.getStatus().name());
        }
        example.setOrderByClause("update_time desc");
        List<GroomingReportDTO> reportDTOS = groomingReportMapper.selectByExample(example).stream()
                .map(GroomingReportMapper.INSTANCE::entity2DTO)
                .toList();

        if (CollectionUtils.isEmpty(reportDTOS)) {
            return List.of();
        }

        if (StringUtils.hasText(params.getStartDate()) && StringUtils.hasText(params.getEndDate())) {
            List<Integer> groomingIds =
                    reportDTOS.stream().map(GroomingReportDTO::getGroomingId).toList();
            List<MoeGroomingAppointment> appointments = appointmentService.getAppointmentByIdsAndDateRange(
                    params.getBusinessId().intValue(), groomingIds, params.getStartDate(), params.getEndDate());
            reportDTOS = reportDTOS.stream()
                    .filter(report -> appointments.stream()
                            .anyMatch(appointment -> appointment.getId().equals(report.getGroomingId())))
                    .toList();
        }

        return reportDTOS;
    }

    public Boolean batchGenerateGroomingReportUuid(Integer businessId, List<Integer> reportCardIds) {
        if (CollectionUtils.isEmpty(reportCardIds)) {
            return false;
        }
        GroomingReportTemplateDTO template = getGroomingReportTemplate(businessId, false);
        List<MoeGroomingReport> moeGroomingReports =
                groomingReportMapper.selectByBusinessIdAndIds(businessId, reportCardIds);
        moeGroomingReports.forEach(moeGroomingReport -> {
            GroomingReportContentParams params =
                    JsonUtil.toBean(moeGroomingReport.getContentJson(), GroomingReportContentParams.class);
            // check
            checkGroomingReportContentParams(params, template);
            if (moeGroomingReport.getUuid() == null) {
                moeGroomingReport.setUuid(buildUniqueIdForGroomingReport());
                groomingReportMapper.updateByPrimaryKeySelective(moeGroomingReport);
            }
        });
        return true;
    }

    /**
     * 数据双写：初始化 fulfillment_report_template
     */
    private SyncFulfillmentReportTemplateResponse initFulfillmentReportTemplate(Integer businessId, Long companyId) {
        return fulfillmentReportService.syncFulfillmentReportTemplate(
            SyncFulfillmentReportTemplateRequest.newBuilder()
                .setOperation(SyncOperation.CREATE)
                .setUniqueKey(FulfillmentReportTemplateUniqueKey.newBuilder()
                    .setBusinessId(businessId)
                    .setCompanyId(companyId)
                    .setCareType(CareType.CARE_TYPE_GROOMING)
                    .build())
                .setTemplate(FulfillmentReportTemplateSync.newBuilder()
                    .setCompanyId(companyId)
                    .setBusinessId(businessId)
                    .setCareType(CareType.CARE_TYPE_GROOMING)
                    .build())
                .build());
    }

    /**
     * 数据双写：初始化 fulfillment_report_question
     */
    private BatchSyncFulfillmentReportQuestionsResponse initFulfillmentReportQuestions(
        Integer businessId, Long companyId, List<MoeGroomingReportQuestion> defaultQuestions) {
        FulfillmentReportTemplate template =
            fulfillmentReportService.getFulfillmentReportTemplate(
                GetFulfillmentReportTemplateRequest.newBuilder()
                    .setCompanyId(companyId)
                    .setBusinessId(businessId)
                    .setCareType(CareType.CARE_TYPE_GROOMING)
                    .build()).getTemplate();
        List<FulfillmentReportQuestionSync> syncs = defaultQuestions.stream()
            .map(question ->
                GroomingReportConverter.buildFulfillmentReportQuestionSync(question, template)
            )
            .toList();

        return fulfillmentReportService.batchSyncFulfillmentReportQuestions(
            BatchSyncFulfillmentReportQuestionsRequest.newBuilder()
                .setOperation(SyncOperation.CREATE)
                .setTemplateIdentifier(QuestionTemplateIdentifier.newBuilder()
                    .setTemplateId(template.getId())
                    .build())
                .addAllQuestions(syncs)
                .build());
    }

    /**
     * 数据双写：同步 fulfillment_report_question
     */
    private BatchSyncFulfillmentReportQuestionsResponse sycnFulfillmentReportQuestions(
        Integer businessId, Long companyId, MoeGroomingReportQuestion question) {
        FulfillmentReportTemplate template =
            fulfillmentReportService.getFulfillmentReportTemplate(
                GetFulfillmentReportTemplateRequest.newBuilder()
                    .setCompanyId(companyId)
                    .setBusinessId(businessId)
                    .setCareType(CareType.CARE_TYPE_GROOMING)
                    .build()).getTemplate();
        FulfillmentReportQuestionSync sync =
            GroomingReportConverter.buildFulfillmentReportQuestionSync(question, template);
        return fulfillmentReportService.batchSyncFulfillmentReportQuestions(
            BatchSyncFulfillmentReportQuestionsRequest.newBuilder()
                .setOperation(SyncOperation.UPSERT)
                .setTemplateIdentifier(QuestionTemplateIdentifier.newBuilder()
                    .setTemplateId(template.getId())
                    .build())
                .addQuestions(sync)
                .build());
    }
}
