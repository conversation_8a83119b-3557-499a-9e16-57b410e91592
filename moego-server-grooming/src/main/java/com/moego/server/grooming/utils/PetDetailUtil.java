package com.moego.server.grooming.utils;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
public class PetDetailUtil {

    public static List<Integer> getStaffIds(
            List<MoeGroomingPetDetail> petDetails, List<EvaluationServiceDetail> evaluations) {
        List<Integer> staffIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(petDetails)) {
            staffIds.addAll(petDetails.stream()
                    .map(MoeGroomingPetDetail::getStaffId)
                    .filter(Objects::nonNull)
                    .toList());
        }
        if (!CollectionUtils.isEmpty(evaluations)) {
            staffIds.addAll(evaluations.stream()
                    .map(EvaluationServiceDetail::getStaffId)
                    .filter(Objects::nonNull)
                    .map(Long::intValue)
                    .toList());
        }
        return staffIds.stream().filter(k -> k > 0).distinct().toList();
    }

    public static List<Integer> getPetIds(
            List<MoeGroomingPetDetail> petDetails, List<EvaluationServiceDetail> evaluations) {
        List<Integer> petIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(petDetails)) {
            petIds.addAll(petDetails.stream()
                    .map(MoeGroomingPetDetail::getPetId)
                    .filter(Objects::nonNull)
                    .toList());
        }
        if (!CollectionUtils.isEmpty(evaluations)) {
            petIds.addAll(evaluations.stream()
                    .map(EvaluationServiceDetail::getPetId)
                    .filter(Objects::nonNull)
                    .map(Long::intValue)
                    .toList());
        }
        return petIds.stream().filter(k -> k > 0).distinct().toList();
    }

    public static List<Integer> getServiceIds(List<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .map(MoeGroomingPetDetail::getServiceId)
                .filter(Objects::nonNull)
                .filter(k -> k > 0)
                .distinct()
                .toList();
    }

    public static List<Long> getLodgingIds(List<MoeGroomingPetDetail> petDetails) {
        List<Long> lodgingIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(petDetails)) {
            lodgingIds.addAll(petDetails.stream()
                    .map(MoeGroomingPetDetail::getLodgingId)
                    .filter(Objects::nonNull)
                    .toList());
        }
        return lodgingIds.stream().filter(k -> k > 0).distinct().toList();
    }

    public static List<String> getDays(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (datePoint(petDetail)) {
            return DateUtil.generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());
        }
        if (specificDates(petDetail)) {
            return getSpecificDates(petDetail);
        }

        var actualDatesInfo = getActualDatesInfo(petDetail, petServiceMap);
        // 对于最终关联到 boarding 上的 everyday 配置，不计算最后一天
        if (actualDatesInfo.getServiceItemType() == ServiceItemType.BOARDING_VALUE) {
            return DateUtil.generateAllDatesBetween(
                    actualDatesInfo.getStartDate(),
                    LocalDate.parse(actualDatesInfo.getEndDate()).plusDays(-1).toString());
        }
        // boarding 或者 daycare 下 everyday 配置的 addon，计算所有天数
        if (datePoint(actualDatesInfo)) {
            return DateUtil.generateAllDatesBetween(actualDatesInfo.getStartDate(), actualDatesInfo.getEndDate());
        }
        // 关联到多天的 daycare 上的 everyday 配置，计算所有天数
        return getSpecificDates(actualDatesInfo);
    }

    /**
     * 以下方法均与 moego-svc-appointment PetDetailUtil 保持一致
     */
    public static boolean isServiceTypeNormal(Integer serviceType) {
        if (serviceType == null) {
            return false;
        }
        ServiceType type = ServiceType.forNumber(serviceType);
        return type != null && !ServiceType.SERVICE_TYPE_UNSPECIFIED.equals(type);
    }

    public static Map<Integer, Map<Integer, MoeGroomingPetDetail>> getPetServiceMap(List<MoeGroomingPetDetail> all) {
        return all.stream()
                .filter(petDetail -> Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_VALUE))
                .collect(Collectors.groupingBy(
                        MoeGroomingPetDetail::getPetId,
                        Collectors.toMap(MoeGroomingPetDetail::getServiceId, Function.identity(), (p1, p2) -> p1)));
    }

    /**
     * @param petServiceMap petId -> (serviceId -> petDetail)
     */
    public static int getQuantity(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (petDetail.getServiceType() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "service type not found");
        }
        return switch (Objects.requireNonNull(ServiceType.forNumber(petDetail.getServiceType()))) {
            case SERVICE -> getServiceQuantity(petDetail, petServiceMap);
            case ADDON -> getAddOnQuantity(petDetail, petServiceMap);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "service type not found");
        };
    }

    // service 之间没有层级关系，逻辑上是多天 appointment 每天的服务类型
    private static int getServiceQuantity(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        // boarding 下支持挂载有多天的 daycare 服务
        if (Objects.equals(petDetail.getServiceItemType(), ServiceItemType.DAYCARE_VALUE)) {
            return calculateDaycareServiceQuantity(petDetail, petServiceMap);
        }
        return switch (Objects.requireNonNull(ServicePriceUnit.forNumber(petDetail.getPriceUnit()))) {
            case PER_SESSION -> 1;
            case PER_NIGHT -> calculateNights(petDetail);
            case PER_HOUR -> calculateHours(petDetail);
            case PER_DAY -> calculateDays(petDetail);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "price unit not found");
        };
    }

    private static int calculateDaycareServiceQuantity(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class).size();
        }
        if (datePoint(petDetail)) {
            return 1;
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        // every day not include the checkout day
        if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())) {
            return calculateNights(associatedService);
        }
        // every day include the checkout day
        return calculateDays(associatedService);
    }

    /**
     * Calculates the quantity of add-ons for a given pet detail.
     *
     * @param petDetail     The pet detail for which the add-on quantity is
     *                      calculated.
     * @param petServiceMap A map where the key is the petId and the value is
     *                      another map of serviceId to petDetail.
     * @return The quantity of add-ons.
     */
    public static int getAddOnQuantity(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (datePoint(petDetail)) {
            return 1;
        }
        if (specificDates(petDetail)) {
            return getSpecificDates(petDetail).size() * petDetail.getQuantityPerDay();
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.getOrDefault(petDetail.getPetId(), Map.of()));
        if (associatedService == null) {
            return 1;
        }
        if (Objects.equals(associatedService.getServiceItemType(), ServiceItemType.GROOMING_VALUE)) {
            return 1;
        }

        if (Objects.equals(associatedService.getServiceItemType(), ServiceItemType.BOARDING_VALUE)) {
            // every day not include the checkout day
            if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())) {
                return calculateNights(associatedService) * petDetail.getQuantityPerDay();
            }
            // every day include the checkout day
            return calculateDays(associatedService) * petDetail.getQuantityPerDay();
        }

        return getServiceQuantity(associatedService, petServiceMap) * petDetail.getQuantityPerDay();
    }

    /**
     * Retrieves the associated service for petDetail with specific dates or
     * everyday.
     * If an associated service ID is recorded, it returns the corresponding
     * service.
     * Otherwise, it infers the associated service based on the following logic:
     * - For service:
     * - Only supports multiple daycare services under a main boarding service
     * appointment.
     * - For addOn:
     * - Supports multiple addOns under various main service appointments.
     *
     * @param petServiceMap A map of serviceId (service type only) to serviceDetail
     *                      for a certain pet.
     * @return The associated service detail, or null if not found.
     */
    @Nullable
    public static MoeGroomingPetDetail getAssociatedService(
            MoeGroomingPetDetail petDetail, Map<Integer, MoeGroomingPetDetail> petServiceMap) {
        if (petDetail.getAssociatedServiceId() != null && petDetail.getAssociatedServiceId() > 0) {
            return petServiceMap.get(petDetail.getAssociatedServiceId().intValue());
        }
        var mainPetDetail = getMainService(petServiceMap.values());
        if (mainPetDetail == null) {
            return null;
        }

        // for addOn
        if (petDetail.getServiceType() == ServiceType.ADDON_VALUE) {
            return mainPetDetail;
        }

        // for service
        if (petDetail.getServiceType() == ServiceType.SERVICE_VALUE
                && petDetail.getServiceItemType() == ServiceItemType.DAYCARE_VALUE
                && mainPetDetail.getServiceItemType() == ServiceItemType.BOARDING_VALUE) {
            return mainPetDetail;
        }
        return null;
    }

    /**
     * Retrieves the main service from the given petDetails.
     *
     * @param petDetails A collection of petDetails for one pet.
     * @return main service
     */
    @Nullable
    public static MoeGroomingPetDetail getMainService(Collection<MoeGroomingPetDetail> petDetails) {
        var mainServiceItemType = ServiceItemEnum.getMainServiceItemType(petDetails.stream()
                .filter(v -> Objects.equals(v.getServiceType(), ServiceType.SERVICE_VALUE))
                .map(MoeGroomingPetDetail::getServiceItemType)
                .toList());
        return petDetails.stream()
                .filter(v -> Objects.equals(v.getServiceType(), ServiceType.SERVICE_VALUE))
                .filter(v -> Objects.equals(v.getServiceItemType(), mainServiceItemType.getServiceItem()))
                .findFirst()
                .orElse(null);
    }

    public static boolean datePoint(MoeGroomingPetDetail petDetail) {
        return StringUtils.hasText(petDetail.getStartDate()) && Objects.nonNull(petDetail.getStartTime());
    }

    public static boolean specificDates(MoeGroomingPetDetail petDetail) {
        return !getSpecificDates(petDetail).isEmpty();
    }

    public static boolean isSpecificDates(MoeGroomingPetDetail petDetail) {
        return Objects.equals(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE_VALUE, petDetail.getDateType())
                && !getSpecificDates(petDetail).isEmpty();
    }

    public static List<String> getSpecificDates(MoeGroomingPetDetail petDetail) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasText(petDetail.getSpecificDates())) {
            result = JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        return result;
    }

    private static int calculateNights(MoeGroomingPetDetail petDetail) {
        if (!StringUtils.hasText(petDetail.getStartDate()) || !StringUtils.hasText(petDetail.getEndDate())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "start date and end date must be specified");
        }
        int between = (int) ChronoUnit.DAYS.between(
                LocalDate.parse(petDetail.getStartDate()), LocalDate.parse(petDetail.getEndDate()));
        return between <= 0 ? 1 : between;
    }

    private static int calculateDays(MoeGroomingPetDetail petDetail) {
        if (!StringUtils.hasText(petDetail.getStartDate()) || !StringUtils.hasText(petDetail.getEndDate())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "start date and end date must be specified");
        }
        int between = (int) ChronoUnit.DAYS.between(
                LocalDate.parse(petDetail.getStartDate()), LocalDate.parse(petDetail.getEndDate()));
        return Math.max(between + 1, 1);
    }

    private static int calculateHours(MoeGroomingPetDetail petDetail) {
        if (petDetail.getStartTime() == null || petDetail.getEndTime() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "start time and end time must be specified");
        }
        LocalDateTime start =
                buildDateTime(petDetail.getStartDate(), petDetail.getStartTime().intValue());
        LocalDateTime end =
                buildDateTime(petDetail.getEndDate(), petDetail.getEndTime().intValue());
        long minutesBetween = Duration.between(start, end).toMinutes();
        return BigDecimal.valueOf(Math.ceil((double) minutesBetween / 60)).intValue();
    }

    private static LocalDateTime buildDateTime(String date, Integer minuteOfDay) {
        return LocalDateTime.of(LocalDate.parse(date), LocalTime.ofSecondOfDay(minuteOfDay * 60));
    }

    /**
     * Generate all dates between start date and end date
     * e.g. 2024-02-01 to 2024-02-03
     * return [2024-02-01, 2024-02-02]
     */
    public static List<String> generateAllDatesBetweenByNight(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        List<String> dates = new ArrayList<>();
        for (LocalDate date = start; date.isBefore(end); date = date.plusDays(1)) {
            dates.add(date.toString());
        }
        return dates;
    }

    /**
     * @return 包含静态日期信息的 petDetail，即 datePoint 或 specificDates 类型的 petDetail
     */
    static MoeGroomingPetDetail getActualDatesInfo(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (datePoint(petDetail) || specificDates(petDetail)) {
            return petDetail;
        }
        var associatedService =
                getAssociatedService(petDetail, petServiceMap.getOrDefault(petDetail.getPetId(), Map.of()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        return getActualDatesInfo(associatedService, petServiceMap);
    }

    private static List<String> getBoardingDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        return switch (Objects.requireNonNull(ServiceType.forNumber(petDetail.getServiceType()))) {
            case SERVICE -> getBoardingServiceDates(petDetail);
            case ADDON -> getBoardingAddOnDates(petDetail, petServiceMap);
            default -> List.of();
        };
    }

    private static List<String> getBoardingServiceDates(MoeGroomingPetDetail petDetail) {
        if (Objects.equals(ServicePriceUnit.PER_NIGHT.getNumber(), petDetail.getPriceUnit())) {
            return generateAllDatesBetweenByNight(petDetail.getStartDate(), petDetail.getEndDate());
        }
        return DateUtil.generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());
    }

    private static List<String> getBoardingAddOnDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }

        return getDates(petDetail, associatedService);
    }

    public static List<String> getDaycareDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        if (Objects.equals(ServiceItemType.BOARDING_VALUE, associatedService.getServiceItemType())) {
            return getDates(petDetail, associatedService);
        }
        // everyday daycare addon
        return getDaycareDates(associatedService, petServiceMap);
    }

    public static List<String> getGroomingDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        if (Objects.equals(ServiceItemType.BOARDING_VALUE, associatedService.getServiceItemType())) {
            return getDates(petDetail, associatedService);
        }
        return getDaycareDates(associatedService, petServiceMap);
    }

    /* private */ static List<String> getDates(MoeGroomingPetDetail addOn, MoeGroomingPetDetail associatedService) {
        var dateType = PetDetailDateType.forNumber(addOn.getDateType());
        var start = LocalDate.parse(associatedService.getStartDate());
        var end = LocalDate.parse(associatedService.getEndDate());
        if (start.equals(end)) {
            // 对于只有一天的预约，目前存在一些 date type 误用的问题，因此统一做一下兼容处理
            // 因为预约只有一天，所有所有的 Service & add-on 一定发生在这一天，所以直接返回 start 日期
            return List.of(start.toString());
        }
        switch (dateType) {
            case PET_DETAIL_DATE_DATE_POINT -> {
                return List.of(addOn.getStartDate());
            }
            case PET_DETAIL_DATE_SPECIFIC_DATE -> {
                return getSpecificDates(addOn);
            }
            case PET_DETAIL_DATE_EVERYDAY -> {
                // 排除 checkout day
                end = end.plusDays(-1);
            }
            case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> {
                // do nothing
            }
            case PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> {
                // 排除 check-in day
                start = start.plusDays(1);
            }
            case PET_DETAIL_DATE_LAST_DAY -> {
                start = end;
            }
            case PET_DETAIL_DATE_FIRST_DAY -> {
                end = start;
            }
            default -> {
                // do nothing
            }
        }
        return DateUtil.generateAllDatesBetween(start.toString(), end.toString());
    }

    public static List<String> getServiceDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        return switch (Objects.requireNonNull(PetDetailDTOUtil.mapServiceItemType(petDetail.getServiceItemType()))) {
            case BOARDING -> getBoardingDates(petDetail, petServiceMap);
            case DAYCARE -> getDaycareDates(petDetail, petServiceMap);
            case GROOMING -> getGroomingDates(petDetail, petServiceMap);
            case DOG_WALKING -> getGroomingDates(petDetail, petServiceMap); // same as grooming
            default -> List.of();
        };
    }

    public static List<String> getServiceActualDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (Objects.equals(ServiceItemType.BOARDING_VALUE, petDetail.getServiceItemType())
                && Objects.equals(ServiceType.SERVICE_VALUE, petDetail.getServiceType())) {
            return getBoardingServiceDates(petDetail);
        }
        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            if (datePoint(petDetail)) {
                return List.of(petDetail.getStartDate());
            }
            if (specificDates(petDetail)) {
                return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
            }
            return List.of();
        }
        return getDates(petDetail, associatedService);
    }
}
