package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingReportSetting;
import java.util.Date;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoeGroomingReportSettingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_setting
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_setting
     *
     * @mbg.generated
     */
    int insert(MoeGroomingReportSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_setting
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingReportSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_setting
     *
     * @mbg.generated
     */
    MoeGroomingReportSetting selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingReportSetting record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_report_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingReportSetting record);

    int initGroomingReportSetting(@Param("businessId") Integer businessId, @Param("companyId") Long companyId);

    MoeGroomingReportSetting selectByBusinessId(Integer businessId);

    int updatePublishTimeByBusinessId(@Param("businessId") Integer businessId, @Param("templatePublishTime") Date date);
}
