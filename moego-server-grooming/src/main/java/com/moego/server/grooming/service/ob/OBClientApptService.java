package com.moego.server.grooming.service.ob;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding;
import com.moego.server.grooming.mapstruct.PetMapper;
import com.moego.server.grooming.mapstruct.ProfileRequestMapper;
import com.moego.server.grooming.service.CompanyGroomingServiceQueryService;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.MoeAppointmentQueryService;
import com.moego.server.grooming.service.MoeGroomingReportService;
import com.moego.server.grooming.service.dto.ob.OBClientApptDTO;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Slf4j
@Service
@AllArgsConstructor
public class OBClientApptService {

    private final AppointmentMapperProxy appointmentMapper;
    private final PetDetailMapperProxy petDetailMapper;

    private final IPetClient petClient;

    private final MoeAppointmentQueryService appointmentQueryService;
    private final OBBusinessStaffService businessStaffService;
    private final CompanyGroomingServiceQueryService companyGroomingServiceQueryService;
    private final GroomingServiceService serviceService;
    private final MoeGroomingReportService groomingReportService;
    private final OBCustomerService customerService;
    private final OBServiceService obServiceService;

    public OBClientApptDTO getLastAppt(Integer customerId, MoeBusinessDto businessInfo) {
        String businessNowDate = DateUtil.convertLocalDateToDateString(
                LocalDateTime.now(), businessInfo.getTimezoneName(), DateUtil.STANDARD_DATE);
        Integer businessNowMinutes = DateUtil.getNowMinutes(businessInfo.getTimezoneName());

        OBClientApptDTO clientApptDTO = appointmentMapper.getLastFinishedApptByCustomerId(
                businessInfo.getCompanyId().longValue(), customerId, businessNowDate, businessNowMinutes);
        if (Objects.isNull(clientApptDTO)) {
            return null;
        }

        // last appt detail info
        OBClientApptDTO obClientApptDTO = fillApptDetail(clientApptDTO, businessInfo.getId());

        if (!allServiceApplicable(obClientApptDTO, businessInfo)) {
            // last appointment's service not applicable
            return null;
        }

        return obClientApptDTO;
    }

    private boolean allServiceApplicable(OBClientApptDTO obClientApptDTO, MoeBusinessDto businessInfo) {
        Map<Integer, OBClientApptDTO.OBServiceInfoDTO> serviceMap = obClientApptDTO.getServiceInfoList().stream()
                .collect(Collectors.toMap(OBClientApptDTO.OBServiceInfoDTO::getServiceId, Function.identity()));

        // petId -> Pair<serviceIdList, addOnIdList>
        Map<Integer, Pair<List<Integer>, List<Integer>>> petServiceMap = new HashMap<>();
        for (OBClientApptDTO.OBClientApptPetDetailDTO petDetail : obClientApptDTO.getPetDetails()) {
            Pair<List<Integer>, List<Integer>> serviceAndAddOn =
                    petServiceMap.getOrDefault(petDetail.getPetId(), Pair.of(new ArrayList<>(), new ArrayList<>()));
            if (Objects.equals(
                    serviceMap.get(petDetail.getServiceId()).getType().intValue(), ServiceType.SERVICE_VALUE)) {
                serviceAndAddOn.getFirst().add(petDetail.getServiceId());
            } else {
                serviceAndAddOn.getSecond().add(petDetail.getServiceId());
            }
            petServiceMap.put(petDetail.getPetId(), serviceAndAddOn);
        }

        try {
            obServiceService.checkApplicableServiceByPetId(
                    petServiceMap,
                    businessInfo.getId(),
                    businessInfo.getCompanyId().longValue(),
                    obClientApptDTO.getCustomerId());
        } catch (Exception e) {
            log.info("checkApplicableServiceByPetId result:", e);
            return false;
        }
        return true;
    }

    public OBClientApptDTO getOBClientAppt(Integer customerId, Integer apptId) {
        MoeGroomingAppointment appt = appointmentMapper.selectByPrimaryKey(apptId);
        if (Objects.isNull(appt) || !Objects.equals(appt.getCustomerId(), customerId)) {
            throw new CommonException(ResponseCodeEnum.APPOINTMENT_NOT_FOUND);
        }
        OBClientApptDTO clientApptDTO = new OBClientApptDTO();
        clientApptDTO.setId(appt.getId());
        clientApptDTO.setCustomerId(appt.getCustomerId());
        clientApptDTO.setOrderId(appt.getOrderId());
        clientApptDTO.setAppointmentDate(appt.getAppointmentDate());
        clientApptDTO.setAppointmentStartTime(appt.getAppointmentStartTime());
        clientApptDTO.setAppointmentEndTime(appt.getAppointmentEndTime());
        return fillApptDetail(clientApptDTO, appt.getBusinessId());
    }

    /**
     * 查询 grooming report 对应的预约信息
     *
     * @param customerId
     * @param reportId
     * @return
     */
    public OBClientApptDTO getOBClientApptFromGroomingReport(Integer customerId, Integer reportId) {
        GroomingReportInfoDTO reportInfo = groomingReportService.getGroomingReportInfoForOB(customerId, reportId);
        if (reportInfo == null) {
            throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND);
        }

        // 对于 OB Book again 流程，如果已有下个预约，则报错，前端跳转到 OB landing page
        CustomerGroomingAppointmentDTO nextAppointment = appointmentQueryService.getCustomerPetNextAppointment(
                reportInfo.getCompanyId(),
                reportInfo.getBusinessId(),
                reportInfo.getCustomerId(),
                reportInfo.getPetId());
        if (nextAppointment != null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_GROOMING_REPORT_BOOK_AGAIN_EXPIRED, "Already have next appointment");
        }

        OBClientApptDTO clientApptDTO = getOBClientAppt(customerId, reportInfo.getGroomingId());
        // 设置 recommend date
        clientApptDTO.setRecommendDate(
                reportInfo.getContent().getRecommendation().getNextAppointmentDate());
        // 移除非本 grooming report 的 pet service 信息
        clientApptDTO
                .getPetDetails()
                .removeIf(petDetail -> !petDetail.getPetId().equals(reportInfo.getPetId()));
        clientApptDTO.getPetInfoList().removeIf(petInfo -> !petInfo.getPetId().equals(reportInfo.getPetId()));
        return clientApptDTO;
    }

    public OBClientApptDTO fillApptDetail(OBClientApptDTO clientApptDTO, Integer businessId) {
        List<OBClientApptDTO.OBClientApptPetDetailDTO> clientApptPetDetailDTOList =
                petDetailMapper.getApptPetDetail(clientApptDTO.getId());
        clientApptDTO.setPetDetails(clientApptPetDetailDTOList);
        Set<Integer> petIdSet = new HashSet<>();
        Set<Integer> serviceIdSet = new HashSet<>();
        Set<Integer> staffIdSet = new HashSet<>();
        for (OBClientApptDTO.OBClientApptPetDetailDTO petDetailDTO : clientApptPetDetailDTOList) {
            petIdSet.add(petDetailDTO.getPetId());
            serviceIdSet.add(petDetailDTO.getServiceId());
            staffIdSet.add(petDetailDTO.getStaffId());
        }
        // fill service info
        List<OBClientApptDTO.OBServiceInfoDTO> serviceInfoList =
                getServiceInfoList(businessId, new ArrayList<>(serviceIdSet), new ArrayList<>(staffIdSet));
        clientApptDTO.setServiceInfoList(serviceInfoList);
        // fill pet detail info
        List<CustomerPetDetailDTO> petDetailDTOList = petClient.getCustomerPetListByIdList(new ArrayList<>(petIdSet));
        CustomerHasRequestDTO requestDTO =
                customerService.getCustomerHasRequestUpdate(businessId, clientApptDTO.getCustomerId());
        if (!requestDTO.hasRequestUpdate()) {
            clientApptDTO.setPetInfoList(PetMapper.INSTANCE.petDetailDTO2PetInfoDTO(petDetailDTOList));
        } else {
            Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> petProfileMap =
                    Optional.ofNullable(requestDTO.mergedProfile().getPets()).orElse(List.of()).stream()
                            .collect(Collectors.toMap(
                                    CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
            clientApptDTO.setPetInfoList(petDetailDTOList.stream()
                    .map(dto -> {
                        CustomerProfileRequestDTO.PetProfileDTO pet = petProfileMap.get(dto.getPetId());
                        if (Objects.isNull(pet)) {
                            return PetMapper.INSTANCE.petDetailDTO2PetInfoDTO(dto);
                        }
                        OBClientApptDTO.OBPetInfoDTO petInfoDTO = ProfileRequestMapper.INSTANCE.dto2PetInfoDTO(pet);
                        petInfoDTO.setPetId(dto.getPetId());
                        petInfoDTO.setStatus(dto.getStatus());
                        petInfoDTO.setLifeStatus(petInfoDTO.getLifeStatus());
                        return petInfoDTO;
                    })
                    .collect(Collectors.toList()));
        }
        // fill staff info
        clientApptDTO.setStaffInfoList(businessStaffService.getAllStaffList(businessId, new ArrayList<>(staffIdSet)));
        return clientApptDTO;
    }

    private List<OBClientApptDTO.OBServiceInfoDTO> getServiceInfoList(
            Integer businessId, List<Integer> serviceIdList, List<Integer> staffIdList) {
        // service id - available staff id
        Map<Integer, List<Integer>> serviceToStaffMap =
                businessStaffService.getServiceToStaffMap(businessId, serviceIdList, staffIdList);
        Map<Integer, List<MoeGroomingServiceBreedBinding>> serviceBreedMap =
                serviceService.getServiceBreedBindingMap(null, businessId);
        List<OBClientApptDTO.OBServiceInfoDTO> obServiceInfoDTOList = new ArrayList<>();
        List<MoeGroomingService> serviceList =
                companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                        businessId, serviceIdList);
        Map<Integer, MoeGroomingService> serviceMap =
                serviceList.stream().collect(Collectors.toMap(MoeGroomingService::getId, Function.identity()));
        for (Integer serviceId : serviceIdList) {
            OBClientApptDTO.OBServiceInfoDTO serviceInfoDTO = new OBClientApptDTO.OBServiceInfoDTO();
            MoeGroomingService service = serviceMap.get(serviceId);
            if (Objects.isNull(service)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "service not found");
            }
            BeanUtils.copyProperties(service, serviceInfoDTO);
            serviceInfoDTO.setServiceId(serviceId);
            serviceInfoDTO.setAvailableStaffIdList(serviceToStaffMap.get(serviceInfoDTO.getServiceId()));
            if (Objects.equals(serviceInfoDTO.getBreedFilter(), CommonConstant.ENABLE)) {
                var breedBindingList = serviceBreedMap.getOrDefault(serviceInfoDTO.getServiceId(), List.of());
                // 兼容老结构
                Map<Integer, String> typeBreedMap = breedBindingList.stream()
                        .collect(Collectors.toMap(
                                MoeGroomingServiceBreedBinding::getPetTypeId,
                                binding ->
                                        binding.getIsAll() ? ServiceEnum.ALL_BREEDS_VALUE : binding.getBreedNameList(),
                                (v1, v2) -> v1));
                serviceInfoDTO.setTypeBreedMap(typeBreedMap);
            }
            obServiceInfoDTOList.add(serviceInfoDTO);
        }
        return obServiceInfoDTOList;
    }
}
