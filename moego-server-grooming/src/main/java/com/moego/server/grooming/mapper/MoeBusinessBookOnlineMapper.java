package com.moego.server.grooming.mapper;

import com.moego.server.grooming.dto.ob.BookOnlineConfigDTO;
import com.moego.server.grooming.mapper.po.BusinessCompanyPO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnlineExample;
import com.moego.server.grooming.params.BusinessBookOnlineParams;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBusinessBookOnlineMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessBookOnlineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBusinessBookOnlineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int insert(MoeBusinessBookOnline record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessBookOnline record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    List<MoeBusinessBookOnline> selectByExampleWithBLOBs(MoeBusinessBookOnlineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    List<MoeBusinessBookOnline> selectByExample(MoeBusinessBookOnlineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    MoeBusinessBookOnline selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBusinessBookOnline record, @Param("example") MoeBusinessBookOnlineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeBusinessBookOnline record, @Param("example") MoeBusinessBookOnlineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBusinessBookOnline record, @Param("example") MoeBusinessBookOnlineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessBookOnline record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBusinessBookOnline record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_book_online
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessBookOnline record);

    /**
     * get Record by businessId
     * @param businessId 商户主键
     * @return
     */
    MoeBusinessBookOnline selectByBusinessId(Integer businessId);

    List<MoeBusinessBookOnline> getBusinessBookOnlineList(List<Integer> businessIdList);

    /**
     *  根据主键或者businessId修改记录
     * @param obParams 入参
     * @return
     */
    int updateInfoByPrimaryIdOrBusinessId(BusinessBookOnlineParams obParams);

    int updateInfoByBusinessId(MoeBusinessBookOnline businessBookOnline);

    /**
     * get businessId by ob name
     * @param bookOnlineName 商户主键
     * @return
     */
    BusinessCompanyPO selectByBookOnlineName(String bookOnlineName);

    List<BookOnlineConfigDTO> listAllBookOnlineName();

    void deletePaymentGroupSetting(@Param("businessId") Integer businessId);

    List<MoeBusinessBookOnline> listOnlineBookingV3Legacies();

    List<MoeBusinessBookOnline> listAll();

    /**
     * get all companyId and businessId which is_enable online booking
     */
    List<BusinessCompanyPO> selectByIsEnable();

    /**
     * get all companyId and businessId
     */
    List<BusinessCompanyPO> selectAllCompanyIdBusinessId();
}
