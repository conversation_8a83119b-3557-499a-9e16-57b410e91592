package com.moego.server.grooming.mapper;

import com.moego.common.dto.SortDto;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBookOnlineQuestionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    long countByExample(MoeBookOnlineQuestionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBookOnlineQuestionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int insert(MoeBookOnlineQuestion row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int insertSelective(MoeBookOnlineQuestion row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    List<MoeBookOnlineQuestion> selectByExampleWithBLOBs(MoeBookOnlineQuestionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    List<MoeBookOnlineQuestion> selectByExample(MoeBookOnlineQuestionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    MoeBookOnlineQuestion selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeBookOnlineQuestion row, @Param("example") MoeBookOnlineQuestionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") MoeBookOnlineQuestion row, @Param("example") MoeBookOnlineQuestionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") MoeBookOnlineQuestion row, @Param("example") MoeBookOnlineQuestionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBookOnlineQuestion row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBookOnlineQuestion row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_book_online_question
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBookOnlineQuestion row);

    /**
     * get business questions according to type
     * @param businessId id
     * @param type 1 : pet 2:pet owner
     * @return
     */
    List<MoeBookOnlineQuestion> getListByBusinessId(
            @Param("businessId") Integer businessId, @Param("type") Integer type);

    List<MoeBookOnlineQuestion> getShowQuestionsByBusinessId(
            @Param("businessId") Integer businessId, @Param("type") Integer type);

    /**
     * sort questions
     * @param sortDtos
     * @return
     */
    Integer sortMoeQuestion(@Param("sortDtos") List<SortDto> sortDtos);

    Integer getCountWithName(
            @Param(value = "businessId") Integer businessId,
            @Param(value = "name") String name,
            @Param(value = "updateId") Integer updateId,
            @Param(value = "type") Byte type);

    Integer getNextBusinessId(Integer businessId);
}
