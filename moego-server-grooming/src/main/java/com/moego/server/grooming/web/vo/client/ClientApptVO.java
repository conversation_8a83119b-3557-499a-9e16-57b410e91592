package com.moego.server.grooming.web.vo.client;

import com.moego.server.grooming.dto.AutoAssignDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Data
@Accessors(chain = true)
public class ClientApptVO {

    @Schema(description = "Ticket unique identifier")
    private String bookingId;

    @Schema(description = "Appointment date, yyyy-MM-dd")
    private String apptDate;

    @Schema(description = "Appointment start time, minute offset of the day")
    private Integer apptStartTime;

    private Boolean noStartTime;

    @Schema(description = "Appointment end time, minute offset of the day")
    private Integer apptEndTime;

    @Schema(description = "Business id")
    private Integer businessId;

    @Schema(description = "Source, 22168-ob, 22018-web, 17216-android, 17802-ios")
    private Integer source;

    @Schema(description = "Submit time, timestamp")
    private Long submitTime;

    private AutoAssignDTO autoAssign;
}
