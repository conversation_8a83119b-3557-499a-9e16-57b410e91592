package com.moego.server.grooming.service.ob;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import org.junit.jupiter.api.Test;

class OBAbandonServiceTest {

    @Test
    void testMergeWelcomePageWithUpdatePriority() {}

    @Test
    void testMergeBasicInfoWithUpdatePriority() {}

    @Test
    void testMergeSelectCareTypeWithUpdatePriority() {
        // Case 1: existingRecord has value, updateRecord does not
        MoeBookOnlineAbandonRecord existingRecord1 = new MoeBookOnlineAbandonRecord();
        existingRecord1.setCareType(ServiceItemEnum.BOARDING.name());

        MoeBookOnlineAbandonRecord updateRecord1 = new MoeBookOnlineAbandonRecord();

        OBAbandonService.mergeSelectCareTypeWithUpdatePriority(existingRecord1, updateRecord1);

        assertThat(updateRecord1.getCareType()).isEqualTo(ServiceItemEnum.BOARDING.name());

        // Case 2: existingRecord does not have value, updateRecord does
        MoeBookOnlineAbandonRecord existingRecord2 = new MoeBookOnlineAbandonRecord();

        MoeBookOnlineAbandonRecord updateRecord2 = new MoeBookOnlineAbandonRecord();
        updateRecord2.setCareType(ServiceItemEnum.BOARDING.name());

        OBAbandonService.mergeSelectCareTypeWithUpdatePriority(existingRecord2, updateRecord2);

        assertThat(updateRecord2.getCareType()).isEqualTo(ServiceItemEnum.BOARDING.name());

        // Case 3: Both existingRecord and updateRecord have different values
        MoeBookOnlineAbandonRecord existingRecord3 = new MoeBookOnlineAbandonRecord();
        existingRecord3.setCareType(ServiceItemEnum.EVALUATION.name());

        MoeBookOnlineAbandonRecord updateRecord3 = new MoeBookOnlineAbandonRecord();
        updateRecord3.setCareType(ServiceItemEnum.BOARDING.name());

        OBAbandonService.mergeSelectCareTypeWithUpdatePriority(existingRecord3, updateRecord3);

        assertThat(updateRecord3.getCareType()).isEqualTo(ServiceItemEnum.BOARDING.name());
    }

    @Test
    void testMergeSelectAddressWithUpdatePriority() {}

    @Test
    void testMergeSelectPetWithUpdatePriority() {}

    @Test
    void testMergeSelectServiceWithUpdatePriority() {}

    @Test
    void testMergeSelectGroomerWithUpdatePriority() {}

    @Test
    void testMergeSelectDateWithUpdatePriority() {
        // Case 1: existingRecord has value, updateRecord does not
        MoeBookOnlineAbandonRecord existingRecord1 = new MoeBookOnlineAbandonRecord();
        existingRecord1.setAppointmentDate("2023-10-01");

        MoeBookOnlineAbandonRecord updateRecord1 = new MoeBookOnlineAbandonRecord();

        OBAbandonService.mergeSelectDateWithUpdatePriority(existingRecord1, updateRecord1);

        assertThat(updateRecord1.getAppointmentDate()).isEqualTo("2023-10-01");

        // Case 2: existingRecord does not have value, updateRecord does
        MoeBookOnlineAbandonRecord existingRecord2 = new MoeBookOnlineAbandonRecord();

        MoeBookOnlineAbandonRecord updateRecord2 = new MoeBookOnlineAbandonRecord();
        updateRecord2.setAppointmentDate("2023-10-02");

        OBAbandonService.mergeSelectDateWithUpdatePriority(existingRecord2, updateRecord2);

        assertThat(updateRecord2.getAppointmentDate()).isEqualTo("2023-10-02");

        // Case 3: Both existingRecord and updateRecord have different values
        MoeBookOnlineAbandonRecord existingRecord3 = new MoeBookOnlineAbandonRecord();
        existingRecord3.setAppointmentDate("2023-10-01");

        MoeBookOnlineAbandonRecord updateRecord3 = new MoeBookOnlineAbandonRecord();
        updateRecord3.setAppointmentDate("2023-10-02");

        OBAbandonService.mergeSelectDateWithUpdatePriority(existingRecord3, updateRecord3);

        assertThat(updateRecord3.getAppointmentDate()).isEqualTo("2023-10-02");
    }

    @Test
    void testMergeSelectTimeWithUpdatePriority_Boarding() {
        // Case 1: existingRecord has value, updateRecord does not
        MoeBookOnlineAbandonRecord existingRecord1 = new MoeBookOnlineAbandonRecord();
        existingRecord1.setCareType(ServiceItemEnum.BOARDING.name());
        existingRecord1.setAppointmentStartTime(540);
        existingRecord1.setAppointmentEndTime(1020);

        MoeBookOnlineAbandonRecord updateRecord1 = new MoeBookOnlineAbandonRecord();

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord1, updateRecord1);

        assertThat(updateRecord1.getAppointmentStartTime()).isEqualTo(540);
        assertThat(updateRecord1.getAppointmentEndTime()).isEqualTo(1020);

        // Case 2: existingRecord does not have value, updateRecord does
        MoeBookOnlineAbandonRecord existingRecord2 = new MoeBookOnlineAbandonRecord();
        existingRecord2.setCareType(ServiceItemEnum.BOARDING.name());

        MoeBookOnlineAbandonRecord updateRecord2 = new MoeBookOnlineAbandonRecord();
        updateRecord2.setAppointmentStartTime(540);
        updateRecord2.setAppointmentEndTime(1020);

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord2, updateRecord2);

        assertThat(updateRecord2.getAppointmentStartTime()).isEqualTo(540);
        assertThat(updateRecord2.getAppointmentEndTime()).isEqualTo(1020);

        // Case 3: Both existingRecord and updateRecord have different values
        MoeBookOnlineAbandonRecord existingRecord3 = new MoeBookOnlineAbandonRecord();
        existingRecord3.setCareType(ServiceItemEnum.BOARDING.name());
        existingRecord3.setAppointmentStartTime(540);
        existingRecord3.setAppointmentEndTime(1020);

        MoeBookOnlineAbandonRecord updateRecord3 = new MoeBookOnlineAbandonRecord();
        updateRecord3.setCareType(ServiceItemEnum.BOARDING.name());
        updateRecord3.setAppointmentStartTime(600);
        updateRecord3.setAppointmentEndTime(1080);

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord3, updateRecord3);

        assertThat(updateRecord3.getAppointmentStartTime()).isEqualTo(600);
        assertThat(updateRecord3.getAppointmentEndTime()).isEqualTo(1080);
    }

    @Test
    void testMergeSelectTimeWithUpdatePriority_Daycare() {
        // Case 1: existingRecord has value, updateRecord does not
        MoeBookOnlineAbandonRecord existingRecord1 = new MoeBookOnlineAbandonRecord();
        existingRecord1.setCareType(ServiceItemEnum.DAYCARE.name());
        existingRecord1.setAppointmentStartTime(540);
        existingRecord1.setAppointmentEndTime(1020);

        MoeBookOnlineAbandonRecord updateRecord1 = new MoeBookOnlineAbandonRecord();

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord1, updateRecord1);

        assertThat(updateRecord1.getAppointmentStartTime()).isEqualTo(540);
        assertThat(updateRecord1.getAppointmentEndTime()).isEqualTo(1020);

        // Case 2: existingRecord does not have value, updateRecord does
        MoeBookOnlineAbandonRecord existingRecord2 = new MoeBookOnlineAbandonRecord();
        existingRecord2.setCareType(ServiceItemEnum.DAYCARE.name());

        MoeBookOnlineAbandonRecord updateRecord2 = new MoeBookOnlineAbandonRecord();
        updateRecord2.setAppointmentStartTime(540);
        updateRecord2.setAppointmentEndTime(1020);

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord2, updateRecord2);

        assertThat(updateRecord2.getAppointmentStartTime()).isEqualTo(540);
        assertThat(updateRecord2.getAppointmentEndTime()).isEqualTo(1020);

        // Case 3: Both existingRecord and updateRecord have different values
        MoeBookOnlineAbandonRecord existingRecord3 = new MoeBookOnlineAbandonRecord();
        existingRecord3.setCareType(ServiceItemEnum.DAYCARE.name());
        existingRecord3.setAppointmentStartTime(540);
        existingRecord3.setAppointmentEndTime(1020);

        MoeBookOnlineAbandonRecord updateRecord3 = new MoeBookOnlineAbandonRecord();
        updateRecord3.setCareType(ServiceItemEnum.DAYCARE.name());
        updateRecord3.setAppointmentStartTime(600);
        updateRecord3.setAppointmentEndTime(1080);

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord3, updateRecord3);

        assertThat(updateRecord3.getAppointmentStartTime()).isEqualTo(600);
        assertThat(updateRecord3.getAppointmentEndTime()).isEqualTo(1080);
    }

    @Test
    void testMergeSelectTimeWithUpdatePriority_Grooming() {
        // Case 1: existingRecord has value, updateRecord does not
        MoeBookOnlineAbandonRecord existingRecord1 = new MoeBookOnlineAbandonRecord();
        existingRecord1.setCareType(ServiceItemEnum.GROOMING.name());
        existingRecord1.setStaffId(10000);
        existingRecord1.setAppointmentDate("2024-10-01");
        existingRecord1.setAppointmentStartTime(540);

        MoeBookOnlineAbandonRecord updateRecord1 = new MoeBookOnlineAbandonRecord();

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord1, updateRecord1);

        assertThat(updateRecord1.getStaffId()).isEqualTo(10000);
        assertThat(updateRecord1.getAppointmentDate()).isEqualTo("2024-10-01");
        assertThat(updateRecord1.getAppointmentStartTime()).isEqualTo(540);

        // Case 2: existingRecord does not have value, updateRecord does
        MoeBookOnlineAbandonRecord existingRecord2 = new MoeBookOnlineAbandonRecord();
        existingRecord2.setCareType(ServiceItemEnum.GROOMING.name());

        MoeBookOnlineAbandonRecord updateRecord2 = new MoeBookOnlineAbandonRecord();
        updateRecord2.setStaffId(10000);
        updateRecord2.setAppointmentDate("2024-10-01");
        updateRecord2.setAppointmentStartTime(540);

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord2, updateRecord2);

        assertThat(updateRecord2.getStaffId()).isEqualTo(10000);
        assertThat(updateRecord2.getAppointmentDate()).isEqualTo("2024-10-01");
        assertThat(updateRecord2.getAppointmentStartTime()).isEqualTo(540);

        // Case 3: Both existingRecord and updateRecord have different values
        MoeBookOnlineAbandonRecord existingRecord3 = new MoeBookOnlineAbandonRecord();
        existingRecord3.setCareType(ServiceItemEnum.GROOMING.name());
        existingRecord3.setStaffId(10000);
        existingRecord3.setAppointmentDate("2024-10-01");
        existingRecord3.setAppointmentStartTime(540);

        MoeBookOnlineAbandonRecord updateRecord3 = new MoeBookOnlineAbandonRecord();
        updateRecord3.setCareType(ServiceItemEnum.GROOMING.name());
        updateRecord3.setStaffId(20000);
        updateRecord3.setAppointmentDate("2024-11-01");
        updateRecord3.setAppointmentStartTime(600);

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord3, updateRecord3);

        assertThat(updateRecord3.getStaffId()).isEqualTo(20000);
        assertThat(updateRecord3.getAppointmentDate()).isEqualTo("2024-11-01");
        assertThat(updateRecord3.getAppointmentStartTime()).isEqualTo(600);
    }

    @Test
    void testMergeSelectTimeWithUpdatePriority_Evaluation() {
        // Case 1: existingRecord has value, updateRecord does not
        MoeBookOnlineAbandonRecord existingRecord1 = new MoeBookOnlineAbandonRecord();
        existingRecord1.setCareType(ServiceItemEnum.EVALUATION.name());
        existingRecord1.setAppointmentDate("2024-10-01");
        existingRecord1.setAppointmentStartTime(540);

        MoeBookOnlineAbandonRecord updateRecord1 = new MoeBookOnlineAbandonRecord();

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord1, updateRecord1);

        assertThat(updateRecord1.getAppointmentDate()).isEqualTo("2024-10-01");
        assertThat(updateRecord1.getAppointmentStartTime()).isEqualTo(540);

        // Case 2: existingRecord does not have value, updateRecord does
        MoeBookOnlineAbandonRecord existingRecord2 = new MoeBookOnlineAbandonRecord();
        existingRecord2.setCareType(ServiceItemEnum.EVALUATION.name());

        MoeBookOnlineAbandonRecord updateRecord2 = new MoeBookOnlineAbandonRecord();
        updateRecord2.setAppointmentDate("2024-10-01");
        updateRecord2.setAppointmentStartTime(540);

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord2, updateRecord2);

        assertThat(updateRecord2.getAppointmentDate()).isEqualTo("2024-10-01");
        assertThat(updateRecord2.getAppointmentStartTime()).isEqualTo(540);

        // Case 3: Both existingRecord and updateRecord have different values
        MoeBookOnlineAbandonRecord existingRecord3 = new MoeBookOnlineAbandonRecord();
        existingRecord3.setCareType(ServiceItemEnum.EVALUATION.name());
        existingRecord3.setAppointmentDate("2024-10-01");
        existingRecord3.setAppointmentStartTime(540);

        MoeBookOnlineAbandonRecord updateRecord3 = new MoeBookOnlineAbandonRecord();
        updateRecord3.setCareType(ServiceItemEnum.EVALUATION.name());
        updateRecord3.setAppointmentDate("2024-11-01");
        updateRecord3.setAppointmentStartTime(600);

        OBAbandonService.mergeSelectTimeWithUpdatePriority(existingRecord3, updateRecord3);

        assertThat(updateRecord3.getAppointmentDate()).isEqualTo("2024-11-01");
        assertThat(updateRecord3.getAppointmentStartTime()).isEqualTo(600);
    }

    @Test
    void testMergeAdditionalPetInfoWithUpdatePriority() {}

    @Test
    void testMergePersonalInfoWithUpdatePriority() {}
}
