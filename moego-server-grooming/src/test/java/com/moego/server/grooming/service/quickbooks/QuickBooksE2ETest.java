package com.moego.server.grooming.service.quickbooks;

import com.google.protobuf.Timestamp;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.ratelimit.v1.Dimension;
import com.moego.idl.models.ratelimit.v1.Rule;
import com.moego.idl.models.ratelimit.v1.TimeSlot;
import com.moego.idl.service.ratelimit.v1.AllowRequest;
import com.moego.idl.service.ratelimit.v1.RateLimitServiceGrpc;
import com.moego.idl.service.ratelimit.v1.RegisterRulesRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
@ExtendWith(MockitoExtension.class)
public class QuickBooksE2ETest {

    @Resource
    private RateLimitServiceGrpc.RateLimitServiceBlockingStub rateLimitServiceBlockingStub;

    private static final Long QB_RATE_LIMITER_THRESHOLD = 5L;

    @BeforeEach
    public void registerRules() {
        var req = RegisterRulesRequest.newBuilder()
                .setDomain("QuickBooksService")
                .addRules(Rule.newBuilder()
                        .setName("the quick books rate limiter")
                        .addAllDimensions(List.of(
                                Dimension.newBuilder()
                                        .setLabel("BusinessID")
                                        .setExist(true)
                                        .build(),
                                Dimension.newBuilder()
                                        .setLabel("API")
                                        .setExact("QuickBooksService")
                                        .build()))
                        .setType(Rule.Type.NORMAL)
                        .setThreshold(QB_RATE_LIMITER_THRESHOLD)
                        .setTimeSlot(TimeSlot.newBuilder()
                                .setSize(1)
                                .setUnit(TimeSlot.Unit.MINUTE)
                                .build())
                        .build())
                .build();
        var resp = rateLimitServiceBlockingStub.registerRules(req);
        // 这里可以忽略resp 的成功与否, 因为每次都会注册, 只要有一次注册成功即可, 这里打个日志
        if (!resp.getSucceed()) {
            log.error("register rate limiter failed");
        }
    }

    //    @Test
    public void test_rate_limit() {
        final Integer limit = 10;
        for (var i = 0; i < limit; i++) {
            allow(1, 1);
        }
    }

    private void allow(Integer business, Integer hits) {
        int limit = 0;
        while (limit < 3) {
            var resp = rateLimitServiceBlockingStub.allow(AllowRequest.newBuilder()
                    .setDomain("QuickBooksService")
                    .setTimestamp(Timestamp.newBuilder()
                            .setSeconds(Instant.now().getEpochSecond())
                            .setNanos(Instant.now().getNano())
                            .build())
                    .setHits(hits > 0 ? hits : 1)
                    .putAllResources(Map.of("BusinessID", business.toString(), "API", "QuickBooksService"))
                    .build());

            if (resp.getAllow()) {
                log.info("rate limiter, allow");
                break;
            }
            try {
                // sleep 时长为预期 + 1s
                limit++;
                long arg = resp.getRetryAfter().getSeconds() + 1;
                log.info("rate limiter, retry after {}s", arg);
                Thread.sleep((arg) * 1000L);
            } catch (InterruptedException e) {
                log.error("rate limiter error", e);
                throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, e.getMessage());
            }
        }
    }
}
