package com.moego.server.grooming.service;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.common.enums.SubscriptionConst;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ss.SmartScheduleResultDto;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.MoeBookOnlineAvailableStaffMapper;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.dto.OBAvailableTimeDto;
import com.moego.server.grooming.service.dto.OBAvailableTimeStaffAvailableTimeDto;
import com.moego.server.grooming.service.ob.OBBusinessService;
import com.moego.server.grooming.service.ob.OBBusinessStaffService;
import com.moego.server.grooming.service.ob.OBClientService;
import com.moego.server.grooming.service.ob.OBClientTimeSlotService;
import com.moego.server.grooming.service.ob.SmartScheduleV2Service;
import com.moego.server.grooming.service.remote.ServiceManagementService;
import com.moego.server.grooming.service.remote.SmartSchedulerService;
import com.moego.server.grooming.web.vo.ob.SmartScheduleV2Request;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 * @since 2022/10/24
 */
@ExtendWith(MockitoExtension.class)
public class OBClientTimeSlotServiceTest {

    @InjectMocks
    private OBClientTimeSlotService obClientTimeSlotService;

    @Mock
    private IBusinessStaffClient iBusinessStaffClient;

    @Mock
    private MoeBookOnlineAvailableStaffMapper moeBookOnlineAvailableStaffMapper;

    @Mock
    private GroomingServiceService groomingServiceService;

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private SmartScheduleV2Service smartScheduleV2Service;

    @Mock
    private OBBusinessService businessService;

    @Mock
    private OBClientService clientService;

    @Mock
    private CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;

    @Mock
    private OBBusinessStaffService businessStaffService;

    @Mock
    MoeGroomingBookOnlineService moeGroomingBookOnlineService;

    @Mock
    private ServiceManagementService serviceManagementService;

    @Mock
    private CompanyHelper companyHelper;

    @Mock
    private SmartSchedulerService smartSchedulerService;

    private final Long COMPANY_ID = 123L;
    private final Integer BUSINESS_ID = 123;
    private final Integer STAFF_ID = 123;
    private final Set<Integer> staffIdSet = new HashSet<>();
    private final OBTimeSlotDTO timeSlotDTO = new OBTimeSlotDTO();

    @BeforeEach
    public void initialize() {
        timeSlotDTO.setBusinessId(BUSINESS_ID);
        timeSlotDTO.setDate(LocalDate.now().toString());
        timeSlotDTO.setCount(42);
        Map<Integer, List<Integer>> petServices = new HashMap<>();
        timeSlotDTO.setPetServices(petServices);
        timeSlotDTO.setServiceIds(Arrays.asList(100, 200));
        timeSlotDTO.setLat("22.53925");
        timeSlotDTO.setLng("113.9431");
        staffIdSet.add(STAFF_ID);
    }

    @Test
    public void getTimeSlotListCustomerBlocked() {
        Integer customerId = 101001;
        timeSlotDTO.setCustomerId(customerId);
        Mockito.doReturn(true).when(clientService).validCustomerBlocked(null, COMPANY_ID, BUSINESS_ID, customerId);
        Mockito.doReturn(COMPANY_ID).when(companyHelper).mustGetCompanyId(BUSINESS_ID);

        Map<String, OBAvailableTimeDto> timeSlotList = obClientTimeSlotService.getTimeSlotList(timeSlotDTO);
        Assertions.assertThat(timeSlotList)
                .as("Blocked customer has empty time slot")
                .isEmpty();
    }

    @Test
    public void getTimeSlotList() {
        timeSlotDTO.setCount(1);
        Integer CUSTOMER_ID = 101001;
        timeSlotDTO.setCustomerId(CUSTOMER_ID);

        Mockito.doReturn(false).when(clientService).validCustomerBlocked(null, COMPANY_ID, BUSINESS_ID, CUSTOMER_ID);
        Mockito.doReturn(COMPANY_ID).when(companyHelper).mustGetCompanyId(BUSINESS_ID);

        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setCompanyId(COMPANY_ID);
        businessBookOnline.setBusinessId(BUSINESS_ID);
        businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_WORKING_HOUR);
        businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
        businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
        businessBookOnline.setBookingRangeStartOffset(0);
        businessBookOnline.setBookingRangeEndOffset(360);
        businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
        Mockito.doReturn(businessBookOnline).when(businessService).getSettingInfoByBusinessId(BUSINESS_ID);

        List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
        MoeGroomingServiceDTO serviceDTO = new MoeGroomingServiceDTO();
        serviceDTO.setId(100);
        serviceDTO.setDuration(60);
        serviceDTO.setType(ServiceEnum.TYPE_SERVICE);
        serviceDTO.setIsAllStaff(CommonConstant.ENABLE);
        serviceDTOList.add(serviceDTO);
        Mockito.doReturn(serviceDTOList)
                .when(groomingServiceService)
                .getServicesByServiceIds(Mockito.anyInt(), Mockito.anyList());

        List<MoeStaffDto> staffDtoList = new ArrayList<>();
        MoeStaffDto staffDto = new MoeStaffDto();
        staffDto.setId(STAFF_ID);
        staffDto.setStatus(StaffEnum.STATUS_NORMAL);
        staffDtoList.add(staffDto);
        Mockito.doReturn(staffDtoList).when(businessStaffService).getOBAvailableStaffList(BUSINESS_ID);

        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(timeSlotDTO.getBusinessId());
        OBBusinessInfoDTO obBusinessInfoDTO = new OBBusinessInfoDTO();
        obBusinessInfoDTO.setTimezoneName("Asia/Shanghai");
        obBusinessInfoDTO.setBusinessMode(SubscriptionConst.BUSINESS_TYPE_MOBILE);
        Mockito.doReturn(obBusinessInfoDTO).when(iBusinessBusinessClient).getBusinessInfoForOB(infoIdParams);

        SmartScheduleResultDto smartScheduleVOs = new SmartScheduleResultDto();
        smartScheduleVOs.setAvailable(false);
        Mockito.doReturn(smartScheduleVOs)
                .when(smartScheduleV2Service)
                .smartScheduleV2(Mockito.anyInt(), Mockito.any(SmartScheduleV2Request.class));

        BookOnlineStaffAvailabilityDTO bookOnlineStaffAvailabilityDTO = new BookOnlineStaffAvailabilityDTO();
        bookOnlineStaffAvailabilityDTO.setStaffId(STAFF_ID);
        bookOnlineStaffAvailabilityDTO.setSyncWithWorkingHour(OnlineBookingConst.SYNC_WITH_WORKING_HOUR_ENABLE);
        Mockito.doReturn(Collections.singletonList(bookOnlineStaffAvailabilityDTO))
                .when(moeGroomingBookOnlineService)
                .getStaffOBAvailability(Mockito.anyInt(), Mockito.anyBoolean());

        List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> mockResponse = new ArrayList<>();
        Mockito.when(serviceManagementService.batchGetCustomizedService(Mockito.eq(COMPANY_ID), Mockito.anyList()))
                .thenReturn(Collections.emptyList());

        obClientTimeSlotService.getTimeSlotList(timeSlotDTO);
    }

    @Test
    public void getTimeSlotList_withFeatureFlag() {
        timeSlotDTO.setCount(1);
        Integer CUSTOMER_ID = 101001;
        timeSlotDTO.setCustomerId(CUSTOMER_ID);

        Mockito.doReturn(false).when(clientService).validCustomerBlocked(null, COMPANY_ID, BUSINESS_ID, CUSTOMER_ID);
        Mockito.doReturn(COMPANY_ID).when(companyHelper).mustGetCompanyId(BUSINESS_ID);

        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setCompanyId(COMPANY_ID);
        businessBookOnline.setBusinessId(BUSINESS_ID);
        businessBookOnline.setAvailableTimeType(OnlineBookingConst.AVAILABLE_TIME_TYPE_WORKING_HOUR);
        businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
        businessBookOnline.setBookingRangeEndType(BookOnlineDTO.BookingRangeEndType.USING_OFFSET);
        businessBookOnline.setBookingRangeStartOffset(0);
        businessBookOnline.setBookingRangeEndOffset(360);
        businessBookOnline.setShowOneAvailableTime(ServiceEnum.ONLY_SHOW_ONE_EACH_DAY_STAFF_FALSE);
        Mockito.doReturn(businessBookOnline).when(businessService).getSettingInfoByBusinessId(BUSINESS_ID);

        List<MoeGroomingServiceDTO> serviceDTOList = new ArrayList<>();
        MoeGroomingServiceDTO serviceDTO = new MoeGroomingServiceDTO();
        serviceDTO.setId(100);
        serviceDTO.setDuration(60);
        serviceDTO.setType(ServiceEnum.TYPE_SERVICE);
        serviceDTO.setIsAllStaff(CommonConstant.ENABLE);
        serviceDTOList.add(serviceDTO);
        Mockito.doReturn(serviceDTOList)
                .when(groomingServiceService)
                .getServicesByServiceIds(Mockito.anyInt(), Mockito.anyList());

        List<MoeStaffDto> staffDtoList = new ArrayList<>();
        MoeStaffDto staffDto = new MoeStaffDto();
        staffDto.setId(STAFF_ID);
        staffDto.setStatus(StaffEnum.STATUS_NORMAL);
        staffDtoList.add(staffDto);
        Mockito.doReturn(staffDtoList).when(businessStaffService).getOBAvailableStaffList(BUSINESS_ID);

        InfoIdParams infoIdParams = new InfoIdParams();
        infoIdParams.setInfoId(timeSlotDTO.getBusinessId());
        OBBusinessInfoDTO obBusinessInfoDTO = new OBBusinessInfoDTO();
        obBusinessInfoDTO.setTimezoneName("Asia/Shanghai");
        obBusinessInfoDTO.setBusinessMode(SubscriptionConst.BUSINESS_TYPE_MOBILE);
        Mockito.doReturn(obBusinessInfoDTO).when(iBusinessBusinessClient).getBusinessInfoForOB(infoIdParams);

        SmartScheduleResultDto smartScheduleVOs = new SmartScheduleResultDto();
        smartScheduleVOs.setAvailable(false);
        Mockito.doReturn(smartScheduleVOs)
                .when(smartScheduleV2Service)
                .smartScheduleV2(Mockito.anyInt(), Mockito.any(SmartScheduleV2Request.class));

        BookOnlineStaffAvailabilityDTO bookOnlineStaffAvailabilityDTO = new BookOnlineStaffAvailabilityDTO();
        bookOnlineStaffAvailabilityDTO.setStaffId(STAFF_ID);
        bookOnlineStaffAvailabilityDTO.setSyncWithWorkingHour(OnlineBookingConst.SYNC_WITH_WORKING_HOUR_ENABLE);
        Mockito.doReturn(Collections.singletonList(bookOnlineStaffAvailabilityDTO))
                .when(moeGroomingBookOnlineService)
                .getStaffOBAvailability(Mockito.anyInt(), Mockito.anyBoolean());

        List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> mockResponse = new ArrayList<>();
        Mockito.when(serviceManagementService.batchGetCustomizedService(Mockito.eq(COMPANY_ID), Mockito.anyList()))
                .thenReturn(Collections.emptyList());

        obClientTimeSlotService.getTimeSlotList(timeSlotDTO);
    }

    @Test
    public void generateAvailableTimePointWorkDay() {
        OBAvailableTimeStaffAvailableTimeDto timeList = new OBAvailableTimeStaffAvailableTimeDto();
        List<Integer> amList = new ArrayList<>();
        List<Integer> pmList = new ArrayList<>();
        timeList.setAm(amList);
        timeList.setPm(pmList);
        List<TimeRangeDto> timeRangeDtoList = new ArrayList<>();
        TimeRangeDto timeRangeDto = new TimeRangeDto();
        timeRangeDto.setStartTime(540);
        timeRangeDto.setEndTime(1140);
        timeRangeDtoList.add(timeRangeDto);
        int appointInterval = 30;
        int totalDuration = 60;
        LocalDate currentDate = LocalDate.now();
        LocalDateTime nowDateTime = LocalDateTime.of(currentDate, LocalTime.of(12, 0));
        String workDay = currentDate.toString();

        obClientTimeSlotService.generateAvailableTimePoint(
                timeRangeDtoList, appointInterval, totalDuration, timeList, nowDateTime, workDay);
        Assertions.assertThat(amList).isEmpty();
    }

    @Test
    public void generateAvailableTimePointNotWorkDay() {
        OBAvailableTimeStaffAvailableTimeDto timeList = new OBAvailableTimeStaffAvailableTimeDto();
        List<Integer> amList = new ArrayList<>();
        List<Integer> pmList = new ArrayList<>();
        timeList.setAm(amList);
        timeList.setPm(pmList);
        List<TimeRangeDto> timeRangeDtoList = new ArrayList<>();
        TimeRangeDto timeRangeDto = new TimeRangeDto();
        timeRangeDto.setStartTime(540);
        timeRangeDto.setEndTime(1140);
        timeRangeDtoList.add(timeRangeDto);
        int appointInterval = 30;
        int totalDuration = 60;
        LocalDate currentDate = LocalDate.now();
        LocalDateTime nowDateTime = LocalDateTime.of(currentDate, LocalTime.of(12, 0));
        String workDay = currentDate.plusDays(1).toString();

        obClientTimeSlotService.generateAvailableTimePoint(
                timeRangeDtoList, appointInterval, totalDuration, timeList, nowDateTime, workDay);
        Assertions.assertThat(amList).isNotEmpty();
    }
}
