package com.moego.server.grooming.service.ob;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.exception.CommonException;
import com.moego.lib.utils.model.Pair;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService;
import com.moego.server.grooming.service.GroomingServiceService;
import com.moego.server.grooming.service.dto.ob.PetAvailableDTO;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OBClientTimeSlotServiceTest {

    @Mock
    private GroomingServiceService groomingServiceService;

    @InjectMocks
    private OBClientTimeSlotService obClientTimeSlotService;

    private Integer businessId;
    private Set<Integer> obAvailableStaffIdList;

    private static final Byte NOT_ALL_STAFF = 0;

    @BeforeEach
    void setUp() {
        businessId = 1;
        obAvailableStaffIdList = new HashSet<>(List.of(1, 2, 3));
    }

    @Test
    void getAvailableStaffIdByService_whenServiceIsAllStaff_returnsAllAvailableStaff() {
        // Arrange
        MoeGroomingServiceDTO service = new MoeGroomingServiceDTO();
        service.setId(1);
        service.setType(ServiceEnum.TYPE_SERVICE);
        service.setIsAllStaff(CustomerContactEnum.IS_ALL_STAFF);

        // Act
        Set<Integer> result = obClientTimeSlotService.getAvailableStaffIdByServiceNew(
                businessId, obAvailableStaffIdList, List.of(service));

        // Assert
        assertThat(result).isEqualTo(obAvailableStaffIdList);
    }

    @Test
    void getAvailableStaffIdByService_whenServiceHasSpecificStaff_returnsIntersection() {
        // Arrange
        MoeGroomingServiceDTO service = new MoeGroomingServiceDTO();
        service.setId(1);
        service.setType(ServiceEnum.TYPE_SERVICE);
        service.setIsAllStaff(NOT_ALL_STAFF);

        List<MoeBookOnlineStaffService> staffServices = List.of(createStaffService(1, 1), createStaffService(1, 2));

        when(groomingServiceService.getServiceStaffByServiceIds(eq(businessId), any()))
                .thenReturn(staffServices);

        // Act
        Set<Integer> result = obClientTimeSlotService.getAvailableStaffIdByServiceNew(
                businessId, obAvailableStaffIdList, List.of(service));

        // Assert
        assertThat(result).containsExactlyInAnyOrder(1, 2);
    }

    @Test
    void getAvailableStaffIdByService_whenMultipleServices_returnsIntersection() {
        // Arrange
        MoeGroomingServiceDTO service1 = new MoeGroomingServiceDTO();
        service1.setId(1);
        service1.setType(ServiceEnum.TYPE_SERVICE);
        service1.setIsAllStaff(NOT_ALL_STAFF);

        MoeGroomingServiceDTO service2 = new MoeGroomingServiceDTO();
        service2.setId(2);
        service2.setType(ServiceEnum.TYPE_SERVICE);
        service2.setIsAllStaff(NOT_ALL_STAFF);

        List<MoeBookOnlineStaffService> staffServices = List.of(
                createStaffService(1, 1), createStaffService(1, 2), createStaffService(2, 2), createStaffService(2, 3));

        when(groomingServiceService.getServiceStaffByServiceIds(eq(businessId), any()))
                .thenReturn(staffServices);

        // Act
        Set<Integer> result = obClientTimeSlotService.getAvailableStaffIdByServiceNew(
                businessId, obAvailableStaffIdList, List.of(service1, service2));

        // Assert
        assertThat(result).containsExactly(2);
    }

    @Test
    void getAvailableStaffIdByService_whenNoAvailableStaff_throwsException() {
        // Arrange
        MoeGroomingServiceDTO service = new MoeGroomingServiceDTO();
        service.setId(1);
        service.setType(ServiceEnum.TYPE_SERVICE);
        service.setIsAllStaff(NOT_ALL_STAFF);

        List<MoeBookOnlineStaffService> staffServices = List.of(createStaffService(1, 4), createStaffService(1, 5));

        when(groomingServiceService.getServiceStaffByServiceIds(eq(businessId), any()))
                .thenReturn(staffServices);

        // Act & Assert
        assertThrows(
                CommonException.class,
                () -> obClientTimeSlotService.getAvailableStaffIdByServiceNew(
                        businessId, obAvailableStaffIdList, List.of(service)));
    }

    private MoeBookOnlineStaffService createStaffService(Integer serviceId, Integer staffId) {
        MoeBookOnlineStaffService staffService = new MoeBookOnlineStaffService();
        staffService.setServiceId(serviceId);
        staffService.setStaffId(staffId);
        return staffService;
    }

    @Nested
    @DisplayName("GetSequenceAvailableTime Tests")
    class GetSequenceAvailableTimeTest {

        @Test
        void getSequenceAvailableTime_whenBothTimesHaveMatchingPets_returnsValidPair() throws Exception {
            // Arrange
            Integer startTime = 540; // 9:00 AM
            Integer previousTime = 480; // 8:00 AM
            HashSet<Integer> petIndexSet = new HashSet<>(Set.of(1, 2));

            // 创建 timeToAvailablePet 映射
            Map<Integer, PetAvailableDTO> timeToAvailablePet = new HashMap<>();

            // startTime 的可用宠物数据
            PetAvailableDTO petAvailable1 = new PetAvailableDTO();
            petAvailable1.setPetAvailableSubList(List.of(
                    Set.of(1), // 第一个子列表包含宠物1
                    Set.of(2) // 第二个子列表包含宠物2
                    ));
            timeToAvailablePet.put(startTime, petAvailable1);

            // previousTime 的可用宠物数据
            PetAvailableDTO petAvailable2 = new PetAvailableDTO();
            petAvailable2.setPetAvailableSubList(List.of(
                    Set.of(2), // 第一个子列表包含宠物2
                    Set.of(1) // 第二个子列表包含宠物1
                    ));
            timeToAvailablePet.put(previousTime, petAvailable2);

            // Act - 使用反射调用私有方法
            Method method = OBClientTimeSlotService.class.getDeclaredMethod(
                    "getSequenceAvailableTime", Integer.class, Integer.class, Map.class, HashSet.class);
            method.setAccessible(true);

            @SuppressWarnings("unchecked")
            Pair<Pair<Integer, Set<Integer>>, Pair<Integer, Set<Integer>>> result =
                    (Pair<Pair<Integer, Set<Integer>>, Pair<Integer, Set<Integer>>>)
                            method.invoke(null, startTime, previousTime, timeToAvailablePet, petIndexSet);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.key().key()).isEqualTo(previousTime); // 前一个时间
            assertThat(result.key().value()).isEqualTo(Set.of(2)); // 前一个时间的宠物集合
            assertThat(result.value().key()).isEqualTo(startTime); // 开始时间
            assertThat(result.value().value()).isEqualTo(Set.of(1)); // 开始时间的宠物集合
        }

        @Test
        void getSequenceAvailableTime_whenNoMatchingPets_returnsNull() throws Exception {
            // Arrange
            Integer startTime = 540;
            Integer previousTime = 480;
            HashSet<Integer> petIndexSet = new HashSet<>(Set.of(1, 2));

            Map<Integer, PetAvailableDTO> timeToAvailablePet = new HashMap<>();

            // startTime 只有宠物3可用
            PetAvailableDTO petAvailable1 = new PetAvailableDTO();
            petAvailable1.setPetAvailableSubList(List.of(Set.of(3)));
            timeToAvailablePet.put(startTime, petAvailable1);

            // previousTime 只有宠物4可用
            PetAvailableDTO petAvailable2 = new PetAvailableDTO();
            petAvailable2.setPetAvailableSubList(List.of(Set.of(4)));
            timeToAvailablePet.put(previousTime, petAvailable2);

            // Act
            Method method = OBClientTimeSlotService.class.getDeclaredMethod(
                    "getSequenceAvailableTime", Integer.class, Integer.class, Map.class, HashSet.class);
            method.setAccessible(true);

            Object result = method.invoke(null, startTime, previousTime, timeToAvailablePet, petIndexSet);

            // Assert
            assertNull(result);
        }

        @Test
        void getSequenceAvailableTime_whenTimeToAvailablePetIsEmpty_returnsNull() throws Exception {
            // Arrange
            Integer startTime = 540;
            Integer previousTime = 480;
            HashSet<Integer> petIndexSet = new HashSet<>(Set.of(1, 2));
            Map<Integer, PetAvailableDTO> timeToAvailablePet = new HashMap<>();

            // Act
            Method method = OBClientTimeSlotService.class.getDeclaredMethod(
                    "getSequenceAvailableTime", Integer.class, Integer.class, Map.class, HashSet.class);
            method.setAccessible(true);

            Object result = method.invoke(null, startTime, previousTime, timeToAvailablePet, petIndexSet);

            // Assert
            assertNull(result);
        }

        @Test
        void getSequenceAvailableTime_whenPetAvailableSubListIsNull_returnsNull() throws Exception {
            // Arrange
            Integer startTime = 540;
            Integer previousTime = 480;
            HashSet<Integer> petIndexSet = new HashSet<>(Set.of(1, 2));

            Map<Integer, PetAvailableDTO> timeToAvailablePet = new HashMap<>();

            // 创建 PetAvailableDTO 但不设置 petAvailableSubList (保持为null)
            PetAvailableDTO petAvailable1 = new PetAvailableDTO();
            PetAvailableDTO petAvailable2 = new PetAvailableDTO();

            timeToAvailablePet.put(startTime, petAvailable1);
            timeToAvailablePet.put(previousTime, petAvailable2);

            // Act
            Method method = OBClientTimeSlotService.class.getDeclaredMethod(
                    "getSequenceAvailableTime", Integer.class, Integer.class, Map.class, HashSet.class);
            method.setAccessible(true);

            Object result = method.invoke(null, startTime, previousTime, timeToAvailablePet, petIndexSet);

            // Assert
            assertNull(result);
        }

        @Test
        void getSequenceAvailableTime_whenPartialMatch_returnsValidPair() throws Exception {
            // Arrange
            Integer startTime = 540;
            Integer previousTime = 480;
            HashSet<Integer> petIndexSet = new HashSet<>(Set.of(1, 2, 3));

            Map<Integer, PetAvailableDTO> timeToAvailablePet = new HashMap<>();

            // startTime 可以处理宠物1和2
            PetAvailableDTO petAvailable1 = new PetAvailableDTO();
            petAvailable1.setPetAvailableSubList(List.of(Set.of(1, 2)));
            timeToAvailablePet.put(startTime, petAvailable1);

            // previousTime 可以处理宠物3
            PetAvailableDTO petAvailable2 = new PetAvailableDTO();
            petAvailable2.setPetAvailableSubList(List.of(Set.of(3)));
            timeToAvailablePet.put(previousTime, petAvailable2);

            // Act
            Method method = OBClientTimeSlotService.class.getDeclaredMethod(
                    "getSequenceAvailableTime", Integer.class, Integer.class, Map.class, HashSet.class);
            method.setAccessible(true);

            @SuppressWarnings("unchecked")
            Pair<Pair<Integer, Set<Integer>>, Pair<Integer, Set<Integer>>> result =
                    (Pair<Pair<Integer, Set<Integer>>, Pair<Integer, Set<Integer>>>)
                            method.invoke(null, startTime, previousTime, timeToAvailablePet, petIndexSet);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.key().key()).isEqualTo(previousTime);
            assertThat(result.key().value()).isEqualTo(Set.of(3));
            assertThat(result.value().key()).isEqualTo(startTime);
            assertThat(result.value().value()).isEqualTo(Set.of(1, 2));
        }

        @Test
        void getSequenceAvailableTime_whenPartialMatch_returnsMultiValidPairs() throws Exception {
            // Arrange
            Integer startTime = 540;
            Integer previousTime = 480;
            HashSet<Integer> petIndexSet = new HashSet<>(Set.of(1, 2, 3));

            Map<Integer, PetAvailableDTO> timeToAvailablePet = new HashMap<>();

            // startTime 可以处理两只宠物
            PetAvailableDTO petAvailable1 = new PetAvailableDTO();
            petAvailable1.setPetAvailableSubList(List.of(Set.of(1, 2), Set.of(1, 3), Set.of(2, 3)));
            timeToAvailablePet.put(startTime, petAvailable1);

            // previousTime 可以处理一只宠物3
            PetAvailableDTO petAvailable2 = new PetAvailableDTO();
            petAvailable2.setPetAvailableSubList(List.of(Set.of(1), Set.of(2), Set.of(3)));
            timeToAvailablePet.put(previousTime, petAvailable2);

            // Act
            Method method = OBClientTimeSlotService.class.getDeclaredMethod(
                    "getSequenceAvailableTime", Integer.class, Integer.class, Map.class, HashSet.class);
            method.setAccessible(true);

            @SuppressWarnings("unchecked")
            Pair<Pair<Integer, Set<Integer>>, Pair<Integer, Set<Integer>>> result =
                    (Pair<Pair<Integer, Set<Integer>>, Pair<Integer, Set<Integer>>>)
                            method.invoke(null, startTime, previousTime, timeToAvailablePet, petIndexSet);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.key().key()).isEqualTo(previousTime);
            assertThat(result.key().value()).isEqualTo(Set.of(3));
            assertThat(result.value().key()).isEqualTo(startTime);
            assertThat(result.value().value()).isEqualTo(Set.of(1, 2));
        }
    }
}
