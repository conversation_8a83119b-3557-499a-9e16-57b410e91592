package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.server.grooming.dto.InvoiceSummaryDTO;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.params.InvoiceAmountVo;
import com.moego.server.payment.client.IPaymentPaymentClient;
import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class InvoiceServiceTest {

    @Mock
    private OrderService orderService;

    @Mock
    private MoeGroomingAppointmentMapper appointmentMapper;

    @Mock
    private IPaymentPaymentClient paymentService;

    @Mock
    private MoePetDetailService petDetailService;

    @InjectMocks
    private InvoiceService invoiceService;

    private MoeGroomingInvoice sampleInvoice;
    private MoeGroomingAppointment sampleAppointment;

    @BeforeEach
    void setUp() {
        sampleInvoice = new MoeGroomingInvoice();
        sampleInvoice.setId(1);
        sampleInvoice.setBusinessId(100);
        sampleInvoice.setGroomingId(200);
        sampleInvoice.setCustomerId(300);
        sampleInvoice.setTotalAmount(new BigDecimal("100.00"));

        sampleAppointment = new MoeGroomingAppointment();
        sampleAppointment.setId(200);
        sampleAppointment.setBusinessId(100);
        sampleAppointment.setCustomerId(300);
    }

    @Test
    void getById_ShouldReturnInvoiceSummary() {
        // Arrange
        when(orderService.getOrderWithItemsById(anyInt())).thenReturn(sampleInvoice);
        when(orderService.convertToSummaryDTO(any())).thenReturn(new InvoiceSummaryDTO());

        // Act
        InvoiceSummaryDTO result = invoiceService.getById(1, null);

        // Assert
        assertThat(result).isNotNull();
        verify(orderService).getOrderWithItemsById(1);
        verify(orderService).convertToSummaryDTO(any());
    }

    @Test
    void setTips_ShouldUpdateTipsAndReturnInvoice() {
        // Arrange
        InvoiceAmountVo param = new InvoiceAmountVo();
        param.setInvoiceId(1);
        param.setValue(new BigDecimal("10.00"));

        when(orderService.getOrderWithItemsById(anyInt())).thenReturn(sampleInvoice);
        when(orderService.convertToSummaryDTO(any())).thenReturn(new InvoiceSummaryDTO());

        // Act
        InvoiceSummaryDTO result = invoiceService.setTips(100, 200, param);

        // Assert
        assertThat(result).isNotNull();
        verify(orderService).setTips(100, 200, param);
    }

    @Test
    void placeOrder_WhenInvoiceNotCompleted_ShouldUpdateStatus() {
        // Arrange
        sampleInvoice.setStatus(InvoiceStatusEnum.INVOICE_STATUS_CREATED);
        when(orderService.getOrderWithItemsById(anyInt())).thenReturn(sampleInvoice);
        when(orderService.convertToSummaryDTO(any())).thenReturn(new InvoiceSummaryDTO());

        // Act
        InvoiceSummaryDTO result = invoiceService.placeOrder(1);

        // Assert
        assertThat(result).isNotNull();
        verify(orderService).updateOrderStatus(1, InvoiceStatusEnum.INVOICE_STATUS_PROCESSING);
    }

    @Test
    void selectListByIds_ShouldReturnInvoiceSummaries() {
        // Arrange
        List<Integer> ids = List.of(1, 2, 3);
        when(orderService.getListByIds(any())).thenReturn(List.of(sampleInvoice));

        // Act
        List<InvoiceSummaryDTO> results = invoiceService.selectInvoicesByIds(ids);

        // Assert
        assertThat(results).isNotEmpty();
        assertThat(results).hasSize(1);
        verify(orderService).getListByIds(ids);
    }
}
