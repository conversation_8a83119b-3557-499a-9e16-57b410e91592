load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "module",
    srcs = ["module.go"],
    importpath = "github.com/bazelbuild/bazel-gazelle/internal/module",
    visibility = ["//:__subpackages__"],
    deps = [
        "//label",
        "@com_github_bazelbuild_buildtools//build",
    ],
)

filegroup(
    name = "all_files",
    testonly = True,
    srcs = [
        "BUILD.bazel",
        "module.go",
        "module_test.go",
    ],
    visibility = ["//visibility:public"],
)

alias(
    name = "go_default_library",
    actual = ":module",
    visibility = ["//:__subpackages__"],
)

go_test(
    name = "module_test",
    srcs = ["module_test.go"],
    data = glob(
        ["testdata/**"],
        allow_empty = True,
    ),
    embed = [":module"],
    deps = [
        "@com_github_google_go_cmp//cmp",
        "@io_bazel_rules_go//go/runfiles:go_default_library",
    ],
)
