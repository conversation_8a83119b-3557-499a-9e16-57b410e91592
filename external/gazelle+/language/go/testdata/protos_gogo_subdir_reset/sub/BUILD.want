load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "protos_gogo_proto",
    srcs = ["bar.proto"],
    _gazelle_imports = [],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "protos_gogo_go_proto",
    _gazelle_imports = [],
    importpath = "example.com/repo/protos_gogo",
    proto = ":protos_gogo_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "protos_gogo",
    _gazelle_imports = [],
    embed = [":protos_gogo_go_proto"],
    importpath = "example.com/repo/protos_gogo",
    visibility = ["//visibility:public"],
)
