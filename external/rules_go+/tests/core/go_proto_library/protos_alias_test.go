/* Copyright 2020 The Bazel Authors. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package protos_alias_test

import (
	"myalias/protos" // importing based on the alias import path
	"testing"
)

func use(interface{}) {}

func TestProtos(t *testing.T) {
	// just make sure both types exist
	use(protos.A{})
	use(protos.B{})
}
