load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "defers_lib",
    srcs = ["main.go"],
    importpath = "golang.org/x/tools/go/analysis/passes/defers/cmd/defers",
    visibility = ["//visibility:private"],
    deps = [
        "//go/analysis/passes/defers",
        "//go/analysis/singlechecker",
    ],
)

go_binary(
    name = "defers",
    embed = [":defers_lib"],
    visibility = ["//visibility:public"],
)
