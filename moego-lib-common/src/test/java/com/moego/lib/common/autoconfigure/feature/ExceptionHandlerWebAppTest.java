package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.exception.grpc.GrpcExceptionAdvice;
import com.moego.lib.common.exception.grpc.error.GrpcExceptionServerInterceptor;
import com.moego.lib.common.exception.http.FeignDecoderExceptionAdvice;
import com.moego.lib.common.exception.http.HttpExceptionAdvice;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link ExceptionHandler} tester.
 */
public class ExceptionHandlerWebAppTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(ExceptionHandler.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(HttpExceptionAdvice.class);
            assertThat(context).hasSingleBean(FeignDecoderExceptionAdvice.class);
            assertThat(context).hasSingleBean(GrpcExceptionServerInterceptor.class);
            assertThat(context).hasSingleBean(GrpcExceptionAdvice.class);
        });
    }

    @Test
    public void testHttpDisabled() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(HttpExceptionAdvice.class);
        });
    }

    @Test
    public void testHttpEnabledButHttpServerDisabled() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".server.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(HttpExceptionAdvice.class);
                });
    }

    @Test
    public void testGrpcDisabled() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(GrpcExceptionAdvice.class);
        });
    }

    @Test
    public void testGrpcEnabledButGrpcServerDisabled() {
        runner.withPropertyValues(GrpcProperties.PREFIX + ".server.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(GrpcExceptionAdvice.class);
                });
    }
}
