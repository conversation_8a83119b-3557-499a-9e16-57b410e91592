name: API integration test workflow
run-name: Run API integration testing triggered by ${{ inputs.source_repo || github.repository }}${{ (inputs.distinct_id && format(' [{0}]', inputs.distinct_id)) || '' }}

on:
  schedule:
    - cron: "0 0 * * *" # testing (UTC 0:00 -> UTC+8 08:00)
  #    - cron: '0 2 * * *'  # staging (UTC 2:00 -> UTC+8 10:00)
  #    - cron: '0 3 * * *'  # production (UTC 3:00 -> UTC+8 11:00)
  workflow_dispatch:
    inputs:
      env:
        description: "Environment to run tests against"
        type: choice
        required: true
        default: "testing"
        options:
          - testing
          - staging
          - production
      grey_name:
        description: "Grey domain name (branch domain at server side), only required when env is grey"
        required: false
      def_branch:
        description: "Branch name of moego-api-definitions"
        required: false
        default: ""
      test_tags:
        description: "Tags to run tests, separated by comma"
        required: false
        default: ""
      source_repo:
        description: "Source repository that trigger this workflow"
        required: false
        default: ""
      source_sha:
        description: "Source SHA that trigger this workflow"
        required: false
        default: ""
      distinct_id:
        description: "Distinct ID for this run, no need to input, will be injected automatically"
        required: false

jobs:
  api-integration-testing:
    runs-on: moego-runner-arm-8x32
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Run API Integration Tests
        uses: ./.github/actions/api-integration-test
        with:
          env: ${{ github.event.inputs.env || (github.event.schedule == '0 0 * * *' && 'testing') || (github.event.schedule == '0 2 * * *' && 'staging') || (github.event.schedule == '0 3 * * *' && 'production') }}
          grey_name: ${{ github.event.inputs.grey_name || '' }}
          def_branch: ${{ github.event.inputs.def_branch || '' }}
          test_tags: ${{ github.event.inputs.test_tags || (github.event.schedule == '0 0 * * *' && '-staging_only,-production_only,-invoke_stripe') || '' }}
          source_repo: ${{ github.event.inputs.source_repo || github.repository }}
          source_sha: ${{ github.event.inputs.source_sha || github.sha }}
          ADMIN_TOKEN_GITHUB: ${{ secrets.ADMIN_TOKEN_GITHUB }}
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
