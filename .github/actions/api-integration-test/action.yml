name: 'Run API Integration Tests'
description: 'Reusable step to run API integration tests'

inputs:
  env:
    description: 'Environment to run tests against'
    required: true
    default: 'testing'
  grey_name:
    description: 'Grey domain name (branch domain at server side for testing env)'
    required: false
  def_branch:
    description: 'Branch name of moego-api-definitions'
    required: false
    default: ''
  source_repo:
    description: 'Source repository that trigger this workflow'
    required: false
    default: 'moego'
  source_sha:
    description: 'Source SHA that trigger this workflow'
    required: false
    default: ''
  test_tags:
    description: 'Tags to run tests against'
    required: false
    default: ''
  ADMIN_TOKEN_GITHUB:
    description: 'Personal access token'
    required: true
  SLACK_BOT_TOKEN:
    description: 'Slack bot token'
    required: true

runs:
  using: "composite"
  steps:
    - name: Check inputs
      shell: bash
      run: |
        echo "Running tests against ${{ inputs.env }}"
        echo "Grey name: ${{ inputs.grey_name }}"
        echo "Branch name of moego-api-definitions: ${{ inputs.def_branch }}"

    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup GitHub
      uses: MoeGolibrary/moego-actions-tool/.github/actions/setup-github/@production
      with:
        ADMIN_TOKEN_GITHUB: ${{ inputs.ADMIN_TOKEN_GITHUB }}

    - name: Setup Go
      uses: actions/setup-go@v5
      with:
        go-version-file: 'go.mod'
        cache: false
    - name: Check Go version
      shell: bash
      run: go version

    - name: Install openapi-generator-cli
      shell: bash
      run: |
        mkdir -p ~/.npm-global
        npm config set prefix '~/.npm-global'
        echo "$HOME/.npm-global/bin" >> $GITHUB_PATH
        npm install -g @openapitools/openapi-generator-cli
        

    - name: Init repo
      shell: bash
      run: |
        mkdir -p /tmp/bazel-sandbox
        make api-test-init GREY_NAME=${{ inputs.grey_name }} DEF_BRANCH=${{ inputs.def_branch }}

    - name: Run API integration test
      id: run-test
      shell: bash
      continue-on-error: true
      run: |
        rm -rf bazel/out/testlogs/backend/test/api_integration/collection/
        make api-test ENV=${{ inputs.env }} GREY_NAME=${{ inputs.grey_name }} TAGS=${{ inputs.test_tags }} LOG=true LOG_RESPONSE=false

    # Run API integration test 可能失败，主要有两个原因
    # 1. 有测试用例运行失败
    # 2. 编译错误测试没法运行
    # 两种情况都会导致上一个 step 失败，但是没法区分
    # 可以通过 Copy Test Results 来区分，如果只是测试用例运行失败，测试报告是有的，copy 能成功
    # 如果是编译错误，测试用例没有运行，测试报告是空的，copy 就会失败
    - name: Copy Test Results
      id: copy-test-results
      shell: bash
      continue-on-error: true
      run: |
        rm -rf backend/test/api_integration/result/
        mkdir -p backend/test/api_integration/result/ && cp -Lpr bazel/out/testlogs/backend/test/api_integration/collection/* backend/test/api_integration/result/

    - name: Publish Test Results
      if: steps.copy-test-results.outcome == 'success'
      id: publish-test-results
      uses: EnricoMi/publish-unit-test-result-action/linux@v2
      with:
        check_name: "Test Results (${{ github.event.workflow_run.event || github.event_name }})"
        files: |
          backend/test/api_integration/result/**/*.xml

    - name: Echo test summary
      if: steps.publish-test-results.outcome == 'success'
      shell: bash
      run: echo "Summary is ${{ steps.publish-test-results.outputs.json }}"

    - name : Update commit status
      if: always()
      uses: myrotvorets/set-commit-status-action@master
      with:
        token: ${{ inputs.ADMIN_TOKEN_GITHUB }}
        status: ${{ steps.publish-test-results.outcome == 'success' && fromJSON( steps.publish-test-results.outputs.json ).conclusion || 'failure' }}
        description: ${{ steps.publish-test-results.outcome == 'success' && fromJSON( steps.publish-test-results.outputs.json ).conclusion || 'failure' }}
        context: API Integration Test
        repo: ${{ inputs.source_repo }}
        sha: ${{ inputs.source_sha }}
        targetUrl: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

    - name: Send test summary to a Slack channel
      uses: slackapi/slack-github-action@v2.0.0
      if: steps.publish-test-results.outcome == 'success'
      continue-on-error: true
      with:
        errors: true
        method: chat.postMessage
        token: ${{ inputs.SLACK_BOT_TOKEN }}
        payload: |
          channel: C07SXTPLB8E
          blocks:
            - type: "section"
              text:
                type: "mrkdwn"
                text: "*[${{ fromJSON( steps.publish-test-results.outputs.json ).conclusion }}] API integration testing triggered by ${{ inputs.source_repo }}*"
            - type: "divider"
            - type: "context"
              elements:
                - type: "mrkdwn"
                  text: "${{ fromJSON( steps.publish-test-results.outputs.json ).title }}\n"
            - type: "context"
              elements:
                - type: "mrkdwn"
                  text: "✅ ${{ fromJSON( steps.publish-test-results.outputs.json ).stats.tests_succ }} succeed"
                - type: "mrkdwn"
                  text: "💤 ${{ fromJSON( steps.publish-test-results.outputs.json ).stats.tests_skip }} skip"
                - type: "mrkdwn"
                  text: "❌ ${{ fromJSON( steps.publish-test-results.outputs.json ).stats.tests_fail }} fail"
                - type: "mrkdwn"
                  text: "🔥 ${{ fromJSON( steps.publish-test-results.outputs.json ).stats.tests_error }} error"
            - type: "context"
              elements:
                - type: "mrkdwn"
                  text: "env: `${{ inputs.env }}`"
                - type: "mrkdwn"
                  text: "grey: `${{ inputs.grey_name || '-' }}`"
                - type: "mrkdwn"
                  text: "def_branch: `${{ inputs.def_branch || '-' }}`"
            - type: "divider"
            - type: "actions"
              elements:
                - type: "button"
                  text:
                    type: "plain_text"
                    text: "View action page"
                  url: "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

    - name: Send failure notification to Slack
      uses: slackapi/slack-github-action@v2.0.0
      if: steps.publish-test-results.outcome != 'success'
      with:
        errors: true
        method: chat.postMessage
        token: ${{ inputs.SLACK_BOT_TOKEN }}
        payload: |
          channel: C07SXTPLB8E
          blocks:
            - type: "section"
              text:
                type: "mrkdwn"
                text: "*[Error] 🔥 API integration testing triggered by ${{ inputs.source_repo }}*"
            - type: "divider"
            - type: "context"
              elements:
                - type: "mrkdwn"
                  text: "Test failed, possibly due to a build error or environment issue. Please check the logs for details. <@U03S904FJF9>"
            - type: "context"
              elements:
                - type: "mrkdwn"
                  text: "env: `${{ inputs.env }}`"
                - type: "mrkdwn"
                  text: "grey: `${{ inputs.grey_name || '-' }}`"
                - type: "mrkdwn"
                  text: "def_branch: `${{ inputs.def_branch || '-' }}`"
            - type: "divider"
            - type: "actions"
              elements:
                - type: "button"
                  text:
                    type: "plain_text"
                    text: "View action page"
                  url: "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

    - name: If test failed, exit with error
      if: failure() || steps.run-test.outcome == 'failure'
      shell: bash
      run: exit 1